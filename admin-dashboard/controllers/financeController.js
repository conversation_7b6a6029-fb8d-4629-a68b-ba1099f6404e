const memoryStore = require('../models/memoryStore');

/**
 * 财务管理控制器
 * 处理财务统计、交易记录、退款管理、财务报表、消费分析等功能
 */

// 获取财务概览
exports.getFinanceOverview = async (req, res) => {
    try {
        const { startDate, endDate } = req.query;
        
        // 获取交易记录
        const transactions = await memoryStore.findDocuments('transactions');
        const refunds = await memoryStore.findDocuments('refunds');
        
        // 计算时间范围
        const now = new Date();
        const start = startDate ? new Date(startDate) : new Date(now.getFullYear(), now.getMonth(), 1);
        const end = endDate ? new Date(endDate) : now;
        const lastMonthStart = new Date(start.getFullYear(), start.getMonth() - 1, 1);
        const lastMonthEnd = new Date(start.getFullYear(), start.getMonth(), 0);
        
        // 筛选当期交易
        const currentTransactions = transactions.filter(t => {
            const date = new Date(t.createdAt);
            return date >= start && date <= end && t.status === 'completed';
        });
        
        // 筛选上期交易
        const lastMonthTransactions = transactions.filter(t => {
            const date = new Date(t.createdAt);
            return date >= lastMonthStart && date <= lastMonthEnd && t.status === 'completed';
        });
        
        // 计算收入
        const totalRevenue = currentTransactions.reduce((sum, t) => sum + t.amount, 0);
        const lastMonthRevenue = lastMonthTransactions.reduce((sum, t) => sum + t.amount, 0);
        const revenueGrowth = lastMonthRevenue > 0 ? ((totalRevenue - lastMonthRevenue) / lastMonthRevenue * 100).toFixed(1) : 0;
        
        // 计算支出（佣金、退款等）
        const currentRefunds = refunds.filter(r => {
            const date = new Date(r.createdAt);
            return date >= start && date <= end && r.status === 'completed';
        });
        const totalExpense = currentRefunds.reduce((sum, r) => sum + r.amount, 0);
        
        // 计算净利润
        const netProfit = totalRevenue - totalExpense;
        
        // 计算付费转化率
        const totalUsers = await memoryStore.findDocuments('users');
        const payingUsers = [...new Set(currentTransactions.map(t => t.userId))];
        const conversionRate = totalUsers.length > 0 ? (payingUsers.length / totalUsers.length * 100).toFixed(1) : 0;
        
        // 今日统计
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayTransactions = transactions.filter(t => {
            const date = new Date(t.createdAt);
            return date >= today && t.status === 'completed';
        });
        const todayRevenue = todayTransactions.reduce((sum, t) => sum + t.amount, 0);
        
        res.json({
            success: true,
            data: {
                totalRevenue,
                totalExpense,
                netProfit,
                conversionRate: parseFloat(conversionRate),
                revenueGrowth: parseFloat(revenueGrowth),
                orderCount: currentTransactions.length,
                todayRevenue,
                todayOrders: todayTransactions.length,
                monthRevenue: totalRevenue,
                monthOrders: currentTransactions.length
            }
        });
        
    } catch (error) {
        console.error('获取财务概览错误:', error);
        res.status(500).json({
            success: false,
            message: '获取财务概览失败'
        });
    }
};

// 获取交易记录
exports.getTransactions = async (req, res) => {
    try {
        const { page = 1, limit = 20, search, status, type, startDate, endDate } = req.query;
        
        let transactions = await memoryStore.findDocuments('transactions');
        
        // 搜索过滤
        if (search) {
            transactions = transactions.filter(t => 
                t.orderId.toLowerCase().includes(search.toLowerCase()) ||
                t.userId.toLowerCase().includes(search.toLowerCase())
            );
        }
        
        // 状态过滤
        if (status) {
            transactions = transactions.filter(t => t.status === status);
        }
        
        // 类型过滤
        if (type) {
            transactions = transactions.filter(t => t.type === type);
        }
        
        // 时间过滤
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            transactions = transactions.filter(t => {
                const date = new Date(t.createdAt);
                return date >= start && date <= end;
            });
        }
        
        // 排序
        transactions.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedTransactions = transactions.slice(startIndex, endIndex);
        
        res.json({
            success: true,
            data: paginatedTransactions,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: transactions.length,
                pages: Math.ceil(transactions.length / limit)
            }
        });
        
    } catch (error) {
        console.error('获取交易记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取交易记录失败'
        });
    }
};

// 获取退款记录
exports.getRefunds = async (req, res) => {
    try {
        const { page = 1, limit = 20, status } = req.query;
        
        let refunds = await memoryStore.findDocuments('refunds');
        
        // 状态过滤
        if (status) {
            refunds = refunds.filter(r => r.status === status);
        }
        
        // 排序
        refunds.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedRefunds = refunds.slice(startIndex, endIndex);
        
        // 计算统计信息
        const pendingRefunds = refunds.filter(r => r.status === 'pending').length;
        const totalRefundAmount = refunds
            .filter(r => r.status === 'completed')
            .reduce((sum, r) => sum + r.amount, 0);
        
        // 计算退款率
        const transactions = await memoryStore.findDocuments('transactions');
        const completedTransactions = transactions.filter(t => t.status === 'completed');
        const refundRate = completedTransactions.length > 0 
            ? (refunds.filter(r => r.status === 'completed').length / completedTransactions.length * 100).toFixed(1)
            : 0;
        
        res.json({
            success: true,
            data: paginatedRefunds,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: refunds.length,
                pages: Math.ceil(refunds.length / limit)
            },
            stats: {
                pendingRefunds,
                totalRefundAmount,
                refundRate: parseFloat(refundRate),
                avgRefundTime: 2 // 模拟平均处理时间
            }
        });
        
    } catch (error) {
        console.error('获取退款记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取退款记录失败'
        });
    }
};

// 处理退款申请
exports.processRefund = async (req, res) => {
    try {
        const { id } = req.params;
        const { action, reason } = req.body; // action: 'approve' | 'reject'
        
        const refund = await memoryStore.findOneDocument('refunds', { _id: id });
        if (!refund) {
            return res.status(404).json({
                success: false,
                message: '退款申请不存在'
            });
        }
        
        const updateData = {
            status: action === 'approve' ? 'approved' : 'rejected',
            processedAt: new Date(),
            processReason: reason,
            updatedAt: new Date()
        };
        
        await memoryStore.updateDocument('refunds', id, updateData);
        
        res.json({
            success: true,
            message: action === 'approve' ? '退款申请已批准' : '退款申请已拒绝'
        });
        
    } catch (error) {
        console.error('处理退款申请错误:', error);
        res.status(500).json({
            success: false,
            message: '处理退款申请失败'
        });
    }
};

// 获取消费分析数据
exports.getConsumptionAnalysis = async (req, res) => {
    try {
        const transactions = await memoryStore.findDocuments('transactions');
        const users = await memoryStore.findDocuments('users');
        
        // 计算平均订单价值
        const completedTransactions = transactions.filter(t => t.status === 'completed');
        const avgOrderValue = completedTransactions.length > 0 
            ? completedTransactions.reduce((sum, t) => sum + t.amount, 0) / completedTransactions.length
            : 0;
        
        // 计算客户生命周期价值
        const userTransactions = {};
        completedTransactions.forEach(t => {
            if (!userTransactions[t.userId]) {
                userTransactions[t.userId] = [];
            }
            userTransactions[t.userId].push(t);
        });
        
        const customerLifetimeValues = Object.values(userTransactions).map(userTxns => 
            userTxns.reduce((sum, t) => sum + t.amount, 0)
        );
        const avgCustomerLifetimeValue = customerLifetimeValues.length > 0
            ? customerLifetimeValues.reduce((sum, val) => sum + val, 0) / customerLifetimeValues.length
            : 0;
        
        // 计算复购率
        const repeatCustomers = Object.values(userTransactions).filter(userTxns => userTxns.length > 1).length;
        const repeatPurchaseRate = Object.keys(userTransactions).length > 0
            ? (repeatCustomers / Object.keys(userTransactions).length * 100).toFixed(1)
            : 0;
        
        // 模拟其他数据
        const consumptionHabits = [
            { name: '订阅用户', value: 65 },
            { name: '一次性购买', value: 25 },
            { name: '升级用户', value: 10 }
        ];
        
        const productRevenue = [
            { name: '基础版', value: 45 },
            { name: '专业版', value: 35 },
            { name: '企业版', value: 20 }
        ];
        
        const regionConsumption = [
            { name: '华东', value: 35 },
            { name: '华北', value: 25 },
            { name: '华南', value: 20 },
            { name: '西南', value: 12 },
            { name: '其他', value: 8 }
        ];
        
        res.json({
            success: true,
            data: {
                avgOrderValue: Math.round(avgOrderValue),
                customerLifetimeValue: Math.round(avgCustomerLifetimeValue),
                repeatPurchaseRate: parseFloat(repeatPurchaseRate),
                churnRate: 15.2, // 模拟流失率
                consumptionHabits,
                productRevenue,
                regionConsumption,
                conversionFunnel: [
                    { name: '访问用户', value: 10000 },
                    { name: '注册用户', value: 3000 },
                    { name: '试用用户', value: 1200 },
                    { name: '付费用户', value: 480 }
                ]
            }
        });
        
    } catch (error) {
        console.error('获取消费分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取消费分析失败'
        });
    }
};

// 获取退款列表
exports.getRefunds = async (req, res) => {
    try {
        const { page = 1, limit = 20, status } = req.query;

        let refunds = await memoryStore.findDocuments('refunds');

        // 状态过滤
        if (status) {
            refunds = refunds.filter(r => r.status === status);
        }

        // 排序
        refunds.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedRefunds = refunds.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: paginatedRefunds,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: refunds.length,
                pages: Math.ceil(refunds.length / limit)
            }
        });

    } catch (error) {
        console.error('获取退款列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取退款列表失败'
        });
    }
};

// 处理退款申请
exports.processRefund = async (req, res) => {
    try {
        const { id } = req.params;
        const { action, reason } = req.body; // action: 'approve' | 'reject'

        const refund = await memoryStore.findOneDocument('refunds', { _id: id });
        if (!refund) {
            return res.status(404).json({
                success: false,
                message: '退款申请不存在'
            });
        }

        const updateData = {
            status: action === 'approve' ? 'completed' : 'rejected',
            processedBy: req.user?.id || 'admin',
            processedAt: new Date(),
            processReason: reason,
            updatedAt: new Date()
        };

        await memoryStore.updateDocument('refunds', id, updateData);

        res.json({
            success: true,
            message: action === 'approve' ? '退款已批准' : '退款已拒绝'
        });

    } catch (error) {
        console.error('处理退款错误:', error);
        res.status(500).json({
            success: false,
            message: '处理退款失败'
        });
    }
};

// 获取财务报表
exports.getFinanceReports = async (req, res) => {
    try {
        const { type = 'monthly', year = new Date().getFullYear() } = req.query;

        const transactions = await memoryStore.findDocuments('transactions');
        const orders = await memoryStore.findDocuments('orders');

        let reportData = [];

        if (type === 'monthly') {
            // 月度报表
            for (let month = 0; month < 12; month++) {
                const monthTransactions = transactions.filter(t => {
                    const date = new Date(t.createdAt);
                    return date.getFullYear() === parseInt(year) && date.getMonth() === month;
                });

                const revenue = monthTransactions
                    .filter(t => t.type === 'income' && t.status === 'completed')
                    .reduce((sum, t) => sum + t.amount, 0);

                const expense = monthTransactions
                    .filter(t => t.type === 'expense' && t.status === 'completed')
                    .reduce((sum, t) => sum + t.amount, 0);

                const monthOrders = orders.filter(o => {
                    const date = new Date(o.createdAt);
                    return date.getFullYear() === parseInt(year) && date.getMonth() === month;
                }).length;

                reportData.push({
                    period: `${year}-${(month + 1).toString().padStart(2, '0')}`,
                    revenue,
                    expense,
                    profit: revenue - expense,
                    orders: monthOrders
                });
            }
        }

        res.json({
            success: true,
            data: reportData
        });

    } catch (error) {
        console.error('获取财务报表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取财务报表失败'
        });
    }
};

module.exports = exports;
