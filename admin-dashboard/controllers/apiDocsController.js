/**
 * API文档控制器
 * 自动生成和管理API文档
 */

const fs = require('fs').promises;
const path = require('path');

// API文档数据
const apiDocumentation = {
    info: {
        title: 'WriterPro管理后台API',
        version: '1.0.0',
        description: 'WriterPro管理后台的完整API文档',
        baseUrl: 'https://api.writerpro.com/v1',
        contact: {
            name: 'API支持',
            email: '<EMAIL>'
        }
    },
    authentication: {
        type: 'Bearer Token',
        description: '使用JWT令牌进行身份认证',
        header: 'Authorization: Bearer <token>'
    },
    endpoints: {
        auth: {
            name: '认证管理',
            description: '用户认证相关接口',
            endpoints: [
                {
                    method: 'POST',
                    path: '/auth/login',
                    summary: '用户登录',
                    description: '使用邮箱和密码进行登录认证',
                    parameters: [
                        { name: 'email', type: 'string', required: true, description: '用户邮箱' },
                        { name: 'password', type: 'string', required: true, description: '用户密码' }
                    ],
                    responses: {
                        200: {
                            description: '登录成功',
                            example: {
                                success: true,
                                data: {
                                    token: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
                                    user: {
                                        id: 'user_123',
                                        email: '<EMAIL>',
                                        role: 'admin'
                                    },
                                    expiresIn: 3600
                                }
                            }
                        },
                        401: {
                            description: '认证失败',
                            example: {
                                success: false,
                                message: '邮箱或密码错误'
                            }
                        }
                    }
                },
                {
                    method: 'POST',
                    path: '/auth/logout',
                    summary: '用户登出',
                    description: '注销当前用户会话',
                    requiresAuth: true,
                    responses: {
                        200: {
                            description: '登出成功',
                            example: {
                                success: true,
                                message: '登出成功'
                            }
                        }
                    }
                }
            ]
        },
        users: {
            name: '用户管理',
            description: '用户管理相关接口',
            endpoints: [
                {
                    method: 'GET',
                    path: '/users',
                    summary: '获取用户列表',
                    description: '分页获取用户列表，支持搜索和筛选',
                    requiresAuth: true,
                    parameters: [
                        { name: 'page', type: 'integer', required: false, description: '页码，默认1' },
                        { name: 'limit', type: 'integer', required: false, description: '每页数量，默认20' },
                        { name: 'search', type: 'string', required: false, description: '搜索关键词' },
                        { name: 'status', type: 'string', required: false, description: '用户状态：active, inactive' }
                    ],
                    responses: {
                        200: {
                            description: '获取成功',
                            example: {
                                success: true,
                                data: {
                                    users: [
                                        {
                                            id: 'user_123',
                                            username: 'john_doe',
                                            email: '<EMAIL>',
                                            status: 'active',
                                            createdAt: '2024-01-01T00:00:00Z'
                                        }
                                    ],
                                    pagination: {
                                        page: 1,
                                        limit: 20,
                                        total: 100,
                                        pages: 5
                                    }
                                }
                            }
                        }
                    }
                },
                {
                    method: 'POST',
                    path: '/users',
                    summary: '创建用户',
                    description: '创建新用户账户',
                    requiresAuth: true,
                    parameters: [
                        { name: 'username', type: 'string', required: true, description: '用户名' },
                        { name: 'email', type: 'string', required: true, description: '邮箱地址' },
                        { name: 'password', type: 'string', required: true, description: '密码' },
                        { name: 'role', type: 'string', required: false, description: '用户角色' }
                    ],
                    responses: {
                        201: {
                            description: '创建成功',
                            example: {
                                success: true,
                                data: {
                                    id: 'user_124',
                                    username: 'new_user',
                                    email: '<EMAIL>',
                                    status: 'active'
                                }
                            }
                        }
                    }
                }
            ]
        },
        agents: {
            name: '代理商管理',
            description: '代理商管理相关接口',
            endpoints: [
                {
                    method: 'GET',
                    path: '/agents',
                    summary: '获取代理商列表',
                    requiresAuth: true,
                    parameters: [
                        { name: 'page', type: 'integer', required: false, description: '页码' },
                        { name: 'limit', type: 'integer', required: false, description: '每页数量' },
                        { name: 'level', type: 'integer', required: false, description: '代理商等级' },
                        { name: 'status', type: 'string', required: false, description: '状态' }
                    ]
                }
            ]
        },
        commission: {
            name: '佣金管理',
            description: '佣金管理相关接口',
            endpoints: [
                {
                    method: 'GET',
                    path: '/commission/records',
                    summary: '获取佣金记录',
                    requiresAuth: true,
                    parameters: [
                        { name: 'agentId', type: 'string', required: false, description: '代理商ID' },
                        { name: 'status', type: 'string', required: false, description: '状态' }
                    ]
                },
                {
                    method: 'POST',
                    path: '/commission/calculate',
                    summary: '计算订单佣金',
                    requiresAuth: true,
                    parameters: [
                        { name: 'orderId', type: 'string', required: true, description: '订单ID' },
                        { name: 'agentId', type: 'string', required: true, description: '代理商ID' },
                        { name: 'orderAmount', type: 'number', required: true, description: '订单金额' }
                    ]
                }
            ]
        },
        pricing: {
            name: '价格管理',
            description: '价格管理相关接口',
            endpoints: [
                {
                    method: 'GET',
                    path: '/pricing/products',
                    summary: '获取产品价格列表',
                    requiresAuth: true
                },
                {
                    method: 'POST',
                    path: '/pricing/calculate',
                    summary: '计算产品价格',
                    requiresAuth: true,
                    parameters: [
                        { name: 'productId', type: 'string', required: true, description: '产品ID' },
                        { name: 'quantity', type: 'integer', required: false, description: '数量' },
                        { name: 'memberLevel', type: 'integer', required: false, description: '会员等级' }
                    ]
                }
            ]
        }
    },
    errorCodes: {
        'INVALID_TOKEN': { code: 401, message: '无效的访问令牌' },
        'INSUFFICIENT_PERMISSIONS': { code: 403, message: '权限不足' },
        'RESOURCE_NOT_FOUND': { code: 404, message: '资源不存在' },
        'VALIDATION_ERROR': { code: 400, message: '参数验证失败' },
        'RATE_LIMIT_EXCEEDED': { code: 429, message: '请求频率超限' },
        'INTERNAL_SERVER_ERROR': { code: 500, message: '服务器内部错误' }
    }
};

// 获取完整API文档
exports.getApiDocumentation = async (req, res) => {
    try {
        res.json({
            success: true,
            data: apiDocumentation
        });
    } catch (error) {
        console.error('获取API文档失败:', error);
        res.status(500).json({
            success: false,
            message: '获取API文档失败'
        });
    }
};

// 获取OpenAPI规范文档
exports.getOpenApiSpec = async (req, res) => {
    try {
        const openApiSpec = generateOpenApiSpec(apiDocumentation);
        res.json(openApiSpec);
    } catch (error) {
        console.error('生成OpenAPI规范失败:', error);
        res.status(500).json({
            success: false,
            message: '生成OpenAPI规范失败'
        });
    }
};

// 获取API统计信息
exports.getApiStats = async (req, res) => {
    try {
        const stats = {
            totalEndpoints: calculateTotalEndpoints(apiDocumentation),
            categories: Object.keys(apiDocumentation.endpoints).length,
            lastUpdated: new Date().toISOString(),
            version: apiDocumentation.info.version,
            authRequired: calculateAuthRequiredEndpoints(apiDocumentation)
        };
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取API统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取API统计失败'
        });
    }
};

// 生成Postman集合
exports.generatePostmanCollection = async (req, res) => {
    try {
        const collection = generatePostmanCollection(apiDocumentation);
        
        res.setHeader('Content-Type', 'application/json');
        res.setHeader('Content-Disposition', 'attachment; filename="WriterPro-API.postman_collection.json"');
        res.json(collection);
    } catch (error) {
        console.error('生成Postman集合失败:', error);
        res.status(500).json({
            success: false,
            message: '生成Postman集合失败'
        });
    }
};

// 测试API端点
exports.testApiEndpoint = async (req, res) => {
    try {
        const { method, path, parameters, headers } = req.body;
        
        // 这里可以实现实际的API测试逻辑
        // 模拟测试结果
        const testResult = {
            success: true,
            method,
            path,
            statusCode: 200,
            responseTime: Math.floor(Math.random() * 500) + 100,
            response: {
                success: true,
                message: 'API测试成功',
                data: {}
            }
        };
        
        res.json({
            success: true,
            data: testResult
        });
    } catch (error) {
        console.error('API测试失败:', error);
        res.status(500).json({
            success: false,
            message: 'API测试失败'
        });
    }
};

// 辅助函数
function generateOpenApiSpec(docs) {
    const spec = {
        openapi: '3.0.0',
        info: docs.info,
        servers: [
            { url: docs.info.baseUrl, description: '生产环境' }
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT'
                }
            }
        },
        paths: {}
    };
    
    // 转换端点到OpenAPI格式
    Object.values(docs.endpoints).forEach(category => {
        category.endpoints.forEach(endpoint => {
            if (!spec.paths[endpoint.path]) {
                spec.paths[endpoint.path] = {};
            }
            
            spec.paths[endpoint.path][endpoint.method.toLowerCase()] = {
                summary: endpoint.summary,
                description: endpoint.description,
                parameters: endpoint.parameters?.map(param => ({
                    name: param.name,
                    in: param.type === 'string' ? 'query' : 'query',
                    required: param.required,
                    description: param.description,
                    schema: { type: param.type }
                })),
                responses: endpoint.responses || {
                    200: { description: '成功' }
                }
            };
            
            if (endpoint.requiresAuth) {
                spec.paths[endpoint.path][endpoint.method.toLowerCase()].security = [
                    { bearerAuth: [] }
                ];
            }
        });
    });
    
    return spec;
}

function generatePostmanCollection(docs) {
    const collection = {
        info: {
            name: docs.info.title,
            description: docs.info.description,
            version: docs.info.version,
            schema: 'https://schema.getpostman.com/json/collection/v2.1.0/collection.json'
        },
        auth: {
            type: 'bearer',
            bearer: [
                {
                    key: 'token',
                    value: '{{access_token}}',
                    type: 'string'
                }
            ]
        },
        variable: [
            {
                key: 'base_url',
                value: docs.info.baseUrl,
                type: 'string'
            }
        ],
        item: []
    };
    
    // 转换端点到Postman格式
    Object.entries(docs.endpoints).forEach(([categoryKey, category]) => {
        const folder = {
            name: category.name,
            description: category.description,
            item: []
        };
        
        category.endpoints.forEach(endpoint => {
            const item = {
                name: endpoint.summary,
                request: {
                    method: endpoint.method,
                    header: [],
                    url: {
                        raw: '{{base_url}}' + endpoint.path,
                        host: ['{{base_url}}'],
                        path: endpoint.path.split('/').filter(p => p)
                    }
                }
            };
            
            if (endpoint.requiresAuth) {
                item.request.auth = {
                    type: 'bearer',
                    bearer: [
                        {
                            key: 'token',
                            value: '{{access_token}}',
                            type: 'string'
                        }
                    ]
                };
            }
            
            if (endpoint.parameters && endpoint.method !== 'GET') {
                item.request.body = {
                    mode: 'raw',
                    raw: JSON.stringify(
                        endpoint.parameters.reduce((obj, param) => {
                            obj[param.name] = param.type === 'string' ? '' : 
                                            param.type === 'integer' ? 0 : null;
                            return obj;
                        }, {}),
                        null,
                        2
                    ),
                    options: {
                        raw: {
                            language: 'json'
                        }
                    }
                };
            }
            
            folder.item.push(item);
        });
        
        collection.item.push(folder);
    });
    
    return collection;
}

function calculateTotalEndpoints(docs) {
    return Object.values(docs.endpoints).reduce((total, category) => {
        return total + category.endpoints.length;
    }, 0);
}

function calculateAuthRequiredEndpoints(docs) {
    return Object.values(docs.endpoints).reduce((total, category) => {
        return total + category.endpoints.filter(endpoint => endpoint.requiresAuth).length;
    }, 0);
}

module.exports = exports;
