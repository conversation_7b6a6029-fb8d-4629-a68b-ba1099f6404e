const memoryStore = require('../models/memoryStore');

/**
 * 系统设置控制器
 * 处理基本设置、价格设置、系统参数、支付设置、通知设置、操作日志等功能
 */

// 获取价格设置
exports.getPricingSettings = async (req, res) => {
    try {
        const pricingPlans = await memoryStore.findDocuments('pricingPlans');
        
        res.json({
            success: true,
            data: pricingPlans
        });
        
    } catch (error) {
        console.error('获取价格设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取价格设置失败'
        });
    }
};

// 创建价格方案
exports.createPricingPlan = async (req, res) => {
    try {
        const planData = {
            ...req.body,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const plan = await memoryStore.createDocument('pricingPlans', planData);
        
        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'create_pricing', `创建价格方案: ${planData.productName}`, req.ip);
        
        res.json({
            success: true,
            data: plan,
            message: '价格方案创建成功'
        });
        
    } catch (error) {
        console.error('创建价格方案错误:', error);
        res.status(500).json({
            success: false,
            message: '创建价格方案失败'
        });
    }
};

// 更新价格方案
exports.updatePricingPlan = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };
        
        const plan = await memoryStore.updateDocument('pricingPlans', id, updateData);
        
        if (!plan) {
            return res.status(404).json({
                success: false,
                message: '价格方案不存在'
            });
        }
        
        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'update_pricing', `更新价格方案: ${plan.productName}`, req.ip);
        
        res.json({
            success: true,
            data: plan,
            message: '价格方案更新成功'
        });
        
    } catch (error) {
        console.error('更新价格方案错误:', error);
        res.status(500).json({
            success: false,
            message: '更新价格方案失败'
        });
    }
};

// 批量调整价格
exports.batchAdjustPricing = async (req, res) => {
    try {
        const { adjustmentType, adjustmentValue, scope } = req.body;
        
        let filter = {};
        if (scope !== 'all') {
            filter.category = scope;
        }
        
        const plans = await memoryStore.findDocuments('pricingPlans', filter);
        
        for (const plan of plans) {
            let newPrice = plan.originalPrice;
            
            if (adjustmentType === 'percentage') {
                newPrice = plan.originalPrice * (1 + adjustmentValue / 100);
            } else if (adjustmentType === 'fixed') {
                newPrice = plan.originalPrice + adjustmentValue;
            }
            
            await memoryStore.updateDocument('pricingPlans', plan._id, {
                originalPrice: Math.max(0, newPrice),
                updatedAt: new Date()
            });
        }
        
        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'batch_adjust_pricing', 
            `批量调整价格: ${adjustmentType} ${adjustmentValue}`, req.ip);
        
        res.json({
            success: true,
            message: `成功调整 ${plans.length} 个价格方案`
        });
        
    } catch (error) {
        console.error('批量调整价格错误:', error);
        res.status(500).json({
            success: false,
            message: '批量调整价格失败'
        });
    }
};

// 获取系统配置
exports.getSystemConfig = async (req, res) => {
    try {
        let config = await memoryStore.findOneDocument('settings', { type: 'system' });
        
        if (!config) {
            // 创建默认系统配置
            config = await memoryStore.createDocument('settings', {
                type: 'system',
                data: {
                    systemName: 'WriterPro',
                    systemVersion: '1.0.0',
                    systemDescription: '智能写作助手平台',
                    contactEmail: '<EMAIL>',
                    contactPhone: '************',
                    apiRateLimit: 100,
                    registerRateLimit: 10,
                    loginRateLimit: 5,
                    cacheExpiry: 30,
                    enableRedis: true,
                    enableMemoryCache: true
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: config.data
        });
        
    } catch (error) {
        console.error('获取系统配置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取系统配置失败'
        });
    }
};

// 更新系统配置
exports.updateSystemConfig = async (req, res) => {
    try {
        const { configType } = req.params; // basic, rateLimit, cache
        
        let config = await memoryStore.findOneDocument('settings', { type: 'system' });
        
        if (config) {
            // 更新指定类型的配置
            const updatedData = { ...config.data, ...req.body };
            config = await memoryStore.updateDocument('settings', config._id, {
                data: updatedData,
                updatedAt: new Date()
            });
        } else {
            config = await memoryStore.createDocument('settings', {
                type: 'system',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'update_system_config', 
            `更新系统配置: ${configType}`, req.ip);
        
        res.json({
            success: true,
            data: config.data,
            message: '系统配置更新成功'
        });
        
    } catch (error) {
        console.error('更新系统配置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新系统配置失败'
        });
    }
};

// 获取操作日志
exports.getOperationLogs = async (req, res) => {
    try {
        const { page = 1, limit = 20, search, level, module, startDate, endDate } = req.query;
        
        let logs = await memoryStore.findDocuments('operationLogs');
        
        // 搜索过滤
        if (search) {
            logs = logs.filter(log => 
                log.username?.toLowerCase().includes(search.toLowerCase()) ||
                log.operation?.toLowerCase().includes(search.toLowerCase()) ||
                log.details?.toLowerCase().includes(search.toLowerCase())
            );
        }
        
        // 级别过滤
        if (level) {
            logs = logs.filter(log => log.level === level);
        }
        
        // 模块过滤
        if (module) {
            logs = logs.filter(log => log.module === module);
        }
        
        // 时间过滤
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            logs = logs.filter(log => {
                const date = new Date(log.timestamp);
                return date >= start && date <= end;
            });
        }
        
        // 排序
        logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedLogs = logs.slice(startIndex, endIndex);
        
        // 计算统计信息
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayLogs = logs.filter(log => new Date(log.timestamp) >= today).length;
        const errorLogs = logs.filter(log => log.level === 'error').length;
        const warningLogs = logs.filter(log => log.level === 'warning').length;
        
        res.json({
            success: true,
            data: paginatedLogs,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: logs.length,
                pages: Math.ceil(logs.length / limit)
            },
            stats: {
                totalLogs: logs.length,
                todayLogs,
                errorLogs,
                warningLogs
            }
        });
        
    } catch (error) {
        console.error('获取操作日志错误:', error);
        res.status(500).json({
            success: false,
            message: '获取操作日志失败'
        });
    }
};

// 清理操作日志
exports.clearOperationLogs = async (req, res) => {
    try {
        const { days = 30 } = req.body;
        
        const cutoffDate = new Date();
        cutoffDate.setDate(cutoffDate.getDate() - days);
        
        const logs = await memoryStore.findDocuments('operationLogs');
        const logsToDelete = logs.filter(log => new Date(log.timestamp) < cutoffDate);
        
        for (const log of logsToDelete) {
            await memoryStore.deleteDocument('operationLogs', log._id);
        }
        
        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'clear_logs', 
            `清理 ${days} 天前的操作日志，共删除 ${logsToDelete.length} 条`, req.ip);
        
        res.json({
            success: true,
            message: `成功清理 ${logsToDelete.length} 条日志`
        });
        
    } catch (error) {
        console.error('清理操作日志错误:', error);
        res.status(500).json({
            success: false,
            message: '清理操作日志失败'
        });
    }
};

// 记录操作日志的辅助函数
async function logOperation(userId, module, operation, details, ip, level = 'info') {
    try {
        const user = userId ? await memoryStore.findOneDocument('users', { _id: userId }) : null;
        
        const logData = {
            userId: userId || null,
            username: user?.username || 'System',
            module,
            operation,
            details,
            level,
            ip: ip || '127.0.0.1',
            timestamp: new Date()
        };
        
        await memoryStore.createDocument('operationLogs', logData);
    } catch (error) {
        console.error('记录操作日志错误:', error);
    }
}

// 获取基本设置
exports.getBasicSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'basic' });

        if (!settings) {
            // 创建默认基本设置
            settings = await memoryStore.createDocument('settings', {
                type: 'basic',
                data: {
                    siteName: 'WriterPro管理后台',
                    siteDescription: '智能写作助手管理系统',
                    contactEmail: '<EMAIL>',
                    contactPhone: '************',
                    companyAddress: '北京市朝阳区科技园区',
                    icp: '京ICP备12345678号',
                    copyright: '© 2025 WriterPro. All rights reserved.',
                    timezone: 'Asia/Shanghai',
                    language: 'zh-CN',
                    maintenanceMode: false
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data
        });

    } catch (error) {
        console.error('获取基本设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取基本设置失败'
        });
    }
};

// 更新基本设置
exports.updateBasicSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'basic' });

        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'basic',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'update_basic', '更新基本设置', req.ip);

        res.json({
            success: true,
            data: settings.data,
            message: '基本设置更新成功'
        });

    } catch (error) {
        console.error('更新基本设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新基本设置失败'
        });
    }
};

// 获取系统参数
exports.getSystemParameters = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'system' });

        if (!settings) {
            // 创建默认系统参数
            settings = await memoryStore.createDocument('settings', {
                type: 'system',
                data: {
                    maxFileSize: 10, // MB
                    allowedFileTypes: ['pdf', 'doc', 'docx', 'txt'],
                    sessionTimeout: 30, // 分钟
                    maxLoginAttempts: 5,
                    passwordMinLength: 8,
                    enableTwoFactor: false,
                    apiRateLimit: 1000, // 每小时
                    enableCache: true,
                    cacheExpiry: 3600, // 秒
                    enableLogging: true,
                    logLevel: 'info',
                    backupFrequency: 'daily',
                    enableMaintenance: false
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data
        });

    } catch (error) {
        console.error('获取系统参数错误:', error);
        res.status(500).json({
            success: false,
            message: '获取系统参数失败'
        });
    }
};

// 更新系统参数
exports.updateSystemParameters = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'system' });

        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'system',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'update_system', '更新系统参数', req.ip);

        res.json({
            success: true,
            data: settings.data,
            message: '系统参数更新成功'
        });

    } catch (error) {
        console.error('更新系统参数错误:', error);
        res.status(500).json({
            success: false,
            message: '更新系统参数失败'
        });
    }
};

// 获取通知设置
exports.getNotificationSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'notification' });

        if (!settings) {
            // 创建默认通知设置
            settings = await memoryStore.createDocument('settings', {
                type: 'notification',
                data: {
                    emailNotifications: {
                        enabled: true,
                        smtpHost: 'smtp.example.com',
                        smtpPort: 587,
                        smtpUser: '<EMAIL>',
                        smtpPassword: '***',
                        fromName: 'WriterPro系统',
                        fromEmail: '<EMAIL>'
                    },
                    smsNotifications: {
                        enabled: false,
                        provider: 'aliyun',
                        accessKey: '***',
                        secretKey: '***',
                        signName: 'WriterPro'
                    },
                    pushNotifications: {
                        enabled: true,
                        provider: 'firebase',
                        serverKey: '***',
                        senderId: '123456789'
                    },
                    notificationTypes: {
                        userRegistration: true,
                        orderCreated: true,
                        paymentSuccess: true,
                        refundRequest: true,
                        systemAlert: true
                    }
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data
        });

    } catch (error) {
        console.error('获取通知设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取通知设置失败'
        });
    }
};

// 更新通知设置
exports.updateNotificationSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'notification' });

        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'notification',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        // 记录操作日志
        await logOperation(req.user?.id, 'settings', 'update_notification', '更新通知设置', req.ip);

        res.json({
            success: true,
            data: settings.data,
            message: '通知设置更新成功'
        });

    } catch (error) {
        console.error('更新通知设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新通知设置失败'
        });
    }
};

module.exports = exports;
