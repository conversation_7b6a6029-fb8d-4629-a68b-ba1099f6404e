const Coupon = require('../models/Coupon');

/**
 * 获取所有优惠券
 */
exports.getAllCoupons = async (req, res) => {
    try {
        // 分页参数
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        
        // 筛选参数
        const filter = {};
        if (req.query.type) filter.type = req.query.type;
        if (req.query.status) filter.status = req.query.status;
        if (req.query.search) {
            filter.$or = [
                { code: { $regex: req.query.search, $options: 'i' } },
                { name: { $regex: req.query.search, $options: 'i' } }
            ];
        }
        
        // 获取优惠券总数
        const total = await Coupon.countDocuments(filter);
        
        // 获取优惠券列表
        const coupons = await Coupon.find(filter)
            .populate('createdBy', 'username name')
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });
        
        return res.status(200).json({
            success: true,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            coupons
        });
    } catch (error) {
        console.error('获取优惠券列表错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 根据ID获取优惠券
 */
exports.getCouponById = async (req, res) => {
    try {
        const coupon = await Coupon.findById(req.params.id)
            .populate('createdBy', 'username name');
        
        if (!coupon) {
            return res.status(404).json({ success: false, message: '优惠券不存在' });
        }
        
        return res.status(200).json({
            success: true,
            coupon
        });
    } catch (error) {
        console.error('获取优惠券详情错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 创建优惠券
 */
exports.createCoupon = async (req, res) => {
    try {
        const { code, name, type, value, startDate, endDate, totalAmount, description, limitType } = req.body;
        
        // 验证必填字段
        if (!code || !name || !type || value === undefined || !startDate || !endDate) {
            return res.status(400).json({ success: false, message: '优惠券代码、名称、类型、面值、开始日期和结束日期为必填项' });
        }
        
        // 检查优惠券代码是否已存在
        const existingCoupon = await Coupon.findOne({ code: code.toUpperCase() });
        if (existingCoupon) {
            return res.status(400).json({ success: false, message: '优惠券代码已存在' });
        }
        
        // 创建新优惠券
        const newCoupon = new Coupon({
            code: code.toUpperCase(),
            name,
            type,
            value,
            startDate,
            endDate,
            totalAmount: totalAmount || 100,
            description: description || '',
            limitType: limitType || 'none',
            createdBy: req.session.user.id
        });
        
        await newCoupon.save();
        
        return res.status(201).json({
            success: true,
            message: '优惠券创建成功',
            coupon: newCoupon
        });
    } catch (error) {
        console.error('创建优惠券错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 更新优惠券
 */
exports.updateCoupon = async (req, res) => {
    try {
        const { name, type, value, startDate, endDate, totalAmount, description, limitType, status } = req.body;
        
        // 查找优惠券
        const coupon = await Coupon.findById(req.params.id);
        if (!coupon) {
            return res.status(404).json({ success: false, message: '优惠券不存在' });
        }
        
        // 更新优惠券信息
        if (name) coupon.name = name;
        if (type) coupon.type = type;
        if (value !== undefined) coupon.value = value;
        if (startDate) coupon.startDate = startDate;
        if (endDate) coupon.endDate = endDate;
        if (totalAmount) coupon.totalAmount = totalAmount;
        if (description !== undefined) coupon.description = description;
        if (limitType) coupon.limitType = limitType;
        if (status) coupon.status = status;
        
        await coupon.save();
        
        return res.status(200).json({
            success: true,
            message: '优惠券更新成功',
            coupon
        });
    } catch (error) {
        console.error('更新优惠券错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 删除优惠券
 */
exports.deleteCoupon = async (req, res) => {
    try {
        // 查找并删除优惠券
        const coupon = await Coupon.findByIdAndDelete(req.params.id);
        
        if (!coupon) {
            return res.status(404).json({ success: false, message: '优惠券不存在' });
        }
        
        return res.status(200).json({
            success: true,
            message: '优惠券删除成功'
        });
    } catch (error) {
        console.error('删除优惠券错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
}; 