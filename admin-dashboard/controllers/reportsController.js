const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'writerprocn',
    password: 'wan521521',
    database: 'writerprocn',
    charset: 'utf8mb4'
};

class ReportsController {
    
    // 获取业务概览报表
    async getBusinessOverview(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { startDate, endDate } = req.query;
            const dateFilter = this.buildDateFilter(startDate, endDate);
            
            // 获取总体统计
            const [overviewStats] = await connection.execute(`
                SELECT 
                    COUNT(DISTINCT u.id) as totalUsers,
                    COUNT(DISTINCT o.id) as totalOrders,
                    COUNT(DISTINCT w.id) as totalWorkOrders,
                    COUNT(DISTINCT e.id) as totalExperts,
                    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as totalRevenue,
                    AVG(CASE WHEN w.status = 'completed' AND w.actualHours > 0 THEN w.actualHours END) as avgCompletionTime,
                    COUNT(CASE WHEN w.status = 'completed' THEN 1 END) as completedWorkOrders,
                    COUNT(CASE WHEN w.status = 'pending' THEN 1 END) as pendingWorkOrders
                FROM User u
                LEFT JOIN \`Order\` o ON u.id = o.userId ${dateFilter.orders}
                LEFT JOIN WorkOrder w ON o.id = w.orderId ${dateFilter.workOrders}
                LEFT JOIN Expert e ON w.expertId = e.id
            `);

            // 获取服务类型分布
            const [serviceTypeStats] = await connection.execute(`
                SELECT 
                    o.serviceType,
                    COUNT(*) as orderCount,
                    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as revenue
                FROM \`Order\` o
                WHERE 1=1 ${dateFilter.orders}
                GROUP BY o.serviceType
            `);

            // 获取每日收入趋势
            const [dailyRevenue] = await connection.execute(`
                SELECT 
                    DATE(o.createdAt) as date,
                    COUNT(*) as orderCount,
                    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as revenue,
                    COUNT(CASE WHEN o.serviceType = 'ai_optimization' THEN 1 END) as aiOrders,
                    COUNT(CASE WHEN o.serviceType = 'expert_optimization' THEN 1 END) as expertOrders
                FROM \`Order\` o
                WHERE 1=1 ${dateFilter.orders}
                GROUP BY DATE(o.createdAt)
                ORDER BY date DESC
                LIMIT 30
            `);

            // 获取专家效率统计
            const [expertEfficiency] = await connection.execute(`
                SELECT 
                    e.expertCode,
                    e.realName,
                    COUNT(w.id) as totalWorkOrders,
                    COUNT(CASE WHEN w.status = 'completed' THEN 1 END) as completedWorkOrders,
                    AVG(CASE WHEN w.status = 'completed' AND w.actualHours > 0 THEN w.actualHours END) as avgHours,
                    AVG(w.clientRating) as avgRating
                FROM Expert e
                LEFT JOIN WorkOrder w ON e.id = w.expertId ${dateFilter.workOrders}
                GROUP BY e.id, e.expertCode, e.realName
                HAVING totalWorkOrders > 0
                ORDER BY completedWorkOrders DESC
                LIMIT 10
            `);

            res.json({
                success: true,
                data: {
                    overview: overviewStats[0],
                    serviceTypeDistribution: serviceTypeStats,
                    dailyRevenueTrend: dailyRevenue,
                    expertEfficiency: expertEfficiency
                }
            });

        } catch (error) {
            console.error('获取业务概览失败:', error);
            res.status(500).json({
                success: false,
                message: '获取业务概览失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取财务报表
    async getFinancialReport(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { startDate, endDate, groupBy = 'day' } = req.query;
            const dateFilter = this.buildDateFilter(startDate, endDate);
            const groupByClause = this.buildGroupByClause(groupBy);

            // 收入统计
            const [revenueStats] = await connection.execute(`
                SELECT 
                    ${groupByClause.select} as period,
                    COUNT(*) as orderCount,
                    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as totalRevenue,
                    SUM(CASE WHEN o.status = 'paid' AND o.serviceType = 'ai_optimization' THEN o.amount ELSE 0 END) as aiRevenue,
                    SUM(CASE WHEN o.status = 'paid' AND o.serviceType = 'expert_optimization' THEN o.amount ELSE 0 END) as expertRevenue,
                    AVG(CASE WHEN o.status = 'paid' THEN o.amount END) as avgOrderValue
                FROM \`Order\` o
                WHERE 1=1 ${dateFilter.orders}
                GROUP BY ${groupByClause.group}
                ORDER BY period DESC
                LIMIT 50
            `);

            // 支付方式统计
            const [paymentMethodStats] = await connection.execute(`
                SELECT 
                    p.paymentMethod,
                    COUNT(*) as transactionCount,
                    SUM(p.amount) as totalAmount,
                    AVG(p.amount) as avgAmount
                FROM Payment p
                WHERE p.status = 'completed' ${dateFilter.payments}
                GROUP BY p.paymentMethod
            `);

            // 退款统计
            const [refundStats] = await connection.execute(`
                SELECT 
                    COUNT(*) as refundCount,
                    SUM(amount) as refundAmount
                FROM \`Order\` o
                WHERE o.status = 'cancelled' ${dateFilter.orders}
            `);

            res.json({
                success: true,
                data: {
                    revenueStats: revenueStats,
                    paymentMethodStats: paymentMethodStats,
                    refundStats: refundStats[0] || { refundCount: 0, refundAmount: 0 }
                }
            });

        } catch (error) {
            console.error('获取财务报表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取财务报表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取用户行为报表
    async getUserBehaviorReport(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { startDate, endDate } = req.query;
            const dateFilter = this.buildDateFilter(startDate, endDate);

            // 用户注册趋势
            const [userRegistrationTrend] = await connection.execute(`
                SELECT 
                    DATE(createdAt) as date,
                    COUNT(*) as newUsers
                FROM User
                WHERE 1=1 ${dateFilter.users}
                GROUP BY DATE(createdAt)
                ORDER BY date DESC
                LIMIT 30
            `);

            // 用户活跃度
            const [userActivity] = await connection.execute(`
                SELECT 
                    COUNT(DISTINCT u.id) as totalUsers,
                    COUNT(DISTINCT CASE WHEN o.createdAt >= DATE_SUB(NOW(), INTERVAL 7 DAY) THEN u.id END) as activeUsers7d,
                    COUNT(DISTINCT CASE WHEN o.createdAt >= DATE_SUB(NOW(), INTERVAL 30 DAY) THEN u.id END) as activeUsers30d,
                    COUNT(DISTINCT CASE WHEN o.createdAt >= DATE_SUB(NOW(), INTERVAL 1 DAY) THEN u.id END) as activeUsers1d
                FROM User u
                LEFT JOIN \`Order\` o ON u.id = o.userId
            `);

            // 用户消费行为
            const [userConsumption] = await connection.execute(`
                SELECT 
                    u.id,
                    u.username,
                    COUNT(o.id) as orderCount,
                    SUM(CASE WHEN o.status = 'paid' THEN o.amount ELSE 0 END) as totalSpent,
                    AVG(CASE WHEN o.status = 'paid' THEN o.amount END) as avgOrderValue,
                    MAX(o.createdAt) as lastOrderDate
                FROM User u
                LEFT JOIN \`Order\` o ON u.id = o.userId ${dateFilter.orders}
                GROUP BY u.id, u.username
                HAVING orderCount > 0
                ORDER BY totalSpent DESC
                LIMIT 20
            `);

            // 服务偏好
            const [servicePreference] = await connection.execute(`
                SELECT 
                    o.serviceType,
                    COUNT(*) as orderCount,
                    COUNT(DISTINCT o.userId) as uniqueUsers,
                    AVG(o.amount) as avgAmount
                FROM \`Order\` o
                WHERE 1=1 ${dateFilter.orders}
                GROUP BY o.serviceType
            `);

            res.json({
                success: true,
                data: {
                    registrationTrend: userRegistrationTrend,
                    userActivity: userActivity[0],
                    topConsumers: userConsumption,
                    servicePreference: servicePreference
                }
            });

        } catch (error) {
            console.error('获取用户行为报表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取用户行为报表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取专家绩效报表
    async getExpertPerformanceReport(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { startDate, endDate, expertId } = req.query;
            const dateFilter = this.buildDateFilter(startDate, endDate);
            const expertFilter = expertId ? `AND e.id = ${parseInt(expertId)}` : '';

            // 专家工作量统计
            const [expertWorkload] = await connection.execute(`
                SELECT 
                    e.expertCode,
                    e.realName,
                    e.status,
                    COUNT(w.id) as totalWorkOrders,
                    COUNT(CASE WHEN w.status = 'completed' THEN 1 END) as completedWorkOrders,
                    COUNT(CASE WHEN w.status = 'in_progress' THEN 1 END) as inProgressWorkOrders,
                    SUM(w.actualHours) as totalHours,
                    AVG(w.actualHours) as avgHoursPerOrder,
                    AVG(w.clientRating) as avgRating,
                    SUM(CASE WHEN w.status = 'completed' THEN o.amount ELSE 0 END) as totalRevenue
                FROM Expert e
                LEFT JOIN WorkOrder w ON e.id = w.expertId ${dateFilter.workOrders}
                LEFT JOIN \`Order\` o ON w.orderId = o.id
                WHERE 1=1 ${expertFilter}
                GROUP BY e.id, e.expertCode, e.realName, e.status
                ORDER BY completedWorkOrders DESC
            `);

            // 专家效率趋势
            const [expertEfficiencyTrend] = await connection.execute(`
                SELECT 
                    DATE(w.updatedAt) as date,
                    e.expertCode,
                    e.realName,
                    COUNT(CASE WHEN w.status = 'completed' THEN 1 END) as completedCount,
                    AVG(CASE WHEN w.status = 'completed' THEN w.actualHours END) as avgHours,
                    AVG(w.clientRating) as avgRating
                FROM Expert e
                LEFT JOIN WorkOrder w ON e.id = w.expertId
                WHERE w.status = 'completed' ${dateFilter.workOrders} ${expertFilter}
                GROUP BY DATE(w.updatedAt), e.id, e.expertCode, e.realName
                ORDER BY date DESC
                LIMIT 100
            `);

            // 专家客户满意度
            const [customerSatisfaction] = await connection.execute(`
                SELECT 
                    e.expertCode,
                    e.realName,
                    COUNT(w.clientRating) as ratedOrders,
                    AVG(w.clientRating) as avgRating,
                    COUNT(CASE WHEN w.clientRating >= 4 THEN 1 END) as highRatingCount,
                    COUNT(CASE WHEN w.clientRating <= 2 THEN 1 END) as lowRatingCount
                FROM Expert e
                LEFT JOIN WorkOrder w ON e.id = w.expertId
                WHERE w.clientRating IS NOT NULL ${dateFilter.workOrders} ${expertFilter}
                GROUP BY e.id, e.expertCode, e.realName
                HAVING ratedOrders > 0
                ORDER BY avgRating DESC
            `);

            res.json({
                success: true,
                data: {
                    expertWorkload: expertWorkload,
                    efficiencyTrend: expertEfficiencyTrend,
                    customerSatisfaction: customerSatisfaction
                }
            });

        } catch (error) {
            console.error('获取专家绩效报表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取专家绩效报表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 导出报表数据
    async exportReport(req, res) {
        try {
            const { reportType, format = 'json', ...params } = req.query;
            
            let reportData;
            switch (reportType) {
                case 'business':
                    reportData = await this.getBusinessOverviewData(params);
                    break;
                case 'financial':
                    reportData = await this.getFinancialReportData(params);
                    break;
                case 'user':
                    reportData = await this.getUserBehaviorReportData(params);
                    break;
                case 'expert':
                    reportData = await this.getExpertPerformanceReportData(params);
                    break;
                default:
                    return res.status(400).json({
                        success: false,
                        message: '不支持的报表类型'
                    });
            }

            if (format === 'csv') {
                const csv = this.convertToCSV(reportData);
                res.setHeader('Content-Type', 'text/csv');
                res.setHeader('Content-Disposition', `attachment; filename="${reportType}_report_${Date.now()}.csv"`);
                res.send(csv);
            } else {
                res.json({
                    success: true,
                    data: reportData
                });
            }

        } catch (error) {
            console.error('导出报表失败:', error);
            res.status(500).json({
                success: false,
                message: '导出报表失败',
                error: error.message
            });
        }
    }

    // 辅助方法
    buildDateFilter(startDate, endDate) {
        let ordersFilter = '';
        let workOrdersFilter = '';
        let usersFilter = '';
        let paymentsFilter = '';

        if (startDate) {
            ordersFilter += ` AND o.createdAt >= '${startDate}'`;
            workOrdersFilter += ` AND w.createdAt >= '${startDate}'`;
            usersFilter += ` AND createdAt >= '${startDate}'`;
            paymentsFilter += ` AND p.createdAt >= '${startDate}'`;
        }

        if (endDate) {
            ordersFilter += ` AND o.createdAt <= '${endDate} 23:59:59'`;
            workOrdersFilter += ` AND w.createdAt <= '${endDate} 23:59:59'`;
            usersFilter += ` AND createdAt <= '${endDate} 23:59:59'`;
            paymentsFilter += ` AND p.createdAt <= '${endDate} 23:59:59'`;
        }

        return {
            orders: ordersFilter,
            workOrders: workOrdersFilter,
            users: usersFilter,
            payments: paymentsFilter
        };
    }

    buildGroupByClause(groupBy) {
        switch (groupBy) {
            case 'hour':
                return {
                    select: 'DATE_FORMAT(o.createdAt, "%Y-%m-%d %H:00:00")',
                    group: 'DATE_FORMAT(o.createdAt, "%Y-%m-%d %H")'
                };
            case 'day':
                return {
                    select: 'DATE(o.createdAt)',
                    group: 'DATE(o.createdAt)'
                };
            case 'week':
                return {
                    select: 'DATE_FORMAT(o.createdAt, "%Y-%u")',
                    group: 'YEAR(o.createdAt), WEEK(o.createdAt)'
                };
            case 'month':
                return {
                    select: 'DATE_FORMAT(o.createdAt, "%Y-%m")',
                    group: 'YEAR(o.createdAt), MONTH(o.createdAt)'
                };
            default:
                return {
                    select: 'DATE(o.createdAt)',
                    group: 'DATE(o.createdAt)'
                };
        }
    }

    convertToCSV(data) {
        if (!data || data.length === 0) return '';
        
        const headers = Object.keys(data[0]);
        const csvContent = [
            headers.join(','),
            ...data.map(row => 
                headers.map(header => 
                    JSON.stringify(row[header] || '')
                ).join(',')
            )
        ].join('\n');
        
        return csvContent;
    }
}

module.exports = new ReportsController();
