const memoryStore = require('../models/memoryStore');

/**
 * 营销工具控制器
 * 处理优惠券管理、推广活动、邀请返利、积分系统等功能
 */

// 获取营销统计概览
exports.getMarketingOverview = async (req, res) => {
    try {
        const coupons = await memoryStore.findDocuments('coupons');
        const campaigns = await memoryStore.findDocuments('campaigns');
        const referrals = await memoryStore.findDocuments('referrals');
        const pointsTransactions = await memoryStore.findDocuments('pointsTransactions');
        
        // 计算统计数据
        const activeCoupons = coupons.filter(c => c.status === 'active').length;
        const newCoupons = coupons.filter(c => {
            const createdDate = new Date(c.createdAt);
            const thisMonth = new Date();
            thisMonth.setDate(1);
            return createdDate >= thisMonth;
        }).length;
        
        const invitedUsers = referrals.filter(r => r.status === 'completed').length;
        const totalInvitations = referrals.length;
        const inviteConversion = totalInvitations > 0 ? (invitedUsers / totalInvitations * 100).toFixed(1) : 0;
        
        const totalPoints = pointsTransactions
            .filter(t => t.type === 'earn')
            .reduce((sum, t) => sum + t.amount, 0);
        const monthlyPoints = pointsTransactions
            .filter(t => {
                const date = new Date(t.createdAt);
                const thisMonth = new Date();
                thisMonth.setDate(1);
                return date >= thisMonth && t.type === 'earn';
            })
            .reduce((sum, t) => sum + t.amount, 0);
        
        // 模拟营销ROI
        const marketingROI = 245.8;
        const roiGrowth = 12.5;
        
        res.json({
            success: true,
            data: {
                activeCoupons,
                newCoupons,
                invitedUsers,
                inviteConversion: parseFloat(inviteConversion),
                totalPoints,
                monthlyPoints,
                marketingROI,
                roiGrowth
            }
        });
        
    } catch (error) {
        console.error('获取营销概览错误:', error);
        res.status(500).json({
            success: false,
            message: '获取营销概览失败'
        });
    }
};

// 获取优惠券列表
exports.getCoupons = async (req, res) => {
    try {
        const { page = 1, limit = 20, status } = req.query;
        
        let coupons = await memoryStore.findDocuments('coupons');
        
        // 状态过滤
        if (status) {
            coupons = coupons.filter(c => c.status === status);
        }
        
        // 排序
        coupons.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedCoupons = coupons.slice(startIndex, endIndex);
        
        // 计算统计信息
        const totalCoupons = coupons.length;
        const usedCoupons = coupons.reduce((sum, c) => sum + c.usedCount, 0);
        const couponSavings = coupons.reduce((sum, c) => sum + (c.savings || 0), 0);
        const totalIssued = coupons.reduce((sum, c) => sum + c.totalCount, 0);
        const couponConversion = totalIssued > 0 ? (usedCoupons / totalIssued * 100).toFixed(1) : 0;
        
        res.json({
            success: true,
            data: paginatedCoupons,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: coupons.length,
                pages: Math.ceil(coupons.length / limit)
            },
            stats: {
                totalCoupons,
                usedCoupons,
                couponSavings,
                couponConversion: parseFloat(couponConversion)
            }
        });
        
    } catch (error) {
        console.error('获取优惠券列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取优惠券列表失败'
        });
    }
};

// 创建优惠券
exports.createCoupon = async (req, res) => {
    try {
        const couponData = {
            ...req.body,
            code: req.body.code || generateCouponCode(),
            status: 'active',
            usedCount: 0,
            savings: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const coupon = await memoryStore.createDocument('coupons', couponData);
        
        res.json({
            success: true,
            data: coupon,
            message: '优惠券创建成功'
        });
        
    } catch (error) {
        console.error('创建优惠券错误:', error);
        res.status(500).json({
            success: false,
            message: '创建优惠券失败'
        });
    }
};

// 更新优惠券
exports.updateCoupon = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };
        
        const coupon = await memoryStore.updateDocument('coupons', id, updateData);
        
        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: '优惠券不存在'
            });
        }
        
        res.json({
            success: true,
            data: coupon,
            message: '优惠券更新成功'
        });
        
    } catch (error) {
        console.error('更新优惠券错误:', error);
        res.status(500).json({
            success: false,
            message: '更新优惠券失败'
        });
    }
};

// 获取推广活动列表
exports.getCampaigns = async (req, res) => {
    try {
        const campaigns = await memoryStore.findDocuments('campaigns');
        
        res.json({
            success: true,
            data: campaigns
        });
        
    } catch (error) {
        console.error('获取推广活动错误:', error);
        res.status(500).json({
            success: false,
            message: '获取推广活动失败'
        });
    }
};

// 创建推广活动
exports.createCampaign = async (req, res) => {
    try {
        const campaignData = {
            ...req.body,
            status: 'draft',
            participants: 0,
            conversions: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const campaign = await memoryStore.createDocument('campaigns', campaignData);
        
        res.json({
            success: true,
            data: campaign,
            message: '推广活动创建成功'
        });
        
    } catch (error) {
        console.error('创建推广活动错误:', error);
        res.status(500).json({
            success: false,
            message: '创建推广活动失败'
        });
    }
};

// 获取邀请返利设置
exports.getReferralSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'referral' });
        
        if (!settings) {
            // 创建默认邀请返利设置
            settings = await memoryStore.createDocument('settings', {
                type: 'referral',
                data: {
                    inviterReward: 50,
                    inviteeReward: 30,
                    linkExpiry: 30,
                    enableReferral: true
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data
        });
        
    } catch (error) {
        console.error('获取邀请返利设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取邀请返利设置失败'
        });
    }
};

// 更新邀请返利设置
exports.updateReferralSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'referral' });
        
        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'referral',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data,
            message: '邀请返利设置更新成功'
        });
        
    } catch (error) {
        console.error('更新邀请返利设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新邀请返利设置失败'
        });
    }
};

// 获取积分系统设置
exports.getPointsSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'points' });
        
        if (!settings) {
            // 创建默认积分设置
            settings = await memoryStore.createDocument('settings', {
                type: 'points',
                data: {
                    registerPoints: 100,
                    dailyCheckIn: 10,
                    spendingRatio: 1,
                    exchangeRatio: 100,
                    pointsExpiry: 365
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data
        });
        
    } catch (error) {
        console.error('获取积分设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取积分设置失败'
        });
    }
};

// 更新积分系统设置
exports.updatePointsSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'points' });
        
        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'points',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data,
            message: '积分设置更新成功'
        });
        
    } catch (error) {
        console.error('更新积分设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新积分设置失败'
        });
    }
};

// 生成优惠券代码的辅助函数
function generateCouponCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 获取优惠券详情
exports.getCouponDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const coupon = await memoryStore.findOneDocument('coupons', { _id: id });

        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: '优惠券不存在'
            });
        }

        res.json({
            success: true,
            data: coupon
        });

    } catch (error) {
        console.error('获取优惠券详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取优惠券详情失败'
        });
    }
};

// 切换优惠券状态
exports.toggleCouponStatus = async (req, res) => {
    try {
        const { id } = req.params;

        const coupon = await memoryStore.findOneDocument('coupons', { _id: id });
        if (!coupon) {
            return res.status(404).json({
                success: false,
                message: '优惠券不存在'
            });
        }

        const newStatus = coupon.status === 'active' ? 'paused' : 'active';

        const updatedCoupon = await memoryStore.updateDocument('coupons', id, {
            status: newStatus,
            updatedAt: new Date()
        });

        res.json({
            success: true,
            data: updatedCoupon,
            message: `优惠券已${newStatus === 'active' ? '启用' : '暂停'}`
        });

    } catch (error) {
        console.error('切换优惠券状态错误:', error);
        res.status(500).json({
            success: false,
            message: '切换优惠券状态失败'
        });
    }
};

// 获取推广活动详情
exports.getCampaignDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const campaign = await memoryStore.findOneDocument('campaigns', { _id: id });

        if (!campaign) {
            return res.status(404).json({
                success: false,
                message: '推广活动不存在'
            });
        }

        res.json({
            success: true,
            data: campaign
        });

    } catch (error) {
        console.error('获取推广活动详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取推广活动详情失败'
        });
    }
};

// 切换推广活动状态
exports.toggleCampaignStatus = async (req, res) => {
    try {
        const { id } = req.params;

        const campaign = await memoryStore.findOneDocument('campaigns', { _id: id });
        if (!campaign) {
            return res.status(404).json({
                success: false,
                message: '推广活动不存在'
            });
        }

        const newStatus = campaign.status === 'active' ? 'paused' : 'active';

        const updatedCampaign = await memoryStore.updateDocument('campaigns', id, {
            status: newStatus,
            updatedAt: new Date()
        });

        res.json({
            success: true,
            data: updatedCampaign,
            message: `推广活动已${newStatus === 'active' ? '启用' : '暂停'}`
        });

    } catch (error) {
        console.error('切换推广活动状态错误:', error);
        res.status(500).json({
            success: false,
            message: '切换推广活动状态失败'
        });
    }
};

// 获取邀请记录
exports.getReferrals = async (req, res) => {
    try {
        const { page = 1, limit = 20, status = '' } = req.query;

        let query = {};
        if (status) {
            query.status = status;
        }

        const referrals = await memoryStore.findDocuments('referrals', query);

        // 分页处理
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedReferrals = referrals.slice(startIndex, endIndex);

        const pagination = {
            page: parseInt(page),
            pages: Math.ceil(referrals.length / limit),
            total: referrals.length,
            limit: parseInt(limit)
        };

        res.json({
            success: true,
            data: paginatedReferrals,
            pagination
        });

    } catch (error) {
        console.error('获取邀请记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取邀请记录失败'
        });
    }
};

// 获取积分交易记录
exports.getPointsTransactions = async (req, res) => {
    try {
        const { page = 1, limit = 20, type = '', userId = '' } = req.query;

        let query = {};
        if (type) {
            query.type = type;
        }
        if (userId) {
            query.userId = userId;
        }

        const transactions = await memoryStore.findDocuments('pointsTransactions', query);

        // 分页处理
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedTransactions = transactions.slice(startIndex, endIndex);

        const pagination = {
            page: parseInt(page),
            pages: Math.ceil(transactions.length / limit),
            total: transactions.length,
            limit: parseInt(limit)
        };

        res.json({
            success: true,
            data: paginatedTransactions,
            pagination
        });

    } catch (error) {
        console.error('获取积分交易记录错误:', error);
        res.status(500).json({
            success: false,
            message: '获取积分交易记录失败'
        });
    }
};

// 获取营销分析数据
exports.getMarketingAnalytics = async (req, res) => {
    try {
        const { period = '30d' } = req.query;

        // 模拟营销分析数据
        const analytics = {
            couponUsage: {
                labels: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
                data: [45, 52, 38, 67, 73, 89, 95]
            },
            campaignPerformance: {
                labels: ['夏季促销', '新用户专享', '会员专享', '限时折扣'],
                data: [320, 280, 150, 200]
            },
            referralTrend: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                data: [65, 78, 92, 85, 98, 112, 125]
            },
            pointsDistribution: {
                labels: ['签到奖励', '购买奖励', '邀请奖励', '活动奖励'],
                data: [35, 45, 15, 25]
            }
        };

        res.json({
            success: true,
            data: analytics
        });

    } catch (error) {
        console.error('获取营销分析数据错误:', error);
        res.status(500).json({
            success: false,
            message: '获取营销分析数据失败'
        });
    }
};

module.exports = exports;
