const os = require('os');
const fs = require('fs').promises;
const memoryStore = require('../models/memoryStore');

/**
 * 系统监控控制器
 * 提供系统状态、API监控、性能告警等功能
 */

// 存储API调用统计
let apiStats = {
    totalCalls: 0,
    successCalls: 0,
    failureCalls: 0,
    responseTimes: [],
    dailyStats: {},
    hourlyStats: {}
};

// 告警状态
let alertStatus = {
    cpu: { active: false, lastAlert: null },
    memory: { active: false, lastAlert: null },
    disk: { active: false, lastAlert: null },
    api: { active: false, lastAlert: null }
};

// 获取系统状态
exports.getSystemStatus = async (req, res) => {
    try {
        // 获取CPU使用率（简化计算）
        const cpus = os.cpus();
        let totalIdle = 0;
        let totalTick = 0;
        
        cpus.forEach(cpu => {
            for (type in cpu.times) {
                totalTick += cpu.times[type];
            }
            totalIdle += cpu.times.idle;
        });
        
        const idle = totalIdle / cpus.length;
        const total = totalTick / cpus.length;
        const cpuUsage = 100 - ~~(100 * idle / total);
        
        // 获取内存使用率
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const memoryUsage = Math.round((usedMemory / totalMemory) * 100);
        
        // 模拟磁盘使用率（实际应用中需要使用专门的库）
        const diskUsage = Math.floor(Math.random() * 20) + 30;
        
        // 模拟网络流量
        const networkTraffic = (Math.random() * 2 + 0.5).toFixed(1) + 'MB/s';
        
        res.json({
            success: true,
            data: {
                cpu: Math.min(cpuUsage, 100),
                memory: memoryUsage,
                disk: diskUsage,
                network: networkTraffic,
                uptime: os.uptime(),
                loadAverage: os.loadavg(),
                platform: os.platform(),
                arch: os.arch(),
                hostname: os.hostname(),
                timestamp: new Date()
            }
        });
        
    } catch (error) {
        console.error('获取系统状态错误:', error);
        res.status(500).json({
            success: false,
            message: '获取系统状态失败'
        });
    }
};

// 获取API统计
exports.getApiStats = async (req, res) => {
    try {
        const today = new Date().toDateString();
        const currentHour = new Date().getHours();
        
        // 获取今日统计
        const todayStats = apiStats.dailyStats[today] || {
            totalCalls: 0,
            successCalls: 0,
            failureCalls: 0,
            responseTimes: []
        };
        
        // 计算成功率
        const successRate = todayStats.totalCalls > 0 
            ? ((todayStats.successCalls / todayStats.totalCalls) * 100).toFixed(1)
            : '100.0';
        
        // 计算平均响应时间
        const avgResponseTime = todayStats.responseTimes.length > 0
            ? Math.round(todayStats.responseTimes.reduce((a, b) => a + b, 0) / todayStats.responseTimes.length)
            : 0;
        
        res.json({
            success: true,
            data: {
                todayCalls: todayStats.totalCalls,
                successRate: parseFloat(successRate),
                avgResponseTime: avgResponseTime,
                errors: todayStats.failureCalls,
                hourlyStats: apiStats.hourlyStats[today] || {},
                timestamp: new Date()
            }
        });
        
    } catch (error) {
        console.error('获取API统计错误:', error);
        res.status(500).json({
            success: false,
            message: '获取API统计失败'
        });
    }
};

// 记录API调用
exports.recordApiCall = (success = true, responseTime = 0) => {
    const today = new Date().toDateString();
    const currentHour = new Date().getHours();
    
    // 初始化今日统计
    if (!apiStats.dailyStats[today]) {
        apiStats.dailyStats[today] = {
            totalCalls: 0,
            successCalls: 0,
            failureCalls: 0,
            responseTimes: []
        };
    }
    
    // 初始化小时统计
    if (!apiStats.hourlyStats[today]) {
        apiStats.hourlyStats[today] = {};
    }
    if (!apiStats.hourlyStats[today][currentHour]) {
        apiStats.hourlyStats[today][currentHour] = {
            totalCalls: 0,
            successCalls: 0,
            failureCalls: 0
        };
    }
    
    // 更新统计
    apiStats.totalCalls++;
    apiStats.dailyStats[today].totalCalls++;
    apiStats.hourlyStats[today][currentHour].totalCalls++;
    
    if (success) {
        apiStats.successCalls++;
        apiStats.dailyStats[today].successCalls++;
        apiStats.hourlyStats[today][currentHour].successCalls++;
    } else {
        apiStats.failureCalls++;
        apiStats.dailyStats[today].failureCalls++;
        apiStats.hourlyStats[today][currentHour].failureCalls++;
    }
    
    // 记录响应时间
    if (responseTime > 0) {
        apiStats.responseTimes.push(responseTime);
        apiStats.dailyStats[today].responseTimes.push(responseTime);
        
        // 只保留最近1000次的响应时间
        if (apiStats.responseTimes.length > 1000) {
            apiStats.responseTimes = apiStats.responseTimes.slice(-1000);
        }
        if (apiStats.dailyStats[today].responseTimes.length > 1000) {
            apiStats.dailyStats[today].responseTimes = apiStats.dailyStats[today].responseTimes.slice(-1000);
        }
    }
};

// 获取系统健康状态
exports.getSystemHealth = async (req, res) => {
    try {
        const systemStatus = await getSystemStatusData();
        const apiStatsData = await getApiStatsData();
        
        // 计算健康分数
        let healthScore = 100;
        
        // CPU使用率影响
        if (systemStatus.cpu > 80) healthScore -= 20;
        else if (systemStatus.cpu > 60) healthScore -= 10;
        
        // 内存使用率影响
        if (systemStatus.memory > 90) healthScore -= 25;
        else if (systemStatus.memory > 70) healthScore -= 15;
        
        // API错误率影响
        const errorRate = apiStatsData.todayCalls > 0 
            ? (apiStatsData.errors / apiStatsData.todayCalls) * 100 
            : 0;
        if (errorRate > 5) healthScore -= 30;
        else if (errorRate > 1) healthScore -= 15;
        
        // 响应时间影响
        if (apiStatsData.avgResponseTime > 1000) healthScore -= 20;
        else if (apiStatsData.avgResponseTime > 500) healthScore -= 10;
        
        healthScore = Math.max(0, healthScore);
        
        let healthStatus = 'excellent';
        if (healthScore < 60) healthStatus = 'poor';
        else if (healthScore < 80) healthStatus = 'fair';
        else if (healthScore < 95) healthStatus = 'good';
        
        res.json({
            success: true,
            data: {
                score: healthScore,
                status: healthStatus,
                checks: {
                    cpu: systemStatus.cpu < 80,
                    memory: systemStatus.memory < 90,
                    api: errorRate < 5,
                    responseTime: apiStatsData.avgResponseTime < 500
                },
                recommendations: generateRecommendations(systemStatus, apiStatsData),
                timestamp: new Date()
            }
        });
        
    } catch (error) {
        console.error('获取系统健康状态错误:', error);
        res.status(500).json({
            success: false,
            message: '获取系统健康状态失败'
        });
    }
};

// 生成建议
function generateRecommendations(systemStatus, apiStats) {
    const recommendations = [];
    
    if (systemStatus.cpu > 80) {
        recommendations.push('CPU使用率过高，建议检查高耗CPU的进程');
    }
    
    if (systemStatus.memory > 90) {
        recommendations.push('内存使用率过高，建议释放内存或增加内存容量');
    }
    
    const errorRate = apiStats.todayCalls > 0 ? (apiStats.errors / apiStats.todayCalls) * 100 : 0;
    if (errorRate > 5) {
        recommendations.push('API错误率过高，建议检查API服务状态');
    }
    
    if (apiStats.avgResponseTime > 1000) {
        recommendations.push('API响应时间过长，建议优化API性能');
    }
    
    if (recommendations.length === 0) {
        recommendations.push('系统运行状态良好，继续保持');
    }
    
    return recommendations;
}

// 获取系统状态数据（内部使用）
async function getSystemStatusData() {
    const cpus = os.cpus();
    let totalIdle = 0;
    let totalTick = 0;
    
    cpus.forEach(cpu => {
        for (type in cpu.times) {
            totalTick += cpu.times[type];
        }
        totalIdle += cpu.times.idle;
    });
    
    const idle = totalIdle / cpus.length;
    const total = totalTick / cpus.length;
    const cpuUsage = 100 - ~~(100 * idle / total);
    
    const totalMemory = os.totalmem();
    const freeMemory = os.freemem();
    const usedMemory = totalMemory - freeMemory;
    const memoryUsage = Math.round((usedMemory / totalMemory) * 100);
    
    return {
        cpu: Math.min(cpuUsage, 100),
        memory: memoryUsage,
        disk: Math.floor(Math.random() * 20) + 30
    };
}

// 获取API统计数据（内部使用）
async function getApiStatsData() {
    const today = new Date().toDateString();
    const todayStats = apiStats.dailyStats[today] || {
        totalCalls: 0,
        successCalls: 0,
        failureCalls: 0,
        responseTimes: []
    };
    
    const avgResponseTime = todayStats.responseTimes.length > 0
        ? Math.round(todayStats.responseTimes.reduce((a, b) => a + b, 0) / todayStats.responseTimes.length)
        : 0;
    
    return {
        todayCalls: todayStats.totalCalls,
        errors: todayStats.failureCalls,
        avgResponseTime: avgResponseTime
    };
}

// 获取告警配置
exports.getAlertConfig = async (req, res) => {
    try {
        const config = await memoryStore.findOne('alert_config', {}) || {
            cpu: { threshold: 80, enabled: true },
            memory: { threshold: 85, enabled: true },
            disk: { threshold: 90, enabled: true },
            response_time: { threshold: 2000, enabled: true },
            error_rate: { threshold: 5, enabled: true },
            notification: {
                email: true,
                webhook: false,
                sms: false
            },
            check_interval: 60,
            alert_cooldown: 300
        };

        res.json({
            success: true,
            data: config
        });
    } catch (error) {
        console.error('获取告警配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取告警配置失败'
        });
    }
};

// 更新告警配置
exports.updateAlertConfig = async (req, res) => {
    try {
        const config = req.body;

        await memoryStore.updateOne('alert_config', {}, config, { upsert: true });

        res.json({
            success: true,
            message: '告警配置更新成功'
        });
    } catch (error) {
        console.error('更新告警配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新告警配置失败'
        });
    }
};

// 获取告警历史
exports.getAlertHistory = async (req, res) => {
    try {
        const { page = 1, limit = 20, level, status } = req.query;

        let query = {};
        if (level) query.level = level;
        if (status) query.status = status;

        const total = await memoryStore.countDocuments('alerts', query);
        const alerts = await memoryStore.findDocuments('alerts', query, {
            skip: (page - 1) * limit,
            limit: parseInt(limit),
            sort: { createdAt: -1 }
        });

        res.json({
            success: true,
            data: {
                alerts,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取告警历史失败:', error);
        res.status(500).json({
            success: false,
            message: '获取告警历史失败'
        });
    }
};

// 处理告警
exports.handleAlert = async (req, res) => {
    try {
        const { alertId } = req.params;
        const { action, note } = req.body;

        const updateData = {
            status: action,
            handledAt: new Date(),
            handledBy: req.user?.id || 'admin',
            note: note || ''
        };

        await memoryStore.updateOne('alerts', { id: alertId }, updateData);

        res.json({
            success: true,
            message: '告警处理成功'
        });
    } catch (error) {
        console.error('处理告警失败:', error);
        res.status(500).json({
            success: false,
            message: '处理告警失败'
        });
    }
};

// 获取性能历史数据
exports.getPerformanceHistory = async (req, res) => {
    try {
        const { period = '24h', metric = 'all' } = req.query;

        const timeRange = calculateTimeRange(period);

        const historyData = await memoryStore.findDocuments('monitoring_history', {
            timestamp: { $gte: timeRange.start, $lte: timeRange.end }
        }, {
            sort: { timestamp: 1 },
            limit: 1000
        });

        res.json({
            success: true,
            data: historyData
        });
    } catch (error) {
        console.error('获取性能历史失败:', error);
        res.status(500).json({
            success: false,
            message: '获取性能历史失败'
        });
    }
};

// 检查告警条件
async function checkAlerts(systemData) {
    try {
        const config = await memoryStore.findOne('alert_config', {});
        if (!config) return;

        const alerts = [];

        // CPU告警检查
        if (config.cpu.enabled && systemData.cpu > config.cpu.threshold) {
            if (!alertStatus.cpu.active ||
                (Date.now() - alertStatus.cpu.lastAlert) > config.alert_cooldown * 1000) {
                alerts.push(createAlert('cpu', 'high', `CPU使用率过高: ${systemData.cpu}%`));
                alertStatus.cpu.active = true;
                alertStatus.cpu.lastAlert = Date.now();
            }
        } else {
            alertStatus.cpu.active = false;
        }

        // 内存告警检查
        if (config.memory.enabled && systemData.memory > config.memory.threshold) {
            if (!alertStatus.memory.active ||
                (Date.now() - alertStatus.memory.lastAlert) > config.alert_cooldown * 1000) {
                alerts.push(createAlert('memory', 'high', `内存使用率过高: ${systemData.memory}%`));
                alertStatus.memory.active = true;
                alertStatus.memory.lastAlert = Date.now();
            }
        } else {
            alertStatus.memory.active = false;
        }

        // 磁盘告警检查
        if (config.disk.enabled && systemData.disk > config.disk.threshold) {
            if (!alertStatus.disk.active ||
                (Date.now() - alertStatus.disk.lastAlert) > config.alert_cooldown * 1000) {
                alerts.push(createAlert('disk', 'critical', `磁盘使用率过高: ${systemData.disk}%`));
                alertStatus.disk.active = true;
                alertStatus.disk.lastAlert = Date.now();
            }
        } else {
            alertStatus.disk.active = false;
        }

        // 保存告警
        for (let alert of alerts) {
            await memoryStore.insertOne('alerts', alert);
        }

    } catch (error) {
        console.error('检查告警失败:', error);
    }
}

// 创建告警对象
function createAlert(type, level, message) {
    return {
        id: generateAlertId(),
        type,
        level,
        message,
        status: 'active',
        createdAt: new Date(),
        handledAt: null,
        handledBy: null,
        note: ''
    };
}

// 保存监控数据
async function saveMonitoringData(data) {
    try {
        await memoryStore.insertOne('monitoring_history', {
            id: generateMonitoringId(),
            ...data,
            timestamp: new Date()
        });

        // 清理旧数据（保留7天）
        const cutoffDate = new Date(Date.now() - 7 * 24 * 60 * 60 * 1000);
        await memoryStore.deleteMany('monitoring_history', {
            timestamp: { $lt: cutoffDate }
        });
    } catch (error) {
        console.error('保存监控数据失败:', error);
    }
}

// 计算时间范围
function calculateTimeRange(period) {
    const now = new Date();
    let start;

    switch (period) {
        case '1h':
            start = new Date(now.getTime() - 60 * 60 * 1000);
            break;
        case '6h':
            start = new Date(now.getTime() - 6 * 60 * 60 * 1000);
            break;
        case '24h':
            start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
        case '7d':
            start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        default:
            start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
    }

    return { start, end: now };
}

// 生成ID
function generateAlertId() {
    return 'alert_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateMonitoringId() {
    return 'monitor_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

module.exports = exports;
