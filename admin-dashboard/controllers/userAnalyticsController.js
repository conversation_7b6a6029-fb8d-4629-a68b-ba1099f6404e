const memoryStore = require('../models/memoryStore');

/**
 * 用户分析控制器
 * 提供用户行为分析、留存率分析、使用统计等功能
 */

// 获取用户统计概览
exports.getUserStats = async (req, res) => {
    try {
        const users = await memoryStore.findDocuments('users');
        const now = new Date();
        const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
        const lastWeek = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        
        // 总用户数
        const totalUsers = users.length;
        
        // 新增用户（本月）
        const newUsers = users.filter(user => 
            new Date(user.createdAt) >= lastMonth
        ).length;
        
        // 活跃用户（7天内登录）
        const activeUsers = users.filter(user => 
            user.lastLoginAt && new Date(user.lastLoginAt) >= lastWeek
        ).length;
        
        // 计算用户增长率
        const lastMonthUsers = users.filter(user => 
            new Date(user.createdAt) < lastMonth
        ).length;
        const userGrowthRate = lastMonthUsers > 0 
            ? ((newUsers / lastMonthUsers) * 100).toFixed(1)
            : '0.0';
        
        // 计算7天留存率
        const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        const newUsersSevenDaysAgo = users.filter(user => {
            const createdDate = new Date(user.createdAt);
            return createdDate >= sevenDaysAgo && createdDate < new Date(now.getTime() - 6 * 24 * 60 * 60 * 1000);
        });
        
        const retainedUsers = newUsersSevenDaysAgo.filter(user => 
            user.lastLoginAt && new Date(user.lastLoginAt) >= new Date(now.getTime() - 24 * 60 * 60 * 1000)
        );
        
        const retentionRate = newUsersSevenDaysAgo.length > 0 
            ? ((retainedUsers.length / newUsersSevenDaysAgo.length) * 100).toFixed(1)
            : '0.0';
        
        res.json({
            success: true,
            data: {
                totalUsers,
                newUsers,
                activeUsers,
                userGrowthRate: parseFloat(userGrowthRate),
                retentionRate: parseFloat(retentionRate)
            }
        });
        
    } catch (error) {
        console.error('获取用户统计错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户统计失败'
        });
    }
};

// 获取用户增长趋势
exports.getUserGrowthTrend = async (req, res) => {
    try {
        const { period = 'month' } = req.query;
        const users = await memoryStore.findDocuments('users');
        
        let data = [];
        const now = new Date();
        
        if (period === 'month') {
            // 最近12个月的数据
            for (let i = 11; i >= 0; i--) {
                const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
                const nextDate = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
                
                const count = users.filter(user => {
                    const createdDate = new Date(user.createdAt);
                    return createdDate >= date && createdDate < nextDate;
                }).length;
                
                data.push({
                    date: date.toISOString().slice(0, 7), // YYYY-MM
                    count: count
                });
            }
        } else if (period === 'week') {
            // 最近12周的数据
            for (let i = 11; i >= 0; i--) {
                const date = new Date(now.getTime() - i * 7 * 24 * 60 * 60 * 1000);
                const nextDate = new Date(date.getTime() + 7 * 24 * 60 * 60 * 1000);
                
                const count = users.filter(user => {
                    const createdDate = new Date(user.createdAt);
                    return createdDate >= date && createdDate < nextDate;
                }).length;
                
                data.push({
                    date: `第${52 - i}周`,
                    count: count
                });
            }
        }
        
        res.json({
            success: true,
            data: data
        });
        
    } catch (error) {
        console.error('获取用户增长趋势错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户增长趋势失败'
        });
    }
};

// 获取用户活跃度分析
exports.getUserActivity = async (req, res) => {
    try {
        const users = await memoryStore.findDocuments('users');
        const now = new Date();
        
        // 按活跃度分类用户
        const categories = {
            daily: 0,    // 日活跃（1天内登录）
            weekly: 0,   // 周活跃（7天内登录）
            monthly: 0,  // 月活跃（30天内登录）
            inactive: 0  // 不活跃（30天以上未登录）
        };
        
        users.forEach(user => {
            if (!user.lastLoginAt) {
                categories.inactive++;
                return;
            }
            
            const lastLogin = new Date(user.lastLoginAt);
            const daysSinceLogin = (now - lastLogin) / (1000 * 60 * 60 * 24);
            
            if (daysSinceLogin <= 1) {
                categories.daily++;
            } else if (daysSinceLogin <= 7) {
                categories.weekly++;
            } else if (daysSinceLogin <= 30) {
                categories.monthly++;
            } else {
                categories.inactive++;
            }
        });
        
        res.json({
            success: true,
            data: categories
        });
        
    } catch (error) {
        console.error('获取用户活跃度错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户活跃度失败'
        });
    }
};

// 获取用户留存分析
exports.getRetentionAnalysis = async (req, res) => {
    try {
        const users = await memoryStore.findDocuments('users');
        const now = new Date();
        
        // 计算不同时期的留存率
        const retentionData = [];
        
        // 计算最近8周的留存率
        for (let week = 7; week >= 0; week--) {
            const weekStart = new Date(now.getTime() - week * 7 * 24 * 60 * 60 * 1000);
            const weekEnd = new Date(weekStart.getTime() + 7 * 24 * 60 * 60 * 1000);
            
            // 该周新注册用户
            const newUsers = users.filter(user => {
                const createdDate = new Date(user.createdAt);
                return createdDate >= weekStart && createdDate < weekEnd;
            });
            
            if (newUsers.length === 0) {
                retentionData.push({
                    week: `第${week + 1}周`,
                    day1: 0,
                    day7: 0,
                    day30: 0
                });
                continue;
            }
            
            // 计算1天、7天、30天留存
            const day1Retained = newUsers.filter(user => {
                if (!user.lastLoginAt) return false;
                const lastLogin = new Date(user.lastLoginAt);
                const dayAfterRegistration = new Date(weekEnd.getTime() + 24 * 60 * 60 * 1000);
                return lastLogin >= weekEnd && lastLogin <= dayAfterRegistration;
            }).length;
            
            const day7Retained = newUsers.filter(user => {
                if (!user.lastLoginAt) return false;
                const lastLogin = new Date(user.lastLoginAt);
                const weekAfterRegistration = new Date(weekEnd.getTime() + 7 * 24 * 60 * 60 * 1000);
                return lastLogin >= weekEnd && lastLogin <= weekAfterRegistration;
            }).length;
            
            const day30Retained = newUsers.filter(user => {
                if (!user.lastLoginAt) return false;
                const lastLogin = new Date(user.lastLoginAt);
                const monthAfterRegistration = new Date(weekEnd.getTime() + 30 * 24 * 60 * 60 * 1000);
                return lastLogin >= weekEnd && lastLogin <= monthAfterRegistration;
            }).length;
            
            retentionData.push({
                week: `第${week + 1}周`,
                day1: ((day1Retained / newUsers.length) * 100).toFixed(1),
                day7: ((day7Retained / newUsers.length) * 100).toFixed(1),
                day30: ((day30Retained / newUsers.length) * 100).toFixed(1)
            });
        }
        
        res.json({
            success: true,
            data: retentionData
        });
        
    } catch (error) {
        console.error('获取留存分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取留存分析失败'
        });
    }
};

// 获取用户分布分析
exports.getUserDistribution = async (req, res) => {
    try {
        const users = await memoryStore.findDocuments('users');
        const roles = await memoryStore.findDocuments('roles');
        
        // 按角色分布
        const roleDistribution = {};
        roles.forEach(role => {
            roleDistribution[role.name] = users.filter(user => user.roleId === role._id).length;
        });
        
        // 按状态分布
        const statusDistribution = {
            active: users.filter(user => user.status === 'active').length,
            inactive: users.filter(user => user.status === 'inactive').length,
            blocked: users.filter(user => user.status === 'blocked').length
        };
        
        // 按注册时间分布（最近6个月）
        const timeDistribution = [];
        const now = new Date();
        for (let i = 5; i >= 0; i--) {
            const monthStart = new Date(now.getFullYear(), now.getMonth() - i, 1);
            const monthEnd = new Date(now.getFullYear(), now.getMonth() - i + 1, 1);
            
            const count = users.filter(user => {
                const createdDate = new Date(user.createdAt);
                return createdDate >= monthStart && createdDate < monthEnd;
            }).length;
            
            timeDistribution.push({
                month: monthStart.toISOString().slice(0, 7),
                count: count
            });
        }
        
        res.json({
            success: true,
            data: {
                roleDistribution,
                statusDistribution,
                timeDistribution
            }
        });
        
    } catch (error) {
        console.error('获取用户分布错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户分布失败'
        });
    }
};

// 获取用户行为分析
exports.getUserBehavior = async (req, res) => {
    try {
        // 模拟用户行为数据
        const behaviorData = {
            avgSessionTime: '25分钟',
            pageViews: 15420,
            bounceRate: '32.5%',
            conversionRate: '8.7%',
            featureUsage: [
                { feature: '文档编辑', usage: 85 },
                { feature: '模板使用', usage: 72 },
                { feature: '导出功能', usage: 68 },
                { feature: '协作功能', usage: 45 },
                { feature: '云存储', usage: 38 }
            ],
            userPath: [
                { step: '登录', users: 1000 },
                { step: '浏览首页', users: 950 },
                { step: '创建文档', users: 680 },
                { step: '编辑文档', users: 520 },
                { step: '保存文档', users: 480 },
                { step: '分享文档', users: 120 }
            ]
        };
        
        res.json({
            success: true,
            data: behaviorData
        });
        
    } catch (error) {
        console.error('获取用户行为分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户行为分析失败'
        });
    }
};

module.exports = exports;
