/**
 * 价格设置控制器
 * 处理产品价格管理、折扣配置、会员价格等功能
 */

const memoryStore = require('../models/memoryStore');

// 获取产品价格列表
exports.getProductPrices = async (req, res) => {
    try {
        const { page = 1, limit = 20, category, status, search } = req.query;
        
        let query = {};
        if (category) query.category = category;
        if (status) query.status = status;
        if (search) {
            query.$or = [
                { name: { $regex: search, $options: 'i' } },
                { sku: { $regex: search, $options: 'i' } }
            ];
        }
        
        const total = await memoryStore.countDocuments('product_prices', query);
        const products = await memoryStore.findDocuments('product_prices', query, {
            skip: (page - 1) * limit,
            limit: parseInt(limit),
            sort: { updatedAt: -1 }
        });
        
        res.json({
            success: true,
            data: {
                products,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取产品价格失败:', error);
        res.status(500).json({
            success: false,
            message: '获取产品价格失败'
        });
    }
};

// 获取单个产品价格详情
exports.getProductPrice = async (req, res) => {
    try {
        const { productId } = req.params;
        
        const product = await memoryStore.findOne('product_prices', { id: productId });
        if (!product) {
            return res.status(404).json({
                success: false,
                message: '产品不存在'
            });
        }
        
        // 获取价格历史
        const priceHistory = await memoryStore.findDocuments('price_history', 
            { productId }, 
            { sort: { createdAt: -1 }, limit: 10 }
        );
        
        res.json({
            success: true,
            data: {
                product,
                priceHistory
            }
        });
    } catch (error) {
        console.error('获取产品价格详情失败:', error);
        res.status(500).json({
            success: false,
            message: '获取产品价格详情失败'
        });
    }
};

// 创建或更新产品价格
exports.updateProductPrice = async (req, res) => {
    try {
        const { productId } = req.params;
        const priceData = req.body;
        
        // 验证必要字段
        if (!priceData.name || !priceData.basePrice) {
            return res.status(400).json({
                success: false,
                message: '产品名称和基础价格为必填项'
            });
        }
        
        const existingProduct = await memoryStore.findOne('product_prices', { id: productId });
        
        if (existingProduct) {
            // 记录价格变更历史
            if (existingProduct.basePrice !== priceData.basePrice) {
                await memoryStore.insertOne('price_history', {
                    id: generatePriceHistoryId(),
                    productId,
                    oldPrice: existingProduct.basePrice,
                    newPrice: priceData.basePrice,
                    changeReason: priceData.changeReason || '价格调整',
                    changedBy: req.user?.id || 'admin',
                    createdAt: new Date()
                });
            }
            
            // 更新产品价格
            await memoryStore.updateOne('product_prices', 
                { id: productId }, 
                { ...priceData, updatedAt: new Date() }
            );
        } else {
            // 创建新产品价格
            const newProduct = {
                id: productId,
                ...priceData,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            
            await memoryStore.insertOne('product_prices', newProduct);
        }
        
        res.json({
            success: true,
            message: '产品价格更新成功'
        });
    } catch (error) {
        console.error('更新产品价格失败:', error);
        res.status(500).json({
            success: false,
            message: '更新产品价格失败'
        });
    }
};

// 批量更新产品价格
exports.batchUpdatePrices = async (req, res) => {
    try {
        const { updates, updateType, adjustmentValue, adjustmentType } = req.body;
        
        if (!updates || !Array.isArray(updates)) {
            return res.status(400).json({
                success: false,
                message: '更新数据格式错误'
            });
        }
        
        let successCount = 0;
        let failCount = 0;
        
        for (let update of updates) {
            try {
                const product = await memoryStore.findOne('product_prices', { id: update.productId });
                if (!product) {
                    failCount++;
                    continue;
                }
                
                let newPrice = product.basePrice;
                
                if (updateType === 'percentage') {
                    // 按百分比调整
                    if (adjustmentType === 'increase') {
                        newPrice = product.basePrice * (1 + adjustmentValue / 100);
                    } else {
                        newPrice = product.basePrice * (1 - adjustmentValue / 100);
                    }
                } else if (updateType === 'fixed') {
                    // 按固定金额调整
                    if (adjustmentType === 'increase') {
                        newPrice = product.basePrice + adjustmentValue;
                    } else {
                        newPrice = product.basePrice - adjustmentValue;
                    }
                } else if (updateType === 'direct') {
                    // 直接设置价格
                    newPrice = update.newPrice;
                }
                
                // 确保价格不为负数
                newPrice = Math.max(0, newPrice);
                
                // 记录价格变更历史
                await memoryStore.insertOne('price_history', {
                    id: generatePriceHistoryId(),
                    productId: update.productId,
                    oldPrice: product.basePrice,
                    newPrice: newPrice,
                    changeReason: update.reason || '批量价格调整',
                    changedBy: req.user?.id || 'admin',
                    createdAt: new Date()
                });
                
                // 更新产品价格
                await memoryStore.updateOne('product_prices', 
                    { id: update.productId }, 
                    { basePrice: newPrice, updatedAt: new Date() }
                );
                
                successCount++;
            } catch (error) {
                failCount++;
            }
        }
        
        res.json({
            success: true,
            data: {
                successCount,
                failCount,
                total: updates.length
            },
            message: `批量更新完成，成功${successCount}个，失败${failCount}个`
        });
    } catch (error) {
        console.error('批量更新价格失败:', error);
        res.status(500).json({
            success: false,
            message: '批量更新价格失败'
        });
    }
};

// 获取折扣配置
exports.getDiscountConfig = async (req, res) => {
    try {
        const discounts = await memoryStore.findDocuments('discounts', {}, {
            sort: { createdAt: -1 }
        });
        
        res.json({
            success: true,
            data: discounts
        });
    } catch (error) {
        console.error('获取折扣配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取折扣配置失败'
        });
    }
};

// 创建或更新折扣
exports.updateDiscount = async (req, res) => {
    try {
        const { discountId } = req.params;
        const discountData = req.body;
        
        // 验证必要字段
        if (!discountData.name || !discountData.type || !discountData.value) {
            return res.status(400).json({
                success: false,
                message: '折扣名称、类型和数值为必填项'
            });
        }
        
        // 验证日期
        if (discountData.startDate && discountData.endDate) {
            if (new Date(discountData.startDate) >= new Date(discountData.endDate)) {
                return res.status(400).json({
                    success: false,
                    message: '开始时间必须早于结束时间'
                });
            }
        }
        
        const existingDiscount = await memoryStore.findOne('discounts', { id: discountId });
        
        if (existingDiscount) {
            await memoryStore.updateOne('discounts', 
                { id: discountId }, 
                { ...discountData, updatedAt: new Date() }
            );
        } else {
            const newDiscount = {
                id: discountId,
                ...discountData,
                createdAt: new Date(),
                updatedAt: new Date()
            };
            
            await memoryStore.insertOne('discounts', newDiscount);
        }
        
        res.json({
            success: true,
            message: '折扣配置更新成功'
        });
    } catch (error) {
        console.error('更新折扣配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新折扣配置失败'
        });
    }
};

// 删除折扣
exports.deleteDiscount = async (req, res) => {
    try {
        const { discountId } = req.params;
        
        const result = await memoryStore.deleteOne('discounts', { id: discountId });
        
        if (result.deletedCount === 0) {
            return res.status(404).json({
                success: false,
                message: '折扣不存在'
            });
        }
        
        res.json({
            success: true,
            message: '折扣删除成功'
        });
    } catch (error) {
        console.error('删除折扣失败:', error);
        res.status(500).json({
            success: false,
            message: '删除折扣失败'
        });
    }
};

// 获取会员价格配置
exports.getMemberPricing = async (req, res) => {
    try {
        const memberPricing = await memoryStore.findDocuments('member_pricing', {}, {
            sort: { level: 1 }
        });
        
        res.json({
            success: true,
            data: memberPricing
        });
    } catch (error) {
        console.error('获取会员价格配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取会员价格配置失败'
        });
    }
};

// 更新会员价格配置
exports.updateMemberPricing = async (req, res) => {
    try {
        const { memberLevels } = req.body;
        
        if (!memberLevels || !Array.isArray(memberLevels)) {
            return res.status(400).json({
                success: false,
                message: '会员等级配置格式错误'
            });
        }
        
        // 清空现有配置
        await memoryStore.deleteMany('member_pricing', {});
        
        // 插入新配置
        for (let level of memberLevels) {
            await memoryStore.insertOne('member_pricing', {
                id: generateMemberPricingId(),
                ...level,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            message: '会员价格配置更新成功'
        });
    } catch (error) {
        console.error('更新会员价格配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新会员价格配置失败'
        });
    }
};

// 获取价格统计
exports.getPricingStats = async (req, res) => {
    try {
        const products = await memoryStore.findDocuments('product_prices', {});
        const discounts = await memoryStore.findDocuments('discounts', { status: 'active' });
        
        const stats = {
            totalProducts: products.length,
            avgPrice: products.length > 0 ? 
                products.reduce((sum, p) => sum + p.basePrice, 0) / products.length : 0,
            priceRange: {
                min: products.length > 0 ? Math.min(...products.map(p => p.basePrice)) : 0,
                max: products.length > 0 ? Math.max(...products.map(p => p.basePrice)) : 0
            },
            activeDiscounts: discounts.length,
            categoryStats: this.calculateCategoryStats(products),
            recentChanges: await this.getRecentPriceChanges()
        };
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取价格统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取价格统计失败'
        });
    }
};

// 计算产品价格（考虑折扣和会员等级）
exports.calculatePrice = async (req, res) => {
    try {
        const { productId, userId, memberLevel, quantity = 1 } = req.body;
        
        const product = await memoryStore.findOne('product_prices', { id: productId });
        if (!product) {
            return res.status(404).json({
                success: false,
                message: '产品不存在'
            });
        }
        
        let finalPrice = product.basePrice;
        let appliedDiscounts = [];
        
        // 应用会员折扣
        if (memberLevel) {
            const memberPricing = await memoryStore.findOne('member_pricing', { level: memberLevel });
            if (memberPricing) {
                finalPrice = finalPrice * (1 - memberPricing.discountRate);
                appliedDiscounts.push({
                    type: 'member',
                    name: memberPricing.name,
                    discount: memberPricing.discountRate * 100 + '%'
                });
            }
        }
        
        // 应用产品折扣
        const activeDiscounts = await memoryStore.findDocuments('discounts', {
            status: 'active',
            $or: [
                { productIds: productId },
                { applicableToAll: true }
            ],
            startDate: { $lte: new Date() },
            endDate: { $gte: new Date() }
        });
        
        for (let discount of activeDiscounts) {
            if (discount.type === 'percentage') {
                finalPrice = finalPrice * (1 - discount.value / 100);
            } else if (discount.type === 'fixed') {
                finalPrice = Math.max(0, finalPrice - discount.value);
            }
            
            appliedDiscounts.push({
                type: 'product',
                name: discount.name,
                discount: discount.type === 'percentage' ? 
                    discount.value + '%' : '¥' + discount.value
            });
        }
        
        // 数量折扣
        if (quantity >= 10) {
            finalPrice = finalPrice * 0.95; // 10件以上95折
            appliedDiscounts.push({
                type: 'quantity',
                name: '批量折扣',
                discount: '5%'
            });
        }
        
        const totalPrice = finalPrice * quantity;
        const totalDiscount = (product.basePrice * quantity) - totalPrice;
        
        res.json({
            success: true,
            data: {
                productId,
                basePrice: product.basePrice,
                finalPrice: Math.round(finalPrice * 100) / 100,
                quantity,
                totalPrice: Math.round(totalPrice * 100) / 100,
                totalDiscount: Math.round(totalDiscount * 100) / 100,
                discountRate: totalDiscount > 0 ? 
                    Math.round((totalDiscount / (product.basePrice * quantity)) * 10000) / 100 : 0,
                appliedDiscounts
            }
        });
    } catch (error) {
        console.error('计算价格失败:', error);
        res.status(500).json({
            success: false,
            message: '计算价格失败'
        });
    }
};

// 辅助方法
function generatePriceHistoryId() {
    return 'ph_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateMemberPricingId() {
    return 'mp_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 计算分类统计
exports.calculateCategoryStats = function(products) {
    const categoryStats = {};
    
    products.forEach(product => {
        const category = product.category || '未分类';
        if (!categoryStats[category]) {
            categoryStats[category] = {
                count: 0,
                avgPrice: 0,
                totalValue: 0
            };
        }
        
        categoryStats[category].count++;
        categoryStats[category].totalValue += product.basePrice;
        categoryStats[category].avgPrice = categoryStats[category].totalValue / categoryStats[category].count;
    });
    
    return categoryStats;
};

// 获取最近价格变更
exports.getRecentPriceChanges = async function() {
    return await memoryStore.findDocuments('price_history', {}, {
        sort: { createdAt: -1 },
        limit: 10
    });
};

module.exports = exports;
