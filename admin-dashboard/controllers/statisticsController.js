const User = require('../models/User');
const Content = require('../models/Content');
const ApiKey = require('../models/ApiKey');
const Coupon = require('../models/Coupon');
const IpBlacklist = require('../models/IpBlacklist');

/**
 * 获取仪表盘统计数据
 */
exports.getDashboardStats = async (req, res) => {
    try {
        // 获取用户总数
        const userCount = await User.countDocuments();
        
        // 获取内容总数
        const contentCount = await Content.countDocuments();
        
        // 获取API密钥总数
        const apiKeyCount = await ApiKey.countDocuments();
        
        // 获取优惠券总数
        const couponCount = await Coupon.countDocuments();
        
        // 获取黑名单IP总数
        const blacklistedIpCount = await IpBlacklist.countDocuments({ status: 'active' });
        
        // 获取活跃用户数量（最近30天有登录记录）
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const activeUserCount = await User.countDocuments({
            lastLogin: { $gte: thirtyDaysAgo }
        });
        
        // 获取最近发布的内容
        const recentContents = await Content.find({ status: 'published' })
            .select('title type publishedAt')
            .sort({ publishedAt: -1 })
            .limit(5);
        
        // 获取最近创建的用户
        const recentUsers = await User.find()
            .select('username name email createdAt')
            .sort({ createdAt: -1 })
            .limit(5);
        
        return res.status(200).json({
            success: true,
            stats: {
                userCount,
                contentCount,
                apiKeyCount,
                couponCount,
                blacklistedIpCount,
                activeUserCount,
                recentContents,
                recentUsers
            }
        });
    } catch (error) {
        console.error('获取仪表盘统计数据错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 获取用户统计数据
 */
exports.getUserStats = async (req, res) => {
    try {
        // 获取用户总数
        const totalUsers = await User.countDocuments();
        
        // 获取各角色用户数量
        const adminCount = await User.countDocuments({ role: 'admin' });
        const financeAdminCount = await User.countDocuments({ role: 'finance_admin' });
        const contentAdminCount = await User.countDocuments({ role: 'content_admin' });
        const customerServiceCount = await User.countDocuments({ role: 'customer_service' });
        const agentCount = await User.countDocuments({ role: 'agent' });
        
        // 获取各状态用户数量
        const activeCount = await User.countDocuments({ status: 'active' });
        const inactiveCount = await User.countDocuments({ status: 'inactive' });
        const lockedCount = await User.countDocuments({ status: 'locked' });
        
        // 获取启用双因素认证的用户数量
        const twoFactorEnabledCount = await User.countDocuments({ twoFactorEnabled: true });
        
        // 获取最近30天注册的用户数量
        const thirtyDaysAgo = new Date();
        thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
        const recentRegisteredCount = await User.countDocuments({
            createdAt: { $gte: thirtyDaysAgo }
        });
        
        return res.status(200).json({
            success: true,
            stats: {
                totalUsers,
                roleDistribution: {
                    admin: adminCount,
                    financeAdmin: financeAdminCount,
                    contentAdmin: contentAdminCount,
                    customerService: customerServiceCount,
                    agent: agentCount
                },
                statusDistribution: {
                    active: activeCount,
                    inactive: inactiveCount,
                    locked: lockedCount
                },
                twoFactorEnabledCount,
                recentRegisteredCount
            }
        });
    } catch (error) {
        console.error('获取用户统计数据错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 获取财务统计数据（示例数据）
 */
exports.getFinanceStats = async (req, res) => {
    try {
        // 这里只是示例数据，实际应用中应该从数据库获取真实数据
        const financeStats = {
            revenue: {
                total: 10000,
                monthly: 2500,
                weekly: 625,
                daily: 89
            },
            transactions: {
                total: 500,
                monthly: 125,
                weekly: 31,
                daily: 4
            },
            paymentMethods: {
                alipay: 60,
                wechat: 30,
                creditCard: 10
            },
            activeCoupons: await Coupon.countDocuments({ status: 'active' }),
            expiredCoupons: await Coupon.countDocuments({ status: 'expired' })
        };
        
        return res.status(200).json({
            success: true,
            stats: financeStats
        });
    } catch (error) {
        console.error('获取财务统计数据错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
}; 