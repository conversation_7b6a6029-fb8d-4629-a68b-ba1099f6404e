const ApiKey = require('../models/ApiKey');
const crypto = require('crypto');

/**
 * 生成随机API密钥
 */
const generateApiKey = () => {
    return crypto.randomBytes(32).toString('hex');
};

/**
 * 获取所有API密钥
 */
exports.getAllApiKeys = async (req, res) => {
    try {
        // 分页参数
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        
        // 筛选参数
        const filter = {};
        if (req.query.type) filter.type = req.query.type;
        if (req.query.status) filter.status = req.query.status;
        if (req.query.search) {
            filter.$or = [
                { name: { $regex: req.query.search, $options: 'i' } },
                { provider: { $regex: req.query.search, $options: 'i' } }
            ];
        }
        
        // 获取API密钥总数
        const total = await ApiKey.countDocuments(filter);
        
        // 获取API密钥列表
        const apiKeys = await ApiKey.find(filter)
            .populate('createdBy', 'username name')
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });
        
        // 处理API密钥，只显示部分密钥
        const maskedApiKeys = apiKeys.map(key => {
            const keyObj = key.toObject();
            keyObj.key = `${keyObj.key.substring(0, 8)}...${keyObj.key.substring(keyObj.key.length - 8)}`;
            return keyObj;
        });
        
        return res.status(200).json({
            success: true,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            apiKeys: maskedApiKeys
        });
    } catch (error) {
        console.error('获取API密钥列表错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 根据ID获取API密钥
 */
exports.getApiKeyById = async (req, res) => {
    try {
        const apiKey = await ApiKey.findById(req.params.id)
            .populate('createdBy', 'username name');
        
        if (!apiKey) {
            return res.status(404).json({ success: false, message: 'API密钥不存在' });
        }
        
        // 处理API密钥，只显示部分密钥
        const maskedApiKey = apiKey.toObject();
        maskedApiKey.key = `${maskedApiKey.key.substring(0, 8)}...${maskedApiKey.key.substring(maskedApiKey.key.length - 8)}`;
        
        return res.status(200).json({
            success: true,
            apiKey: maskedApiKey
        });
    } catch (error) {
        console.error('获取API密钥详情错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 创建API密钥
 */
exports.createApiKey = async (req, res) => {
    try {
        const { name, type, provider, projectId, services, limit, expiryDate } = req.body;
        
        // 验证必填字段
        if (!name || !type) {
            return res.status(400).json({ success: false, message: '名称和类型为必填项' });
        }
        
        // 创建新API密钥
        const newApiKey = new ApiKey({
            name,
            key: generateApiKey(),
            type,
            provider: provider || '',
            projectId: projectId || '',
            services: services || [],
            limit: limit || '',
            expiryDate: expiryDate || null,
            createdBy: req.session.user.id
        });
        
        await newApiKey.save();
        
        return res.status(201).json({
            success: true,
            message: 'API密钥创建成功',
            apiKey: {
                ...newApiKey.toObject(),
                // 只有在创建时才返回完整的密钥
                fullKey: newApiKey.key
            }
        });
    } catch (error) {
        console.error('创建API密钥错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 更新API密钥
 */
exports.updateApiKey = async (req, res) => {
    try {
        const { name, provider, projectId, services, limit, status, expiryDate } = req.body;
        
        // 查找API密钥
        const apiKey = await ApiKey.findById(req.params.id);
        if (!apiKey) {
            return res.status(404).json({ success: false, message: 'API密钥不存在' });
        }
        
        // 更新API密钥信息
        if (name) apiKey.name = name;
        if (provider !== undefined) apiKey.provider = provider;
        if (projectId !== undefined) apiKey.projectId = projectId;
        if (services) apiKey.services = services;
        if (limit !== undefined) apiKey.limit = limit;
        if (status) apiKey.status = status;
        if (expiryDate !== undefined) apiKey.expiryDate = expiryDate;
        
        await apiKey.save();
        
        // 处理API密钥，只显示部分密钥
        const maskedApiKey = apiKey.toObject();
        maskedApiKey.key = `${maskedApiKey.key.substring(0, 8)}...${maskedApiKey.key.substring(maskedApiKey.key.length - 8)}`;
        
        return res.status(200).json({
            success: true,
            message: 'API密钥更新成功',
            apiKey: maskedApiKey
        });
    } catch (error) {
        console.error('更新API密钥错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 重新生成API密钥
 */
exports.regenerateApiKey = async (req, res) => {
    try {
        // 查找API密钥
        const apiKey = await ApiKey.findById(req.params.id);
        if (!apiKey) {
            return res.status(404).json({ success: false, message: 'API密钥不存在' });
        }
        
        // 生成新的密钥
        const newKey = generateApiKey();
        apiKey.key = newKey;
        
        await apiKey.save();
        
        return res.status(200).json({
            success: true,
            message: 'API密钥重新生成成功',
            apiKey: {
                ...apiKey.toObject(),
                // 只有在重新生成时才返回完整的密钥
                fullKey: newKey
            }
        });
    } catch (error) {
        console.error('重新生成API密钥错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 删除API密钥
 */
exports.deleteApiKey = async (req, res) => {
    try {
        // 查找并删除API密钥
        const apiKey = await ApiKey.findByIdAndDelete(req.params.id);
        
        if (!apiKey) {
            return res.status(404).json({ success: false, message: 'API密钥不存在' });
        }
        
        return res.status(200).json({
            success: true,
            message: 'API密钥删除成功'
        });
    } catch (error) {
        console.error('删除API密钥错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
}; 