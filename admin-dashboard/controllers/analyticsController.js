/**
 * 数据分析控制器
 * 提供高级数据分析、报表生成、趋势分析等功能
 */

const memoryStore = require('../models/memoryStore');

// 获取综合数据分析
exports.getComprehensiveAnalytics = async (req, res) => {
    try {
        const { period = '30d', startDate, endDate } = req.query;
        
        // 计算时间范围
        const timeRange = calculateTimeRange(period, startDate, endDate);
        
        // 获取各类数据
        const userAnalytics = await getUserAnalytics(timeRange);
        const revenueAnalytics = await getRevenueAnalytics(timeRange);
        const agentAnalytics = await getAgentAnalytics(timeRange);
        const apiAnalytics = await getApiAnalytics(timeRange);
        const marketingAnalytics = await getMarketingAnalytics(timeRange);
        
        const analytics = {
            overview: {
                totalUsers: userAnalytics.total,
                activeUsers: userAnalytics.active,
                newUsers: userAnalytics.new,
                totalRevenue: revenueAnalytics.total,
                monthlyRevenue: revenueAnalytics.monthly,
                totalAgents: agentAnalytics.total,
                activeAgents: agentAnalytics.active,
                apiCalls: apiAnalytics.calls,
                errorRate: apiAnalytics.errorRate
            },
            trends: {
                userGrowth: userAnalytics.growth,
                revenueGrowth: revenueAnalytics.growth,
                agentGrowth: agentAnalytics.growth,
                apiUsage: apiAnalytics.usage
            },
            segments: {
                userSegments: userAnalytics.segments,
                revenueSegments: revenueAnalytics.segments,
                agentSegments: agentAnalytics.segments
            },
            predictions: {
                userGrowthPrediction: predictUserGrowth(userAnalytics.growth),
                revenuePrediction: predictRevenue(revenueAnalytics.growth),
                churnPrediction: predictChurn(userAnalytics.retention)
            }
        };
        
        res.json({
            success: true,
            data: analytics
        });
        
    } catch (error) {
        console.error('获取综合数据分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取数据分析失败'
        });
    }
};

// 获取用户行为分析
exports.getUserBehaviorAnalytics = async (req, res) => {
    try {
        const { userId, period = '30d' } = req.query;
        
        const timeRange = calculateTimeRange(period);
        
        let behaviorData;
        
        if (userId) {
            // 单个用户行为分析
            behaviorData = await getSingleUserBehavior(userId, timeRange);
        } else {
            // 整体用户行为分析
            behaviorData = await getOverallUserBehavior(timeRange);
        }
        
        res.json({
            success: true,
            data: behaviorData
        });
        
    } catch (error) {
        console.error('获取用户行为分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取用户行为分析失败'
        });
    }
};

// 获取收入分析
exports.getRevenueAnalytics = async (req, res) => {
    try {
        const { period = '30d', groupBy = 'day' } = req.query;
        
        const timeRange = calculateTimeRange(period);
        const revenueData = await getDetailedRevenueAnalytics(timeRange, groupBy);
        
        res.json({
            success: true,
            data: revenueData
        });
        
    } catch (error) {
        console.error('获取收入分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取收入分析失败'
        });
    }
};

// 获取代理商绩效分析
exports.getAgentPerformanceAnalytics = async (req, res) => {
    try {
        const { agentId, period = '30d' } = req.query;
        
        const timeRange = calculateTimeRange(period);
        
        let performanceData;
        
        if (agentId) {
            // 单个代理商绩效分析
            performanceData = await getSingleAgentPerformance(agentId, timeRange);
        } else {
            // 整体代理商绩效分析
            performanceData = await getOverallAgentPerformance(timeRange);
        }
        
        res.json({
            success: true,
            data: performanceData
        });
        
    } catch (error) {
        console.error('获取代理商绩效分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取代理商绩效分析失败'
        });
    }
};

// 获取营销效果分析
exports.getMarketingEffectivenessAnalytics = async (req, res) => {
    try {
        const { campaignId, period = '30d' } = req.query;
        
        const timeRange = calculateTimeRange(period);
        
        let marketingData;
        
        if (campaignId) {
            // 单个活动效果分析
            marketingData = await getSingleCampaignAnalytics(campaignId, timeRange);
        } else {
            // 整体营销效果分析
            marketingData = await getOverallMarketingAnalytics(timeRange);
        }
        
        res.json({
            success: true,
            data: marketingData
        });
        
    } catch (error) {
        console.error('获取营销效果分析错误:', error);
        res.status(500).json({
            success: false,
            message: '获取营销效果分析失败'
        });
    }
};

// 生成自定义报表
exports.generateCustomReport = async (req, res) => {
    try {
        const { 
            reportType, 
            metrics, 
            dimensions, 
            filters, 
            period, 
            format = 'json' 
        } = req.body;
        
        const timeRange = calculateTimeRange(period);
        
        // 根据报表类型生成数据
        let reportData;
        
        switch (reportType) {
            case 'user_report':
                reportData = await generateUserReport(metrics, dimensions, filters, timeRange);
                break;
            case 'revenue_report':
                reportData = await generateRevenueReport(metrics, dimensions, filters, timeRange);
                break;
            case 'agent_report':
                reportData = await generateAgentReport(metrics, dimensions, filters, timeRange);
                break;
            case 'marketing_report':
                reportData = await generateMarketingReport(metrics, dimensions, filters, timeRange);
                break;
            default:
                return res.status(400).json({
                    success: false,
                    message: '不支持的报表类型'
                });
        }
        
        // 根据格式返回数据
        if (format === 'csv') {
            const csv = convertToCSV(reportData);
            res.setHeader('Content-Type', 'text/csv');
            res.setHeader('Content-Disposition', 'attachment; filename=report.csv');
            res.send(csv);
        } else {
            res.json({
                success: true,
                data: reportData
            });
        }
        
    } catch (error) {
        console.error('生成自定义报表错误:', error);
        res.status(500).json({
            success: false,
            message: '生成报表失败'
        });
    }
};

// 辅助函数：计算时间范围
function calculateTimeRange(period, startDate, endDate) {
    const now = new Date();
    let start, end;
    
    if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
    } else {
        end = now;
        
        switch (period) {
            case '7d':
                start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            case '1y':
                start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
                break;
            default:
                start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
    }
    
    return { start, end };
}

// 辅助函数：获取用户分析数据
async function getUserAnalytics(timeRange) {
    const users = await memoryStore.findDocuments('users', {});
    
    const total = users.length;
    const active = users.filter(user => user.status === 'active').length;
    const newUsers = users.filter(user => 
        new Date(user.createdAt) >= timeRange.start
    ).length;
    
    // 模拟增长趋势数据
    const growth = generateTrendData(timeRange, 'user_growth');
    
    // 用户分段
    const segments = {
        byRole: users.reduce((acc, user) => {
            acc[user.role] = (acc[user.role] || 0) + 1;
            return acc;
        }, {}),
        byStatus: users.reduce((acc, user) => {
            acc[user.status] = (acc[user.status] || 0) + 1;
            return acc;
        }, {})
    };
    
    return {
        total,
        active,
        new: newUsers,
        growth,
        segments,
        retention: calculateRetention(users, timeRange)
    };
}

// 辅助函数：获取收入分析数据
async function getRevenueAnalytics(timeRange) {
    const transactions = await memoryStore.findDocuments('transactions', {});
    
    const filteredTransactions = transactions.filter(t => 
        new Date(t.createdAt) >= timeRange.start && 
        new Date(t.createdAt) <= timeRange.end
    );
    
    const total = filteredTransactions.reduce((sum, t) => sum + t.amount, 0);
    const monthly = filteredTransactions
        .filter(t => new Date(t.createdAt) >= new Date(Date.now() - 30 * 24 * 60 * 60 * 1000))
        .reduce((sum, t) => sum + t.amount, 0);
    
    const growth = generateTrendData(timeRange, 'revenue_growth');
    
    const segments = {
        byPaymentMethod: filteredTransactions.reduce((acc, t) => {
            acc[t.paymentMethod] = (acc[t.paymentMethod] || 0) + t.amount;
            return acc;
        }, {}),
        byAmount: {
            small: filteredTransactions.filter(t => t.amount < 100).length,
            medium: filteredTransactions.filter(t => t.amount >= 100 && t.amount < 1000).length,
            large: filteredTransactions.filter(t => t.amount >= 1000).length
        }
    };
    
    return {
        total,
        monthly,
        growth,
        segments
    };
}

// 辅助函数：获取代理商分析数据
async function getAgentAnalytics(timeRange) {
    const agents = await memoryStore.findDocuments('agents', {});
    
    const total = agents.length;
    const active = agents.filter(agent => agent.status === 'active').length;
    
    const growth = generateTrendData(timeRange, 'agent_growth');
    
    const segments = {
        byLevel: agents.reduce((acc, agent) => {
            acc[agent.level] = (acc[agent.level] || 0) + 1;
            return acc;
        }, {}),
        byPerformance: {
            high: agents.filter(a => a.totalSales > 10000).length,
            medium: agents.filter(a => a.totalSales >= 1000 && a.totalSales <= 10000).length,
            low: agents.filter(a => a.totalSales < 1000).length
        }
    };
    
    return {
        total,
        active,
        growth,
        segments
    };
}

// 辅助函数：获取API分析数据
async function getApiAnalytics(timeRange) {
    // 模拟API统计数据
    return {
        calls: Math.floor(Math.random() * 100000) + 50000,
        errorRate: (Math.random() * 5).toFixed(2),
        usage: generateTrendData(timeRange, 'api_usage')
    };
}

// 辅助函数：获取营销分析数据
async function getMarketingAnalytics(timeRange) {
    const coupons = await memoryStore.findDocuments('coupons', {});
    const campaigns = await memoryStore.findDocuments('campaigns', {});
    
    return {
        activeCoupons: coupons.filter(c => c.status === 'active').length,
        activeCampaigns: campaigns.filter(c => c.status === 'active').length,
        totalBudget: campaigns.reduce((sum, c) => sum + (c.budget || 0), 0),
        totalSpent: campaigns.reduce((sum, c) => sum + (c.spent || 0), 0)
    };
}

// 辅助函数：生成趋势数据
function generateTrendData(timeRange, type) {
    const days = Math.ceil((timeRange.end - timeRange.start) / (24 * 60 * 60 * 1000));
    const data = [];
    
    for (let i = 0; i < days; i++) {
        const date = new Date(timeRange.start.getTime() + i * 24 * 60 * 60 * 1000);
        let value;
        
        switch (type) {
            case 'user_growth':
                value = Math.floor(Math.random() * 50) + 10;
                break;
            case 'revenue_growth':
                value = Math.floor(Math.random() * 5000) + 1000;
                break;
            case 'agent_growth':
                value = Math.floor(Math.random() * 10) + 1;
                break;
            case 'api_usage':
                value = Math.floor(Math.random() * 10000) + 5000;
                break;
            default:
                value = Math.floor(Math.random() * 100);
        }
        
        data.push({
            date: date.toISOString().split('T')[0],
            value
        });
    }
    
    return data;
}

// 辅助函数：计算用户留存率
function calculateRetention(users, timeRange) {
    // 简化的留存率计算
    const activeUsers = users.filter(user => user.status === 'active').length;
    const totalUsers = users.length;
    
    return totalUsers > 0 ? ((activeUsers / totalUsers) * 100).toFixed(2) : 0;
}

// 辅助函数：预测用户增长
function predictUserGrowth(growthData) {
    if (growthData.length < 7) return null;
    
    const recentGrowth = growthData.slice(-7);
    const avgGrowth = recentGrowth.reduce((sum, item) => sum + item.value, 0) / recentGrowth.length;
    
    return {
        nextWeek: Math.round(avgGrowth * 7),
        nextMonth: Math.round(avgGrowth * 30),
        confidence: 0.75
    };
}

// 辅助函数：预测收入
function predictRevenue(growthData) {
    if (growthData.length < 7) return null;
    
    const recentGrowth = growthData.slice(-7);
    const avgGrowth = recentGrowth.reduce((sum, item) => sum + item.value, 0) / recentGrowth.length;
    
    return {
        nextWeek: Math.round(avgGrowth * 7),
        nextMonth: Math.round(avgGrowth * 30),
        confidence: 0.70
    };
}

// 辅助函数：预测流失率
function predictChurn(retentionRate) {
    const churnRate = 100 - parseFloat(retentionRate);
    
    return {
        currentChurn: churnRate.toFixed(2),
        predictedChurn: (churnRate * 1.1).toFixed(2), // 假设略有增长
        riskLevel: churnRate > 20 ? 'high' : churnRate > 10 ? 'medium' : 'low'
    };
}

// 辅助函数：转换为CSV格式
function convertToCSV(data) {
    if (!Array.isArray(data) || data.length === 0) {
        return '';
    }
    
    const headers = Object.keys(data[0]);
    const csvContent = [
        headers.join(','),
        ...data.map(row => headers.map(header => row[header]).join(','))
    ].join('\n');
    
    return csvContent;
}

module.exports = exports;
