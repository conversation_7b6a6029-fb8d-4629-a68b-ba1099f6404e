/**
 * 角色管理控制器
 * 处理角色CRUD操作、权限分配、角色层级管理等功能
 */

const memoryStore = require('../models/memoryStore');

// 获取所有角色
exports.getRoles = async (req, res) => {
    try {
        const { page = 1, limit = 20, search = '', status = '' } = req.query;
        
        let roles = await memoryStore.findDocuments('roles', {});
        
        // 搜索过滤
        if (search) {
            roles = roles.filter(role => 
                role.name.toLowerCase().includes(search.toLowerCase()) ||
                role.description.toLowerCase().includes(search.toLowerCase())
            );
        }
        
        // 状态过滤
        if (status) {
            roles = roles.filter(role => role.status === status);
        }
        
        // 分页处理
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedRoles = roles.slice(startIndex, endIndex);
        
        const pagination = {
            page: parseInt(page),
            pages: Math.ceil(roles.length / limit),
            total: roles.length,
            limit: parseInt(limit)
        };
        
        res.json({
            success: true,
            data: paginatedRoles,
            pagination
        });
        
    } catch (error) {
        console.error('获取角色列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取角色列表失败'
        });
    }
};

// 获取角色详情
exports.getRoleDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const role = await memoryStore.findOneDocument('roles', { _id: id });
        
        if (!role) {
            return res.status(404).json({
                success: false,
                message: '角色不存在'
            });
        }
        
        res.json({
            success: true,
            data: role
        });
        
    } catch (error) {
        console.error('获取角色详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取角色详情失败'
        });
    }
};

// 创建角色
exports.createRole = async (req, res) => {
    try {
        const { name, description, permissions, level, status } = req.body;
        
        // 验证必填字段
        if (!name || !description) {
            return res.status(400).json({
                success: false,
                message: '角色名称和描述为必填项'
            });
        }
        
        // 检查角色名称是否已存在
        const existingRole = await memoryStore.findOneDocument('roles', { name });
        if (existingRole) {
            return res.status(400).json({
                success: false,
                message: '角色名称已存在'
            });
        }
        
        const roleData = {
            name,
            description,
            permissions: permissions || [],
            level: level || 1,
            status: status || 'active',
            userCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const newRole = await memoryStore.createDocument('roles', roleData);
        
        res.status(201).json({
            success: true,
            data: newRole,
            message: '角色创建成功'
        });
        
    } catch (error) {
        console.error('创建角色错误:', error);
        res.status(500).json({
            success: false,
            message: '创建角色失败'
        });
    }
};

// 更新角色
exports.updateRole = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description, permissions, level, status } = req.body;
        
        const existingRole = await memoryStore.findOneDocument('roles', { _id: id });
        if (!existingRole) {
            return res.status(404).json({
                success: false,
                message: '角色不存在'
            });
        }
        
        // 如果修改了角色名称，检查是否与其他角色重复
        if (name && name !== existingRole.name) {
            const duplicateRole = await memoryStore.findOneDocument('roles', { name });
            if (duplicateRole) {
                return res.status(400).json({
                    success: false,
                    message: '角色名称已存在'
                });
            }
        }
        
        const updateData = {
            updatedAt: new Date()
        };
        
        if (name) updateData.name = name;
        if (description) updateData.description = description;
        if (permissions) updateData.permissions = permissions;
        if (level) updateData.level = level;
        if (status) updateData.status = status;
        
        const updatedRole = await memoryStore.updateDocument('roles', id, updateData);
        
        res.json({
            success: true,
            data: updatedRole,
            message: '角色更新成功'
        });
        
    } catch (error) {
        console.error('更新角色错误:', error);
        res.status(500).json({
            success: false,
            message: '更新角色失败'
        });
    }
};

// 删除角色
exports.deleteRole = async (req, res) => {
    try {
        const { id } = req.params;
        
        const role = await memoryStore.findOneDocument('roles', { _id: id });
        if (!role) {
            return res.status(404).json({
                success: false,
                message: '角色不存在'
            });
        }
        
        // 检查是否有用户使用此角色
        const usersWithRole = await memoryStore.findDocuments('users', { role: role.name });
        if (usersWithRole.length > 0) {
            return res.status(400).json({
                success: false,
                message: `无法删除角色，还有 ${usersWithRole.length} 个用户使用此角色`
            });
        }
        
        await memoryStore.deleteDocument('roles', id);
        
        res.json({
            success: true,
            message: '角色删除成功'
        });
        
    } catch (error) {
        console.error('删除角色错误:', error);
        res.status(500).json({
            success: false,
            message: '删除角色失败'
        });
    }
};

// 获取权限树
exports.getPermissionTree = async (req, res) => {
    try {
        const permissionTree = {
            system: {
                label: '系统管理',
                permissions: {
                    'system.view': '查看系统设置',
                    'system.edit': '编辑系统设置',
                    'system.security': '安全设置管理',
                    'system.api': 'API设置管理',
                    'system.backup': '数据备份管理'
                }
            },
            user: {
                label: '用户管理',
                permissions: {
                    'user.view': '查看用户',
                    'user.add': '添加用户',
                    'user.edit': '编辑用户',
                    'user.delete': '删除用户',
                    'user.export': '导出用户数据',
                    'user.import': '导入用户数据'
                }
            },
            role: {
                label: '角色管理',
                permissions: {
                    'role.view': '查看角色',
                    'role.add': '添加角色',
                    'role.edit': '编辑角色',
                    'role.delete': '删除角色',
                    'role.assign': '分配角色权限'
                }
            },
            agent: {
                label: '代理商管理',
                permissions: {
                    'agent.view': '查看代理商',
                    'agent.add': '添加代理商',
                    'agent.edit': '编辑代理商',
                    'agent.delete': '删除代理商',
                    'agent.commission': '佣金设置',
                    'agent.approve': '审批代理申请'
                }
            },
            finance: {
                label: '财务管理',
                permissions: {
                    'finance.view': '查看财务数据',
                    'finance.transaction': '交易管理',
                    'finance.refund': '退款管理',
                    'finance.report': '财务报表',
                    'finance.withdraw': '提现管理'
                }
            },
            content: {
                label: '内容管理',
                permissions: {
                    'content.view': '查看内容',
                    'content.add': '添加内容',
                    'content.edit': '编辑内容',
                    'content.delete': '删除内容',
                    'content.publish': '发布内容'
                }
            },
            marketing: {
                label: '营销工具',
                permissions: {
                    'marketing.view': '查看营销数据',
                    'marketing.coupon': '优惠券管理',
                    'marketing.campaign': '活动管理',
                    'marketing.referral': '邀请返利管理',
                    'marketing.points': '积分系统管理'
                }
            },
            security: {
                label: '安全中心',
                permissions: {
                    'security.view': '查看安全日志',
                    'security.rules': '风控规则管理',
                    'security.blacklist': 'IP黑名单管理',
                    'security.approval': '敏感操作审批',
                    'security.backup': '数据备份管理'
                }
            }
        };
        
        res.json({
            success: true,
            data: permissionTree
        });
        
    } catch (error) {
        console.error('获取权限树错误:', error);
        res.status(500).json({
            success: false,
            message: '获取权限树失败'
        });
    }
};

// 复制角色
exports.copyRole = async (req, res) => {
    try {
        const { id } = req.params;
        const { name, description } = req.body;
        
        const sourceRole = await memoryStore.findOneDocument('roles', { _id: id });
        if (!sourceRole) {
            return res.status(404).json({
                success: false,
                message: '源角色不存在'
            });
        }
        
        // 检查新角色名称是否已存在
        const existingRole = await memoryStore.findOneDocument('roles', { name });
        if (existingRole) {
            return res.status(400).json({
                success: false,
                message: '角色名称已存在'
            });
        }
        
        const newRoleData = {
            name,
            description: description || `${sourceRole.description} (副本)`,
            permissions: [...sourceRole.permissions],
            level: sourceRole.level,
            status: 'active',
            userCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const newRole = await memoryStore.createDocument('roles', newRoleData);
        
        res.status(201).json({
            success: true,
            data: newRole,
            message: '角色复制成功'
        });
        
    } catch (error) {
        console.error('复制角色错误:', error);
        res.status(500).json({
            success: false,
            message: '复制角色失败'
        });
    }
};

// 批量更新角色状态
exports.batchUpdateRoleStatus = async (req, res) => {
    try {
        const { roleIds, status } = req.body;
        
        if (!roleIds || !Array.isArray(roleIds) || roleIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择要更新的角色'
            });
        }
        
        if (!['active', 'inactive'].includes(status)) {
            return res.status(400).json({
                success: false,
                message: '无效的状态值'
            });
        }
        
        const updatePromises = roleIds.map(id => 
            memoryStore.updateDocument('roles', id, {
                status,
                updatedAt: new Date()
            })
        );
        
        await Promise.all(updatePromises);
        
        res.json({
            success: true,
            message: `成功更新 ${roleIds.length} 个角色的状态`
        });
        
    } catch (error) {
        console.error('批量更新角色状态错误:', error);
        res.status(500).json({
            success: false,
            message: '批量更新角色状态失败'
        });
    }
};

module.exports = exports;
