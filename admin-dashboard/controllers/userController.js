const User = require('../models/User');

/**
 * 获取所有用户
 */
exports.getAllUsers = async (req, res) => {
    try {
        // 分页参数
        const page = parseInt(req.query.page) || 1;
        const limit = parseInt(req.query.limit) || 10;
        const skip = (page - 1) * limit;
        
        // 筛选参数
        const filter = {};
        if (req.query.role) filter.role = req.query.role;
        if (req.query.status) filter.status = req.query.status;
        if (req.query.search) {
            filter.$or = [
                { username: { $regex: req.query.search, $options: 'i' } },
                { name: { $regex: req.query.search, $options: 'i' } },
                { email: { $regex: req.query.search, $options: 'i' } }
            ];
        }
        
        // 获取用户总数
        const total = await User.countDocuments(filter);
        
        // 获取用户列表
        const users = await User.find(filter)
            .select('-password -twoFactorSecret')
            .skip(skip)
            .limit(limit)
            .sort({ createdAt: -1 });
        
        return res.status(200).json({
            success: true,
            total,
            page,
            limit,
            totalPages: Math.ceil(total / limit),
            users
        });
    } catch (error) {
        console.error('获取用户列表错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 根据ID获取用户
 */
exports.getUserById = async (req, res) => {
    try {
        const user = await User.findById(req.params.id)
            .select('-password -twoFactorSecret');
        
        if (!user) {
            return res.status(404).json({ success: false, message: '用户不存在' });
        }
        
        return res.status(200).json({
            success: true,
            user
        });
    } catch (error) {
        console.error('获取用户详情错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 创建用户
 */
exports.createUser = async (req, res) => {
    try {
        const { username, password, name, email, role } = req.body;
        
        // 验证必填字段
        if (!username || !password || !name || !email) {
            return res.status(400).json({ success: false, message: '用户名、密码、姓名和邮箱为必填项' });
        }
        
        // 检查用户名和邮箱是否已存在
        const existingUser = await User.findOne({ $or: [{ username }, { email }] });
        if (existingUser) {
            if (existingUser.username === username) {
                return res.status(400).json({ success: false, message: '用户名已被使用' });
            }
            return res.status(400).json({ success: false, message: '邮箱已被使用' });
        }
        
        // 创建新用户
        const newUser = new User({
            username,
            password, // 在实际应用中，应该对密码进行哈希处理
            name,
            email,
            role: role || 'customer_service'
        });
        
        await newUser.save();
        
        // 返回创建的用户（不包含密码和2FA密钥）
        const user = newUser.toObject();
        delete user.password;
        delete user.twoFactorSecret;
        
        return res.status(201).json({
            success: true,
            message: '用户创建成功',
            user
        });
    } catch (error) {
        console.error('创建用户错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 更新用户
 */
exports.updateUser = async (req, res) => {
    try {
        const { name, email, role, status, avatar } = req.body;
        
        // 查找用户
        const user = await User.findById(req.params.id);
        if (!user) {
            return res.status(404).json({ success: false, message: '用户不存在' });
        }
        
        // 更新用户信息
        if (name) user.name = name;
        if (email) user.email = email;
        if (role) user.role = role;
        if (status) user.status = status;
        if (avatar) user.avatar = avatar;
        
        // 如果提供了新密码，则更新密码
        if (req.body.password) {
            user.password = req.body.password; // 在实际应用中，应该对密码进行哈希处理
        }
        
        await user.save();
        
        // 返回更新后的用户（不包含密码和2FA密钥）
        const updatedUser = user.toObject();
        delete updatedUser.password;
        delete updatedUser.twoFactorSecret;
        
        return res.status(200).json({
            success: true,
            message: '用户更新成功',
            user: updatedUser
        });
    } catch (error) {
        console.error('更新用户错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 删除用户
 */
exports.deleteUser = async (req, res) => {
    try {
        // 查找并删除用户
        const user = await User.findByIdAndDelete(req.params.id);
        
        if (!user) {
            return res.status(404).json({ success: false, message: '用户不存在' });
        }
        
        return res.status(200).json({
            success: true,
            message: '用户删除成功'
        });
    } catch (error) {
        console.error('删除用户错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
}; 