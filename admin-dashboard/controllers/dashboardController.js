const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: 'localhost',
    user: 'writerprocn',
    password: 'wan521521',
    database: 'writerprocn',
    charset: 'utf8mb4'
};

class DashboardController {
    // 获取仪表盘统计数据
    async getStats(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            // 获取用户统计
            const [userStats] = await connection.execute(`
                SELECT 
                    COUNT(*) as totalUsers,
                    COUNT(CASE WHEN status = 'active' THEN 1 END) as activeUsers,
                    COUNT(CASE WHEN DATE(createdAt) = CURDATE() THEN 1 END) as todayUsers,
                    COUNT(CASE WHEN DATE(createdAt) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY) THEN 1 END) as weekUsers
                FROM User
            `);

            // 获取订单统计
            const [orderStats] = await connection.execute(`
                SELECT 
                    COUNT(*) as totalOrders,
                    COUNT(CASE WHEN status = 'paid' THEN 1 END) as paidOrders,
                    COUNT(CASE WHEN DATE(createdAt) = CURDATE() THEN 1 END) as todayOrders,
                    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as totalRevenue,
                    SUM(CASE WHEN status = 'paid' AND DATE(createdAt) = CURDATE() THEN amount ELSE 0 END) as todayRevenue
                FROM \`Order\`
            `);

            // 获取文档统计
            const [docStats] = await connection.execute(`
                SELECT 
                    COUNT(*) as totalDocuments,
                    COUNT(CASE WHEN optimizedContent IS NOT NULL THEN 1 END) as optimizedDocuments,
                    COUNT(CASE WHEN DATE(createdAt) = CURDATE() THEN 1 END) as todayDocuments
                FROM Document
            `);

            // 获取支付统计
            const [paymentStats] = await connection.execute(`
                SELECT 
                    COUNT(*) as totalPayments,
                    COUNT(CASE WHEN status = 'completed' THEN 1 END) as successPayments,
                    COUNT(CASE WHEN paymentMethod = 'alipay' THEN 1 END) as alipayPayments,
                    COUNT(CASE WHEN paymentMethod = 'wechat' THEN 1 END) as wechatPayments,
                    SUM(CASE WHEN status = 'completed' THEN amount ELSE 0 END) as totalPaymentAmount
                FROM Payment
            `);

            // 获取最近7天的收入趋势
            const [revenueTrend] = await connection.execute(`
                SELECT 
                    DATE(createdAt) as date,
                    SUM(CASE WHEN status = 'paid' THEN amount ELSE 0 END) as revenue,
                    COUNT(CASE WHEN status = 'paid' THEN 1 END) as orders
                FROM \`Order\`
                WHERE DATE(createdAt) >= DATE_SUB(CURDATE(), INTERVAL 7 DAY)
                GROUP BY DATE(createdAt)
                ORDER BY date ASC
            `);

            // 获取最近活动
            const [recentActivities] = await connection.execute(`
                (SELECT 'user_register' as type, u.username as title, u.createdAt as time, 'success' as status
                 FROM User u ORDER BY u.createdAt DESC LIMIT 5)
                UNION ALL
                (SELECT 'order_paid' as type, CONCAT('订单 #', o.id) as title, o.updatedAt as time, 'success' as status
                 FROM \`Order\` o WHERE o.status = 'paid' ORDER BY o.updatedAt DESC LIMIT 5)
                UNION ALL
                (SELECT 'document_optimized' as type, d.title as title, d.updatedAt as time, 'info' as status
                 FROM Document d WHERE d.optimizedContent IS NOT NULL ORDER BY d.updatedAt DESC LIMIT 5)
                ORDER BY time DESC LIMIT 10
            `);

            res.json({
                success: true,
                data: {
                    users: userStats[0],
                    orders: orderStats[0],
                    documents: docStats[0],
                    payments: paymentStats[0],
                    revenueTrend: revenueTrend,
                    recentActivities: recentActivities
                }
            });

        } catch (error) {
            console.error('获取仪表盘数据失败:', error);
            res.status(500).json({
                success: false,
                message: '获取仪表盘数据失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取用户列表
    async getUsers(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { page = 1, limit = 10, search = '', status = '' } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            let params = [];

            if (search) {
                whereClause += ' AND (username LIKE ? OR email LIKE ?)';
                params.push(`%${search}%`, `%${search}%`);
            }

            if (status) {
                whereClause += ' AND status = ?';
                params.push(status);
            }

            // 获取用户列表
            const [users] = await connection.execute(`
                SELECT 
                    id, username, email, phone, status, balance, totalConsumption,
                    createdAt, lastLoginAt, lastLoginIp
                FROM User 
                ${whereClause}
                ORDER BY createdAt DESC 
                LIMIT ? OFFSET ?
            `, [...params, parseInt(limit), offset]);

            // 获取总数
            const [countResult] = await connection.execute(`
                SELECT COUNT(*) as total FROM User ${whereClause}
            `, params);

            res.json({
                success: true,
                data: {
                    users: users,
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('获取用户列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取用户列表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取订单列表
    async getOrders(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { page = 1, limit = 10, status = '' } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            let params = [];

            if (status) {
                whereClause += ' AND o.status = ?';
                params.push(status);
            }

            // 获取订单列表
            const [orders] = await connection.execute(`
                SELECT 
                    o.id, o.amount, o.status, o.description, o.createdAt, o.updatedAt,
                    u.username, u.email
                FROM \`Order\` o
                LEFT JOIN User u ON o.userId = u.id
                ${whereClause}
                ORDER BY o.createdAt DESC 
                LIMIT ? OFFSET ?
            `, [...params, parseInt(limit), offset]);

            // 获取总数
            const [countResult] = await connection.execute(`
                SELECT COUNT(*) as total FROM \`Order\` o ${whereClause}
            `, params);

            res.json({
                success: true,
                data: {
                    orders: orders,
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('获取订单列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取订单列表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取支付列表
    async getPayments(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);
            
            const { page = 1, limit = 10, method = '', status = '' } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            let params = [];

            if (method) {
                whereClause += ' AND p.paymentMethod = ?';
                params.push(method);
            }

            if (status) {
                whereClause += ' AND p.status = ?';
                params.push(status);
            }

            // 获取支付列表
            const [payments] = await connection.execute(`
                SELECT 
                    p.id, p.orderId, p.amount, p.paymentMethod, p.status, 
                    p.transactionId, p.createdAt, p.paidAt,
                    u.username, u.email
                FROM Payment p
                LEFT JOIN User u ON p.userId = u.id
                ${whereClause}
                ORDER BY p.createdAt DESC 
                LIMIT ? OFFSET ?
            `, [...params, parseInt(limit), offset]);

            // 获取总数
            const [countResult] = await connection.execute(`
                SELECT COUNT(*) as total FROM Payment p ${whereClause}
            `, params);

            res.json({
                success: true,
                data: {
                    payments: payments,
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('获取支付列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取支付列表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取工单列表
    async getWorkOrders(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);

            const { page = 1, limit = 10, status = '', serviceType = '' } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            let params = [];

            if (status) {
                whereClause += ' AND w.status = ?';
                params.push(status);
            }

            if (serviceType) {
                whereClause += ' AND w.serviceType = ?';
                params.push(serviceType);
            }

            // 获取工单列表
            const [workOrders] = await connection.execute(`
                SELECT
                    w.id, w.title, w.status, w.priority, w.serviceType,
                    w.createdAt, w.assignedAt, w.deadline,
                    u.username as clientName, u.email as clientEmail,
                    e.realName as expertName, e.expertCode,
                    o.amount as orderAmount
                FROM WorkOrder w
                LEFT JOIN User u ON w.userId = u.id
                LEFT JOIN Expert e ON w.expertId = e.id
                LEFT JOIN \`Order\` o ON w.orderId = o.id
                ${whereClause}
                ORDER BY w.createdAt DESC
                LIMIT ? OFFSET ?
            `, [...params, parseInt(limit), offset]);

            // 获取总数
            const [countResult] = await connection.execute(`
                SELECT COUNT(*) as total FROM WorkOrder w ${whereClause}
            `, params);

            res.json({
                success: true,
                data: {
                    workOrders: workOrders,
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('获取工单列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取工单列表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }

    // 获取专家列表
    async getExperts(req, res) {
        let connection;
        try {
            connection = await mysql.createConnection(dbConfig);

            const { page = 1, limit = 10, status = '' } = req.query;
            const offset = (page - 1) * limit;

            let whereClause = 'WHERE 1=1';
            let params = [];

            if (status) {
                whereClause += ' AND e.status = ?';
                params.push(status);
            }

            // 获取专家列表
            const [experts] = await connection.execute(`
                SELECT
                    e.id, e.expertCode, e.realName, e.title, e.status,
                    e.currentOrders, e.maxConcurrentOrders, e.totalOrders,
                    e.completedOrders, e.averageRating, e.createdAt,
                    u.username, u.email
                FROM Expert e
                LEFT JOIN User u ON e.userId = u.id
                ${whereClause}
                ORDER BY e.createdAt DESC
                LIMIT ? OFFSET ?
            `, [...params, parseInt(limit), offset]);

            // 获取总数
            const [countResult] = await connection.execute(`
                SELECT COUNT(*) as total FROM Expert e ${whereClause}
            `, params);

            res.json({
                success: true,
                data: {
                    experts: experts,
                    total: countResult[0].total,
                    page: parseInt(page),
                    limit: parseInt(limit)
                }
            });

        } catch (error) {
            console.error('获取专家列表失败:', error);
            res.status(500).json({
                success: false,
                message: '获取专家列表失败',
                error: error.message
            });
        } finally {
            if (connection) {
                await connection.end();
            }
        }
    }
}

module.exports = new DashboardController();
