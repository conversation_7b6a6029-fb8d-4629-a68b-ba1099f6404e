const memoryStore = require('../models/memoryStore');
const path = require('path');

/**
 * 内容管理控制器
 * 处理协议管理、公告管理、客服二维码、页面内容编辑等功能
 */

// 获取协议列表
exports.getAgreements = async (req, res) => {
    try {
        const agreements = await memoryStore.findDocuments('agreements');

        res.json({
            success: true,
            data: agreements
        });

    } catch (error) {
        console.error('获取协议列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取协议列表失败'
        });
    }
};

// 创建协议
exports.createAgreement = async (req, res) => {
    try {
        const agreementData = {
            ...req.body,
            status: 'draft',
            version: req.body.version || 'v1.0',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const agreement = await memoryStore.createDocument('agreements', agreementData);

        res.json({
            success: true,
            data: agreement,
            message: '协议创建成功'
        });

    } catch (error) {
        console.error('创建协议错误:', error);
        res.status(500).json({
            success: false,
            message: '创建协议失败'
        });
    }
};

// 更新协议
exports.updateAgreement = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const agreement = await memoryStore.updateDocument('agreements', id, updateData);

        if (!agreement) {
            return res.status(404).json({
                success: false,
                message: '协议不存在'
            });
        }

        res.json({
            success: true,
            data: agreement,
            message: '协议更新成功'
        });

    } catch (error) {
        console.error('更新协议错误:', error);
        res.status(500).json({
            success: false,
            message: '更新协议失败'
        });
    }
};

// 获取公告列表
exports.getAnnouncements = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, type } = req.query;

        let announcements = await memoryStore.findDocuments('announcements');

        // 状态过滤
        if (status) {
            announcements = announcements.filter(a => a.status === status);
        }

        // 类型过滤
        if (type) {
            announcements = announcements.filter(a => a.type === type);
        }

        // 排序
        announcements.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedAnnouncements = announcements.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: paginatedAnnouncements,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: announcements.length,
                pages: Math.ceil(announcements.length / limit)
            }
        });

    } catch (error) {
        console.error('获取公告列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取公告列表失败'
        });
    }
};

// 创建公告
exports.createAnnouncement = async (req, res) => {
    try {
        const announcementData = {
            ...req.body,
            status: 'published',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const announcement = await memoryStore.createDocument('announcements', announcementData);

        res.json({
            success: true,
            data: announcement,
            message: '公告发布成功'
        });

    } catch (error) {
        console.error('创建公告错误:', error);
        res.status(500).json({
            success: false,
            message: '创建公告失败'
        });
    }
};

// 更新公告
exports.updateAnnouncement = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const announcement = await memoryStore.updateDocument('announcements', id, updateData);

        if (!announcement) {
            return res.status(404).json({
                success: false,
                message: '公告不存在'
            });
        }

        res.json({
            success: true,
            data: announcement,
            message: '公告更新成功'
        });

    } catch (error) {
        console.error('更新公告错误:', error);
        res.status(500).json({
            success: false,
            message: '更新公告失败'
        });
    }
};

// 删除公告
exports.deleteAnnouncement = async (req, res) => {
    try {
        const { id } = req.params;

        const success = await memoryStore.deleteDocument('announcements', id);

        if (!success) {
            return res.status(404).json({
                success: false,
                message: '公告不存在'
            });
        }

        res.json({
            success: true,
            message: '公告删除成功'
        });

    } catch (error) {
        console.error('删除公告错误:', error);
        res.status(500).json({
            success: false,
            message: '删除公告失败'
        });
    }
};

// 获取客服二维码设置
exports.getQRCodeSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'qrcode' });

        if (!settings) {
            // 创建默认客服二维码设置
            settings = await memoryStore.createDocument('settings', {
                type: 'qrcode',
                data: {
                    qrcodeType: 'wechat',
                    qrcodeName: '在线客服',
                    qrcodeContact: 'service_wechat',
                    serviceTime: '9:00-18:00',
                    qrcodeEnabled: true,
                    qrcodeImage: '/images/default-qrcode.png'
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data
        });

    } catch (error) {
        console.error('获取客服二维码设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取客服二维码设置失败'
        });
    }
};

// 更新客服二维码设置
exports.updateQRCodeSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'qrcode' });

        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'qrcode',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data,
            message: '客服二维码设置更新成功'
        });

    } catch (error) {
        console.error('更新客服二维码设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新客服二维码设置失败'
        });
    }
};

// 获取页面内容列表
exports.getPages = async (req, res) => {
    try {
        const pages = await memoryStore.findDocuments('pages');

        res.json({
            success: true,
            data: pages
        });

    } catch (error) {
        console.error('获取页面内容列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取页面内容列表失败'
        });
    }
};

// 更新页面内容
exports.updatePage = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const page = await memoryStore.updateDocument('pages', id, updateData);

        if (!page) {
            return res.status(404).json({
                success: false,
                message: '页面不存在'
            });
        }

        res.json({
            success: true,
            data: page,
            message: '页面内容更新成功'
        });

    } catch (error) {
        console.error('更新页面内容错误:', error);
        res.status(500).json({
            success: false,
            message: '更新页面内容失败'
        });
    }
};

/**
 * 根据ID获取内容（保留原有功能）
 */
exports.getContentById = async (req, res) => {
    try {
        const content = await Content.findById(req.params.id)
            .populate('createdBy', 'username name');
        
        if (!content) {
            return res.status(404).json({ success: false, message: '内容不存在' });
        }
        
        return res.status(200).json({
            success: true,
            content
        });
    } catch (error) {
        console.error('获取内容详情错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 创建内容
 */
exports.createContent = async (req, res) => {
    try {
        const { title, type, content, path, version, status, startDate, endDate } = req.body;
        
        // 验证必填字段
        if (!title || !type || !content) {
            return res.status(400).json({ success: false, message: '标题、类型和内容为必填项' });
        }
        
        // 创建新内容
        const newContent = new Content({
            title,
            type,
            content,
            path: path || '',
            version: version || 'v1.0',
            status: status || 'draft',
            startDate: startDate || null,
            endDate: endDate || null,
            createdBy: req.session.user.id
        });
        
        await newContent.save();
        
        return res.status(201).json({
            success: true,
            message: '内容创建成功',
            content: newContent
        });
    } catch (error) {
        console.error('创建内容错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 更新内容
 */
exports.updateContent = async (req, res) => {
    try {
        const { title, content, path, version, status, startDate, endDate } = req.body;
        
        // 查找内容
        const contentItem = await Content.findById(req.params.id);
        if (!contentItem) {
            return res.status(404).json({ success: false, message: '内容不存在' });
        }
        
        // 更新内容信息
        if (title) contentItem.title = title;
        if (content) contentItem.content = content;
        if (path !== undefined) contentItem.path = path;
        if (version) contentItem.version = version;
        if (status) contentItem.status = status;
        if (startDate !== undefined) contentItem.startDate = startDate;
        if (endDate !== undefined) contentItem.endDate = endDate;
        
        await contentItem.save();
        
        return res.status(200).json({
            success: true,
            message: '内容更新成功',
            content: contentItem
        });
    } catch (error) {
        console.error('更新内容错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 删除内容
 */
exports.deleteContent = async (req, res) => {
    try {
        // 查找并删除内容
        const content = await Content.findByIdAndDelete(req.params.id);
        
        if (!content) {
            return res.status(404).json({ success: false, message: '内容不存在' });
        }
        
        return res.status(200).json({
            success: true,
            message: '内容删除成功'
        });
    } catch (error) {
        console.error('删除内容错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 获取最新版本的协议或公告
 */
exports.getLatestContent = async (req, res) => {
    try {
        const { type, title } = req.query;
        
        if (!type) {
            return res.status(400).json({ success: false, message: '内容类型为必填项' });
        }
        
        let query = { type, status: 'published' };
        if (title) {
            query.title = title;
        }
        
        const content = await Content.findOne(query)
            .sort({ publishedAt: -1 });
        
        if (!content) {
            return res.status(404).json({ success: false, message: '内容不存在' });
        }
        
        return res.status(200).json({
            success: true,
            content
        });
    } catch (error) {
        console.error('获取最新内容错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 获取内容历史版本
 */
exports.getContentHistory = async (req, res) => {
    try {
        const { type, title } = req.query;
        
        if (!type || !title) {
            return res.status(400).json({ success: false, message: '内容类型和标题为必填项' });
        }
        
        const history = await Content.find({ type, title, status: 'published' })
            .select('version publishedAt createdBy')
            .populate('createdBy', 'username name')
            .sort({ publishedAt: -1 });
        
        return res.status(200).json({
            success: true,
            history
        });
    } catch (error) {
        console.error('获取内容历史错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

// 获取协议详情
exports.getAgreementDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const agreement = await memoryStore.findOneDocument('agreements', { _id: id });

        if (!agreement) {
            return res.status(404).json({
                success: false,
                message: '协议不存在'
            });
        }

        res.json({
            success: true,
            data: agreement
        });

    } catch (error) {
        console.error('获取协议详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取协议详情失败'
        });
    }
};

// 发布协议
exports.publishAgreement = async (req, res) => {
    try {
        const { id } = req.params;

        const agreement = await memoryStore.updateDocument('agreements', id, {
            status: 'published',
            publishedAt: new Date(),
            updatedAt: new Date()
        });

        if (!agreement) {
            return res.status(404).json({
                success: false,
                message: '协议不存在'
            });
        }

        res.json({
            success: true,
            data: agreement,
            message: '协议发布成功'
        });

    } catch (error) {
        console.error('发布协议错误:', error);
        res.status(500).json({
            success: false,
            message: '发布协议失败'
        });
    }
};

// 获取公告详情
exports.getAnnouncementDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const announcement = await memoryStore.findOneDocument('announcements', { _id: id });

        if (!announcement) {
            return res.status(404).json({
                success: false,
                message: '公告不存在'
            });
        }

        res.json({
            success: true,
            data: announcement
        });

    } catch (error) {
        console.error('获取公告详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取公告详情失败'
        });
    }
};

// 发布公告
exports.publishAnnouncement = async (req, res) => {
    try {
        const { id } = req.params;

        const announcement = await memoryStore.updateDocument('announcements', id, {
            status: 'published',
            publishedAt: new Date(),
            updatedAt: new Date()
        });

        if (!announcement) {
            return res.status(404).json({
                success: false,
                message: '公告不存在'
            });
        }

        res.json({
            success: true,
            data: announcement,
            message: '公告发布成功'
        });

    } catch (error) {
        console.error('发布公告错误:', error);
        res.status(500).json({
            success: false,
            message: '发布公告失败'
        });
    }
};

// 下架公告
exports.unpublishAnnouncement = async (req, res) => {
    try {
        const { id } = req.params;

        const announcement = await memoryStore.updateDocument('announcements', id, {
            status: 'unpublished',
            unpublishedAt: new Date(),
            updatedAt: new Date()
        });

        if (!announcement) {
            return res.status(404).json({
                success: false,
                message: '公告不存在'
            });
        }

        res.json({
            success: true,
            data: announcement,
            message: '公告下架成功'
        });

    } catch (error) {
        console.error('下架公告错误:', error);
        res.status(500).json({
            success: false,
            message: '下架公告失败'
        });
    }
};

module.exports = exports;