const memoryStore = require('../models/memoryStore');

/**
 * 安全中心控制器
 * 处理风控规则、敏感操作审批、数据备份等安全功能
 */

// 获取安全概览
exports.getSecurityOverview = async (req, res) => {
    try {
        const riskRules = await memoryStore.findDocuments('riskRules');
        const approvals = await memoryStore.findDocuments('approvals');
        const backups = await memoryStore.findDocuments('backups');
        const securityLogs = await memoryStore.findDocuments('securityLogs');

        // 计算安全等级和评分
        const activeRules = riskRules.filter(r => r.status === 'active').length;
        const pendingApprovals = approvals.filter(a => a.status === 'pending').length;
        const urgentApprovals = approvals.filter(a => a.status === 'pending' && a.priority === 'urgent').length;

        // 计算今日风险事件
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        const todayRisks = securityLogs.filter(log => {
            const logDate = new Date(log.timestamp);
            return logDate >= today && log.level === 'warning';
        }).length;

        // 获取最近备份信息
        const latestBackup = backups
            .filter(b => b.status === 'completed')
            .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))[0];

        const lastBackup = latestBackup ? formatRelativeTime(latestBackup.createdAt) : '未知';

        // 计算安全评分
        let securityScore = 100;
        if (pendingApprovals > 5) securityScore -= 10;
        if (todayRisks > 3) securityScore -= 15;
        if (!latestBackup || isBackupOld(latestBackup.createdAt)) securityScore -= 20;

        const securityLevel = securityScore >= 90 ? '高' : securityScore >= 70 ? '中' : '低';

        res.json({
            success: true,
            data: {
                securityLevel,
                securityScore,
                riskEvents: todayRisks,
                todayRisks,
                pendingApprovals,
                urgentApprovals,
                lastBackup,
                nextBackup: '明日 02:00'
            }
        });

    } catch (error) {
        console.error('获取安全概览错误:', error);
        res.status(500).json({
            success: false,
            message: '获取安全概览失败'
        });
    }
};

// 获取风控规则列表
exports.getRiskRules = async (req, res) => {
    try {
        const riskRules = await memoryStore.findDocuments('riskRules');

        // 计算风险统计
        const highRiskCount = riskRules.filter(r => r.riskLevel === 'high').length;
        const mediumRiskCount = riskRules.filter(r => r.riskLevel === 'medium').length;
        const lowRiskCount = riskRules.filter(r => r.riskLevel === 'low').length;
        const blockedCount = riskRules.filter(r => r.action === 'block').length;

        res.json({
            success: true,
            data: riskRules,
            stats: {
                highRiskCount,
                mediumRiskCount,
                lowRiskCount,
                blockedCount
            }
        });

    } catch (error) {
        console.error('获取风控规则错误:', error);
        res.status(500).json({
            success: false,
            message: '获取风控规则失败'
        });
    }
};

// 获取IP黑名单列表（保留原有功能）
exports.getIpBlacklist = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, search } = req.query;

        let ipBlacklist = await memoryStore.findDocuments('ipBlacklist');

        // 状态过滤
        if (status) {
            ipBlacklist = ipBlacklist.filter(ip => ip.status === status);
        }

        // 搜索过滤
        if (search) {
            ipBlacklist = ipBlacklist.filter(ip =>
                ip.ip.includes(search) ||
                ip.reason?.toLowerCase().includes(search.toLowerCase())
            );
        }

        // 排序
        ipBlacklist.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedIpBlacklist = ipBlacklist.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: paginatedIpBlacklist,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: ipBlacklist.length,
                pages: Math.ceil(ipBlacklist.length / limit)
            }
        });

    } catch (error) {
        console.error('获取IP黑名单列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取IP黑名单列表失败'
        });
    }
};

// 创建风控规则
exports.createRiskRule = async (req, res) => {
    try {
        const ruleData = {
            ...req.body,
            status: 'active',
            triggerCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const rule = await memoryStore.createDocument('riskRules', ruleData);

        res.json({
            success: true,
            data: rule,
            message: '风控规则创建成功'
        });

    } catch (error) {
        console.error('创建风控规则错误:', error);
        res.status(500).json({
            success: false,
            message: '创建风控规则失败'
        });
    }
};

// 获取审批列表
exports.getApprovals = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, type } = req.query;

        let approvals = await memoryStore.findDocuments('approvals');

        // 状态过滤
        if (status) {
            approvals = approvals.filter(a => a.status === status);
        }

        // 类型过滤
        if (type) {
            approvals = approvals.filter(a => a.type === type);
        }

        // 排序
        approvals.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedApprovals = approvals.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: paginatedApprovals,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: approvals.length,
                pages: Math.ceil(approvals.length / limit)
            }
        });

    } catch (error) {
        console.error('获取审批列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取审批列表失败'
        });
    }
};

// 处理审批
exports.processApproval = async (req, res) => {
    try {
        const { id } = req.params;
        const { action, reason } = req.body; // action: 'approve' | 'reject'

        const approval = await memoryStore.findOneDocument('approvals', { _id: id });
        if (!approval) {
            return res.status(404).json({
                success: false,
                message: '审批申请不存在'
            });
        }

        const updateData = {
            status: action === 'approve' ? 'approved' : 'rejected',
            processedBy: req.user?.id || 'admin',
            processedAt: new Date(),
            processReason: reason,
            updatedAt: new Date()
        };

        await memoryStore.updateDocument('approvals', id, updateData);

        res.json({
            success: true,
            message: action === 'approve' ? '审批已通过' : '审批已拒绝'
        });

    } catch (error) {
        console.error('处理审批错误:', error);
        res.status(500).json({
            success: false,
            message: '处理审批失败'
        });
    }
};

// 获取备份列表
exports.getBackups = async (req, res) => {
    try {
        const backups = await memoryStore.findDocuments('backups');

        // 排序
        backups.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt));

        res.json({
            success: true,
            data: backups
        });

    } catch (error) {
        console.error('获取备份列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取备份列表失败'
        });
    }
};

// 创建备份
exports.createBackup = async (req, res) => {
    try {
        const backupData = {
            name: `backup_${new Date().toISOString().split('T')[0]}_${Date.now()}`,
            type: req.body.type || 'full',
            status: 'in_progress',
            size: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const backup = await memoryStore.createDocument('backups', backupData);

        // 模拟备份过程
        setTimeout(async () => {
            await memoryStore.updateDocument('backups', backup._id, {
                status: 'completed',
                size: Math.floor(Math.random() * 1000000000) + 100000000, // 100MB - 1GB
                completedAt: new Date(),
                updatedAt: new Date()
            });
        }, 5000);

        res.json({
            success: true,
            data: backup,
            message: '备份任务已启动'
        });

    } catch (error) {
        console.error('创建备份错误:', error);
        res.status(500).json({
            success: false,
            message: '创建备份失败'
        });
    }
};

// 辅助函数：格式化相对时间
function formatRelativeTime(date) {
    const now = new Date();
    const diff = now - new Date(date);
    const days = Math.floor(diff / (1000 * 60 * 60 * 24));

    if (days === 0) return '今日';
    if (days === 1) return '昨日';
    if (days < 7) return `${days}天前`;
    return new Date(date).toLocaleDateString();
}

// 辅助函数：检查备份是否过期
function isBackupOld(date) {
    const now = new Date();
    const backupDate = new Date(date);
    const diffDays = (now - backupDate) / (1000 * 60 * 60 * 24);
    return diffDays > 2; // 超过2天认为过期
}

/**
 * 添加IP到黑名单（保留原有功能）
 */
exports.addToIpBlacklist = async (req, res) => {
    try {
        const { ip, reason, expiryDate } = req.body;
        
        // 验证必填字段
        if (!ip || !reason) {
            return res.status(400).json({ success: false, message: 'IP地址和原因为必填项' });
        }
        
        // 检查IP是否已在黑名单中
        const existingIp = await IpBlacklist.findOne({ ip, status: 'active' });
        if (existingIp) {
            return res.status(400).json({ success: false, message: '该IP已在黑名单中' });
        }
        
        // 创建新的黑名单记录
        const newIpBlacklist = new IpBlacklist({
            ip,
            reason,
            createdBy: req.session.user ? req.session.user.username : '系统管理员',
            expiryDate: expiryDate || null
        });
        
        await newIpBlacklist.save();
        
        return res.status(201).json({
            success: true,
            message: 'IP已成功添加到黑名单',
            ipBlacklist: newIpBlacklist
        });
    } catch (error) {
        console.error('添加IP到黑名单错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 从黑名单中移除IP
 */
exports.removeFromIpBlacklist = async (req, res) => {
    try {
        // 查找并删除黑名单记录
        const ipBlacklist = await IpBlacklist.findByIdAndDelete(req.params.id);
        
        if (!ipBlacklist) {
            return res.status(404).json({ success: false, message: '黑名单记录不存在' });
        }
        
        return res.status(200).json({
            success: true,
            message: 'IP已成功从黑名单中移除'
        });
    } catch (error) {
        console.error('从黑名单中移除IP错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 更新黑名单IP状态
 */
exports.updateIpBlacklistStatus = async (req, res) => {
    try {
        const { status, expiryDate } = req.body;
        
        // 验证必填字段
        if (!status) {
            return res.status(400).json({ success: false, message: '状态为必填项' });
        }
        
        // 查找黑名单记录
        const ipBlacklist = await IpBlacklist.findById(req.params.id);
        if (!ipBlacklist) {
            return res.status(404).json({ success: false, message: '黑名单记录不存在' });
        }
        
        // 更新状态
        ipBlacklist.status = status;
        if (expiryDate !== undefined) {
            ipBlacklist.expiryDate = expiryDate;
        }
        
        await ipBlacklist.save();
        
        return res.status(200).json({
            success: true,
            message: '黑名单状态更新成功',
            ipBlacklist
        });
    } catch (error) {
        console.error('更新黑名单状态错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
};

/**
 * 检查IP是否在黑名单中
 */
exports.checkIpBlacklist = async (req, res) => {
    try {
        const { ip } = req.query;
        
        if (!ip) {
            return res.status(400).json({ success: false, message: 'IP地址为必填项' });
        }
        
        const isBlacklisted = await IpBlacklist.isBlacklisted(ip);
        
        return res.status(200).json({
            success: true,
            isBlacklisted
        });
    } catch (error) {
        console.error('检查IP黑名单错误:', error);
        return res.status(500).json({ success: false, message: '服务器错误，请稍后再试' });
    }
}; 