const memoryStore = require('../models/memoryStore');

/**
 * 代理管理控制器
 * 处理代理商管理、级别管理、佣金设置、招商加盟等功能
 */

// 代理商等级配置
const AGENT_LEVELS = {
    1: {
        name: '初级代理',
        commission: 0.05,
        minSales: 0,
        maxSales: 50000,
        permissions: ['view_own_data'],
        maxSubAgents: 5,
        recruitmentBonus: 100
    },
    2: {
        name: '中级代理',
        commission: 0.08,
        minSales: 50000,
        maxSales: 200000,
        permissions: ['view_own_data', 'manage_sub_agents'],
        maxSubAgents: 20,
        recruitmentBonus: 300
    },
    3: {
        name: '高级代理',
        commission: 0.12,
        minSales: 200000,
        maxSales: 1000000,
        permissions: ['view_own_data', 'manage_sub_agents', 'view_team_data'],
        maxSubAgents: 50,
        recruitmentBonus: 500
    },
    4: {
        name: '区域代理',
        commission: 0.15,
        minSales: 1000000,
        maxSales: 5000000,
        permissions: ['view_own_data', 'manage_sub_agents', 'view_team_data', 'recruit_agents'],
        maxSubAgents: 100,
        recruitmentBonus: 1000
    },
    5: {
        name: '总代理',
        commission: 0.20,
        minSales: 5000000,
        maxSales: null,
        permissions: ['view_own_data', 'manage_sub_agents', 'view_team_data', 'recruit_agents', 'manage_all'],
        maxSubAgents: null,
        recruitmentBonus: 2000
    }
};

// 佣金分配规则
const COMMISSION_RULES = {
    // 直接佣金：代理商自己销售获得的佣金
    direct: {
        1: 0.05, // 5%
        2: 0.08, // 8%
        3: 0.12, // 12%
        4: 0.15, // 15%
        5: 0.20  // 20%
    },
    // 间接佣金：下级代理商销售，上级获得的佣金
    indirect: {
        1: 0.01, // 1%
        2: 0.02, // 2%
        3: 0.03, // 3%
        4: 0.04, // 4%
        5: 0.05  // 5%
    },
    // 团队佣金：整个团队销售的额外奖励
    team: {
        3: 0.005, // 0.5%
        4: 0.01,  // 1%
        5: 0.015  // 1.5%
    }
};

// 获取代理商概览统计
exports.getAgentOverview = async (req, res) => {
    try {
        const agents = await memoryStore.findDocuments('agents');
        const agentLevels = await memoryStore.findDocuments('agentLevels');
        const commissions = await memoryStore.findDocuments('commissions');
        const applications = await memoryStore.findDocuments('agentApplications');

        // 计算统计数据
        const totalAgentsCount = agents.length;
        const activeAgentsCount = agents.filter(agent => agent.status === 'active').length;
        const totalCommission = commissions
            .filter(c => c.status === 'paid')
            .reduce((sum, c) => sum + c.amount, 0);

        // 计算增长率
        const lastMonth = new Date();
        lastMonth.setMonth(lastMonth.getMonth() - 1);
        const lastMonthAgents = agents.filter(agent => new Date(agent.createdAt) < lastMonth).length;
        const agentGrowth = lastMonthAgents > 0 ?
            ((totalAgentsCount - lastMonthAgents) / lastMonthAgents * 100).toFixed(1) : 0;

        // 待审核申请数
        const pendingApplications = applications.filter(app => app.status === 'pending').length;

        // 本月通过/拒绝申请数
        const thisMonth = new Date();
        thisMonth.setDate(1);
        const approvedThisMonth = applications.filter(app =>
            app.status === 'approved' && new Date(app.updatedAt) >= thisMonth
        ).length;
        const rejectedThisMonth = applications.filter(app =>
            app.status === 'rejected' && new Date(app.updatedAt) >= thisMonth
        ).length;

        res.json({
            success: true,
            data: {
                totalAgentsCount,
                activeAgentsCount,
                totalCommission,
                agentGrowth: parseFloat(agentGrowth),
                pendingApplications,
                approvedThisMonth,
                rejectedThisMonth
            }
        });

    } catch (error) {
        console.error('获取代理商概览错误:', error);
        res.status(500).json({
            success: false,
            message: '获取代理商概览失败'
        });
    }
};

// 获取代理商列表
exports.getAgents = async (req, res) => {
    try {
        const { page = 1, limit = 20, search, status, level } = req.query;
        
        let agents = await memoryStore.findDocuments('agents');
        
        // 搜索过滤
        if (search) {
            agents = agents.filter(agent => 
                agent.name.toLowerCase().includes(search.toLowerCase()) ||
                agent.contactPerson.toLowerCase().includes(search.toLowerCase()) ||
                agent.phone.includes(search)
            );
        }
        
        // 状态过滤
        if (status) {
            agents = agents.filter(agent => agent.status === status);
        }
        
        // 级别过滤
        if (level) {
            agents = agents.filter(agent => agent.level === level);
        }
        
        // 分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedAgents = agents.slice(startIndex, endIndex);
        
        // 计算统计信息
        const stats = {
            total: agents.length,
            active: agents.filter(a => a.status === 'active').length,
            pending: agents.filter(a => a.status === 'pending').length,
            suspended: agents.filter(a => a.status === 'suspended').length
        };
        
        res.json({
            success: true,
            data: paginatedAgents,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: agents.length,
                pages: Math.ceil(agents.length / limit)
            },
            stats
        });
        
    } catch (error) {
        console.error('获取代理商列表错误:', error);
        res.status(500).json({
            success: false,
            message: '获取代理商列表失败'
        });
    }
};

// 创建代理商
exports.createAgent = async (req, res) => {
    try {
        const agentData = {
            ...req.body,
            status: 'pending', // 默认待审核状态
            createdAt: new Date(),
            updatedAt: new Date(),
            totalRevenue: 0,
            totalCommission: 0,
            customerCount: 0,
            subAgentCount: 0
        };
        
        const agent = await memoryStore.createDocument('agents', agentData);
        
        res.json({
            success: true,
            data: agent,
            message: '代理商创建成功'
        });
        
    } catch (error) {
        console.error('创建代理商错误:', error);
        res.status(500).json({
            success: false,
            message: '创建代理商失败'
        });
    }
};

// 更新代理商
exports.updateAgent = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };
        
        const agent = await memoryStore.updateDocument('agents', id, updateData);
        
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }
        
        res.json({
            success: true,
            data: agent,
            message: '代理商更新成功'
        });
        
    } catch (error) {
        console.error('更新代理商错误:', error);
        res.status(500).json({
            success: false,
            message: '更新代理商失败'
        });
    }
};

// 删除代理商
exports.deleteAgent = async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await memoryStore.deleteDocument('agents', id);
        
        if (!success) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }
        
        res.json({
            success: true,
            message: '代理商删除成功'
        });
        
    } catch (error) {
        console.error('删除代理商错误:', error);
        res.status(500).json({
            success: false,
            message: '删除代理商失败'
        });
    }
};

// 获取代理级别列表
exports.getAgentLevels = async (req, res) => {
    try {
        const levels = await memoryStore.findDocuments('agentLevels');
        
        res.json({
            success: true,
            data: levels
        });
        
    } catch (error) {
        console.error('获取代理级别错误:', error);
        res.status(500).json({
            success: false,
            message: '获取代理级别失败'
        });
    }
};

// 创建代理级别
exports.createAgentLevel = async (req, res) => {
    try {
        const levelData = {
            ...req.body,
            createdAt: new Date(),
            updatedAt: new Date()
        };
        
        const level = await memoryStore.createDocument('agentLevels', levelData);
        
        res.json({
            success: true,
            data: level,
            message: '代理级别创建成功'
        });
        
    } catch (error) {
        console.error('创建代理级别错误:', error);
        res.status(500).json({
            success: false,
            message: '创建代理级别失败'
        });
    }
};

// 获取佣金设置
exports.getCommissionSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'commission' });
        
        if (!settings) {
            // 创建默认佣金设置
            settings = await memoryStore.createDocument('settings', {
                type: 'commission',
                data: {
                    level1Commission: 10,
                    level2Commission: 5,
                    level3Commission: 2,
                    settlementPeriod: 'monthly',
                    minWithdraw: 100,
                    withdrawFee: 0.5
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data
        });
        
    } catch (error) {
        console.error('获取佣金设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金设置失败'
        });
    }
};

// 更新佣金设置
exports.updateCommissionSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'commission' });
        
        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'commission',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data,
            message: '佣金设置更新成功'
        });
        
    } catch (error) {
        console.error('更新佣金设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新佣金设置失败'
        });
    }
};

// 获取招商加盟设置
exports.getRecruitmentSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'recruitment' });
        
        if (!settings) {
            // 创建默认招商设置
            settings = await memoryStore.createDocument('settings', {
                type: 'recruitment',
                data: {
                    approvalProcess: 'manual',
                    applicationRequirements: '1. 具备相关行业经验\n2. 有稳定的客户资源\n3. 认同公司文化和产品',
                    contractTemplate: '代理商合同模板内容...'
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data
        });
        
    } catch (error) {
        console.error('获取招商设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取招商设置失败'
        });
    }
};

// 更新招商加盟设置
exports.updateRecruitmentSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'recruitment' });
        
        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'recruitment',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: settings.data,
            message: '招商设置更新成功'
        });
        
    } catch (error) {
        console.error('更新招商设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新招商设置失败'
        });
    }
};

// 获取代理级别列表
exports.getAgentLevels = async (req, res) => {
    try {
        const agentLevels = await memoryStore.findDocuments('agentLevels');

        // 按级别排序
        agentLevels.sort((a, b) => a.order - b.order);

        res.json({
            success: true,
            data: agentLevels
        });

    } catch (error) {
        console.error('获取代理级别错误:', error);
        res.status(500).json({
            success: false,
            message: '获取代理级别失败'
        });
    }
};

// 创建代理级别
exports.createAgentLevel = async (req, res) => {
    try {
        const levelData = {
            ...req.body,
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date()
        };

        const level = await memoryStore.createDocument('agentLevels', levelData);

        res.json({
            success: true,
            data: level,
            message: '代理级别创建成功'
        });

    } catch (error) {
        console.error('创建代理级别错误:', error);
        res.status(500).json({
            success: false,
            message: '创建代理级别失败'
        });
    }
};

// 更新代理级别
exports.updateAgentLevel = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };

        const level = await memoryStore.updateDocument('agentLevels', id, updateData);

        if (!level) {
            return res.status(404).json({
                success: false,
                message: '代理级别不存在'
            });
        }

        res.json({
            success: true,
            data: level,
            message: '代理级别更新成功'
        });

    } catch (error) {
        console.error('更新代理级别错误:', error);
        res.status(500).json({
            success: false,
            message: '更新代理级别失败'
        });
    }
};

// 获取佣金设置
exports.getCommissionSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'commission' });

        if (!settings) {
            // 创建默认佣金设置
            settings = await memoryStore.createDocument('settings', {
                type: 'commission',
                data: {
                    level1Commission: 10,
                    level2Commission: 5,
                    level3Commission: 2,
                    settlementPeriod: 'monthly',
                    minWithdraw: 100,
                    withdrawFee: 0.5
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data
        });

    } catch (error) {
        console.error('获取佣金设置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金设置失败'
        });
    }
};

// 更新佣金设置
exports.updateCommissionSettings = async (req, res) => {
    try {
        let settings = await memoryStore.findOneDocument('settings', { type: 'commission' });

        if (settings) {
            settings = await memoryStore.updateDocument('settings', settings._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            settings = await memoryStore.createDocument('settings', {
                type: 'commission',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: settings.data,
            message: '佣金设置更新成功'
        });

    } catch (error) {
        console.error('更新佣金设置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新佣金设置失败'
        });
    }
};

// ==================== 多级代理商体系功能 ====================

// 获取代理商层级结构
exports.getAgentHierarchy = async (req, res) => {
    try {
        const { agentId } = req.params;

        const agent = await memoryStore.findOne('agents', { id: agentId });
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }

        // 获取上级代理商链
        const uplineChain = await getUplineChain(agentId);

        // 获取下级代理商树
        const downlineTree = await getDownlineTree(agentId);

        // 获取团队统计
        const teamStats = await getTeamStats(agentId);

        res.json({
            success: true,
            data: {
                agent,
                uplineChain,
                downlineTree,
                teamStats
            }
        });
    } catch (error) {
        console.error('获取代理商层级结构失败:', error);
        res.status(500).json({
            success: false,
            message: '获取代理商层级结构失败'
        });
    }
};

// 设置代理商上级
exports.setAgentUpline = async (req, res) => {
    try {
        const { agentId } = req.params;
        const { uplineId } = req.body;

        const agent = await memoryStore.findOne('agents', { id: agentId });
        const upline = await memoryStore.findOne('agents', { id: uplineId });

        if (!agent || !upline) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }

        // 检查是否会形成循环引用
        const uplineChain = await getUplineChain(uplineId);
        if (uplineChain.some(u => u.id === agentId)) {
            return res.status(400).json({
                success: false,
                message: '不能设置下级代理商为上级'
            });
        }

        // 检查上级代理商是否有权限招募下级
        const uplineLevel = AGENT_LEVELS[upline.level];
        if (!uplineLevel.permissions.includes('manage_sub_agents')) {
            return res.status(400).json({
                success: false,
                message: '上级代理商没有招募权限'
            });
        }

        // 检查上级代理商的下级数量限制
        if (uplineLevel.maxSubAgents) {
            const currentSubAgents = await memoryStore.countDocuments('agents', { uplineId });
            if (currentSubAgents >= uplineLevel.maxSubAgents) {
                return res.status(400).json({
                    success: false,
                    message: `上级代理商下级数量已达上限(${uplineLevel.maxSubAgents})`
                });
            }
        }

        // 更新代理商上级关系
        await memoryStore.updateOne('agents', { id: agentId }, {
            uplineId,
            updatedAt: new Date()
        });

        // 记录招募奖励
        await recordRecruitmentBonus(uplineId, agentId);

        res.json({
            success: true,
            message: '设置上级代理商成功'
        });
    } catch (error) {
        console.error('设置代理商上级失败:', error);
        res.status(500).json({
            success: false,
            message: '设置代理商上级失败'
        });
    }
};

// 计算多级佣金
exports.calculateMultiLevelCommission = async (req, res) => {
    try {
        const { orderId, agentId, orderAmount } = req.body;

        if (!orderId || !agentId || !orderAmount) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }

        const commissionResult = await calculateMultiLevelCommission(agentId, orderAmount, orderId);

        res.json({
            success: true,
            data: commissionResult
        });
    } catch (error) {
        console.error('计算多级佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '计算多级佣金失败'
        });
    }
};

// 代理商升级
exports.upgradeAgent = async (req, res) => {
    try {
        const { agentId } = req.params;
        const { newLevel, reason } = req.body;

        const agent = await memoryStore.findOne('agents', { id: agentId });
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }

        if (!AGENT_LEVELS[newLevel]) {
            return res.status(400).json({
                success: false,
                message: '无效的代理商等级'
            });
        }

        if (newLevel <= agent.level) {
            return res.status(400).json({
                success: false,
                message: '只能升级到更高等级'
            });
        }

        // 检查升级条件
        const canUpgrade = await checkUpgradeConditions(agent, newLevel);
        if (!canUpgrade.eligible) {
            return res.status(400).json({
                success: false,
                message: canUpgrade.reason
            });
        }

        const oldLevel = agent.level;

        // 更新代理商等级
        await memoryStore.updateOne('agents', { id: agentId }, {
            level: newLevel,
            upgradeHistory: [
                ...(agent.upgradeHistory || []),
                {
                    fromLevel: oldLevel,
                    toLevel: newLevel,
                    reason,
                    upgradeDate: new Date(),
                    upgradedBy: req.user?.id || 'system'
                }
            ],
            updatedAt: new Date()
        });

        // 记录升级奖励
        await recordUpgradeBonus(agentId, oldLevel, newLevel);

        res.json({
            success: true,
            message: `代理商已成功升级到${AGENT_LEVELS[newLevel].name}`
        });
    } catch (error) {
        console.error('代理商升级失败:', error);
        res.status(500).json({
            success: false,
            message: '代理商升级失败'
        });
    }
};

// 获取代理商团队业绩
exports.getTeamPerformance = async (req, res) => {
    try {
        const { agentId } = req.params;
        const { period = '30d' } = req.query;

        const agent = await memoryStore.findOne('agents', { id: agentId });
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }

        const performance = await getTeamPerformance(agentId, period);

        res.json({
            success: true,
            data: performance
        });
    } catch (error) {
        console.error('获取团队业绩失败:', error);
        res.status(500).json({
            success: false,
            message: '获取团队业绩失败'
        });
    }
};

// ==================== 辅助函数 ====================

// 获取上级代理商链
async function getUplineChain(agentId) {
    const chain = [];
    let currentAgentId = agentId;

    while (currentAgentId) {
        const agent = await memoryStore.findOne('agents', { id: currentAgentId });
        if (!agent || !agent.uplineId) break;

        const upline = await memoryStore.findOne('agents', { id: agent.uplineId });
        if (!upline) break;

        chain.push({
            id: upline.id,
            name: upline.name,
            level: upline.level,
            levelName: AGENT_LEVELS[upline.level].name
        });

        currentAgentId = upline.uplineId;

        // 防止无限循环
        if (chain.length > 10) break;
    }

    return chain;
}

// 获取下级代理商树
async function getDownlineTree(agentId, depth = 0, maxDepth = 3) {
    if (depth >= maxDepth) return [];

    const directDownlines = await memoryStore.findDocuments('agents', { uplineId: agentId });

    const tree = [];
    for (const downline of directDownlines) {
        const subTree = await getDownlineTree(downline.id, depth + 1, maxDepth);
        tree.push({
            id: downline.id,
            name: downline.name,
            level: downline.level,
            levelName: AGENT_LEVELS[downline.level].name,
            status: downline.status,
            joinDate: downline.createdAt,
            children: subTree
        });
    }

    return tree;
}

// 获取团队统计
async function getTeamStats(agentId) {
    const allDownlines = await getAllDownlines(agentId);

    const stats = {
        totalMembers: allDownlines.length,
        activeMembers: allDownlines.filter(a => a.status === 'active').length,
        levelDistribution: {},
        totalSales: 0,
        totalCommission: 0
    };

    // 统计等级分布
    for (let level = 1; level <= 5; level++) {
        stats.levelDistribution[level] = allDownlines.filter(a => a.level === level).length;
    }

    // 计算团队销售额和佣金（这里使用模拟数据）
    stats.totalSales = allDownlines.length * Math.floor(Math.random() * 50000) + 10000;
    stats.totalCommission = stats.totalSales * 0.1;

    return stats;
}

// 获取所有下级代理商
async function getAllDownlines(agentId, visited = new Set()) {
    if (visited.has(agentId)) return [];
    visited.add(agentId);

    const directDownlines = await memoryStore.findDocuments('agents', { uplineId: agentId });
    let allDownlines = [...directDownlines];

    for (const downline of directDownlines) {
        const subDownlines = await getAllDownlines(downline.id, visited);
        allDownlines = allDownlines.concat(subDownlines);
    }

    return allDownlines;
}

// 计算多级佣金
async function calculateMultiLevelCommission(agentId, orderAmount, orderId) {
    const commissions = [];
    const uplineChain = await getUplineChain(agentId);

    // 直接佣金（销售代理商）
    const agent = await memoryStore.findOne('agents', { id: agentId });
    if (agent) {
        const directCommission = orderAmount * COMMISSION_RULES.direct[agent.level];
        commissions.push({
            agentId: agent.id,
            agentName: agent.name,
            level: agent.level,
            type: 'direct',
            rate: COMMISSION_RULES.direct[agent.level],
            amount: directCommission,
            orderId
        });
    }

    // 间接佣金（上级代理商）
    for (let i = 0; i < uplineChain.length && i < 3; i++) {
        const upline = uplineChain[i];
        const indirectRate = COMMISSION_RULES.indirect[upline.level] || 0;
        const indirectCommission = orderAmount * indirectRate;

        if (indirectCommission > 0) {
            commissions.push({
                agentId: upline.id,
                agentName: upline.name,
                level: upline.level,
                type: 'indirect',
                rate: indirectRate,
                amount: indirectCommission,
                orderId,
                fromAgentId: agentId
            });
        }
    }

    // 团队佣金（高级代理商）
    for (const upline of uplineChain) {
        if (COMMISSION_RULES.team[upline.level]) {
            const teamRate = COMMISSION_RULES.team[upline.level];
            const teamCommission = orderAmount * teamRate;

            commissions.push({
                agentId: upline.id,
                agentName: upline.name,
                level: upline.level,
                type: 'team',
                rate: teamRate,
                amount: teamCommission,
                orderId,
                fromAgentId: agentId
            });
        }
    }

    // 保存佣金记录
    for (const commission of commissions) {
        await memoryStore.insertOne('commissions', {
            id: generateCommissionId(),
            ...commission,
            status: 'pending',
            createdAt: new Date()
        });
    }

    return {
        orderId,
        orderAmount,
        totalCommission: commissions.reduce((sum, c) => sum + c.amount, 0),
        commissions
    };
}

// 记录招募奖励
async function recordRecruitmentBonus(uplineId, newAgentId) {
    const upline = await memoryStore.findOne('agents', { id: uplineId });
    if (!upline) return;

    const bonus = AGENT_LEVELS[upline.level].recruitmentBonus;
    if (bonus > 0) {
        await memoryStore.insertOne('commissions', {
            id: generateCommissionId(),
            agentId: uplineId,
            agentName: upline.name,
            type: 'recruitment',
            amount: bonus,
            status: 'pending',
            relatedAgentId: newAgentId,
            createdAt: new Date()
        });
    }
}

// 记录升级奖励
async function recordUpgradeBonus(agentId, oldLevel, newLevel) {
    const agent = await memoryStore.findOne('agents', { id: agentId });
    if (!agent) return;

    const oldBonus = AGENT_LEVELS[oldLevel].recruitmentBonus;
    const newBonus = AGENT_LEVELS[newLevel].recruitmentBonus;
    const upgradeBonus = newBonus - oldBonus;

    if (upgradeBonus > 0) {
        await memoryStore.insertOne('commissions', {
            id: generateCommissionId(),
            agentId,
            agentName: agent.name,
            type: 'upgrade',
            amount: upgradeBonus,
            status: 'pending',
            fromLevel: oldLevel,
            toLevel: newLevel,
            createdAt: new Date()
        });
    }
}

// 检查升级条件
async function checkUpgradeConditions(agent, newLevel) {
    const targetLevel = AGENT_LEVELS[newLevel];
    if (!targetLevel) {
        return { eligible: false, reason: '无效的目标等级' };
    }

    // 检查销售额要求
    const sales = await getAgentSales(agent.id);
    if (sales < targetLevel.minSales) {
        return {
            eligible: false,
            reason: `销售额不足，需要${targetLevel.minSales}，当前${sales}`
        };
    }

    // 检查团队要求（3级以上需要团队）
    if (newLevel >= 3) {
        const teamStats = await getTeamStats(agent.id);
        const minTeamSize = newLevel * 2; // 简单的团队要求

        if (teamStats.activeMembers < minTeamSize) {
            return {
                eligible: false,
                reason: `团队人数不足，需要${minTeamSize}人，当前${teamStats.activeMembers}人`
            };
        }
    }

    return { eligible: true };
}

// 获取代理商销售额
async function getAgentSales(agentId) {
    // 这里应该从订单系统获取真实销售数据
    // 现在返回模拟数据
    return Math.floor(Math.random() * 1000000) + 50000;
}

// 获取团队业绩
async function getTeamPerformance(agentId, period) {
    const allDownlines = await getAllDownlines(agentId);

    // 模拟业绩数据
    const performance = {
        period,
        personalSales: Math.floor(Math.random() * 100000) + 10000,
        teamSales: Math.floor(Math.random() * 500000) + 50000,
        personalCommission: 0,
        teamCommission: 0,
        newRecruits: Math.floor(Math.random() * 5),
        activeMembers: allDownlines.filter(a => a.status === 'active').length,
        topPerformers: allDownlines
            .sort(() => Math.random() - 0.5)
            .slice(0, 5)
            .map(agent => ({
                id: agent.id,
                name: agent.name,
                level: agent.level,
                sales: Math.floor(Math.random() * 50000) + 5000
            }))
    };

    performance.personalCommission = performance.personalSales * 0.1;
    performance.teamCommission = performance.teamSales * 0.05;

    return performance;
}

// ==================== 招商管理功能 ====================

// 获取招商申请列表
exports.getRecruitmentApplications = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, source } = req.query;

        let query = {};
        if (status) query.status = status;
        if (source) query.source = source;

        const applications = await memoryStore.findDocuments('recruitmentApplications', query);

        // 分页处理
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedApplications = applications.slice(startIndex, endIndex);

        // 统计数据
        const stats = {
            total: applications.length,
            pending: applications.filter(app => app.status === 'pending').length,
            approved: applications.filter(app => app.status === 'approved').length,
            rejected: applications.filter(app => app.status === 'rejected').length,
            thisMonth: applications.filter(app => {
                const appDate = new Date(app.createdAt);
                const thisMonth = new Date();
                return appDate.getMonth() === thisMonth.getMonth() &&
                       appDate.getFullYear() === thisMonth.getFullYear();
            }).length
        };

        res.json({
            success: true,
            data: {
                applications: paginatedApplications,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total: applications.length,
                    pages: Math.ceil(applications.length / limit)
                },
                stats
            }
        });
    } catch (error) {
        console.error('获取招商申请列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取招商申请列表失败'
        });
    }
};

// 创建招商申请
exports.createRecruitmentApplication = async (req, res) => {
    try {
        const {
            name,
            phone,
            email,
            company,
            region,
            experience,
            expectedLevel,
            investmentCapacity,
            businessPlan,
            referralCode,
            source = 'website'
        } = req.body;

        // 验证必填字段
        if (!name || !phone || !email) {
            return res.status(400).json({
                success: false,
                message: '姓名、电话和邮箱为必填项'
            });
        }

        // 检查是否已存在相同邮箱或电话的申请
        const existingApplication = await memoryStore.findOne('recruitmentApplications', {
            $or: [{ email }, { phone }]
        });

        if (existingApplication) {
            return res.status(400).json({
                success: false,
                message: '该邮箱或电话已提交过申请'
            });
        }

        // 处理推荐码
        let referralAgent = null;
        if (referralCode) {
            referralAgent = await memoryStore.findOne('agents', { referralCode });
            if (!referralAgent) {
                return res.status(400).json({
                    success: false,
                    message: '推荐码无效'
                });
            }
        }

        const applicationId = generateApplicationId();

        const application = await memoryStore.insertOne('recruitmentApplications', {
            id: applicationId,
            name,
            phone,
            email,
            company,
            region,
            experience,
            expectedLevel: expectedLevel || 1,
            investmentCapacity,
            businessPlan,
            referralCode,
            referralAgentId: referralAgent?.id,
            source,
            status: 'pending',
            submittedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        });

        // 发送确认邮件（模拟）
        await sendApplicationConfirmationEmail(email, applicationId);

        // 如果有推荐人，记录推荐奖励
        if (referralAgent) {
            await recordReferralReward(referralAgent.id, applicationId);
        }

        res.status(201).json({
            success: true,
            data: application,
            message: '招商申请提交成功，我们会尽快联系您'
        });
    } catch (error) {
        console.error('创建招商申请失败:', error);
        res.status(500).json({
            success: false,
            message: '提交申请失败，请稍后重试'
        });
    }
};

// 审核招商申请
exports.reviewRecruitmentApplication = async (req, res) => {
    try {
        const { applicationId } = req.params;
        const { action, reason, assignedLevel, notes } = req.body;

        if (!['approve', 'reject'].includes(action)) {
            return res.status(400).json({
                success: false,
                message: '无效的审核操作'
            });
        }

        const application = await memoryStore.findOne('recruitmentApplications', { id: applicationId });
        if (!application) {
            return res.status(404).json({
                success: false,
                message: '申请不存在'
            });
        }

        if (application.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: '该申请已被处理'
            });
        }

        const updateData = {
            status: action === 'approve' ? 'approved' : 'rejected',
            reviewedAt: new Date(),
            reviewedBy: req.user?.id || 'admin',
            reviewReason: reason,
            reviewNotes: notes,
            updatedAt: new Date()
        };

        if (action === 'approve') {
            // 创建代理商账户
            const agentData = await createAgentFromApplication(application, assignedLevel || application.expectedLevel);
            updateData.agentId = agentData.id;
            updateData.assignedLevel = assignedLevel || application.expectedLevel;

            // 发送欢迎邮件
            await sendWelcomeEmail(application.email, agentData);

            // 处理推荐奖励
            if (application.referralAgentId) {
                await processReferralReward(application.referralAgentId, agentData.id);
            }
        } else {
            // 发送拒绝邮件
            await sendRejectionEmail(application.email, reason);
        }

        await memoryStore.updateOne('recruitmentApplications', { id: applicationId }, updateData);

        res.json({
            success: true,
            message: action === 'approve' ? '申请已通过审核' : '申请已被拒绝'
        });
    } catch (error) {
        console.error('审核招商申请失败:', error);
        res.status(500).json({
            success: false,
            message: '审核申请失败'
        });
    }
};

// 批量审核招商申请
exports.batchReviewApplications = async (req, res) => {
    try {
        const { applicationIds, action, reason } = req.body;

        if (!applicationIds || !Array.isArray(applicationIds) || applicationIds.length === 0) {
            return res.status(400).json({
                success: false,
                message: '请选择要处理的申请'
            });
        }

        if (!['approve', 'reject'].includes(action)) {
            return res.status(400).json({
                success: false,
                message: '无效的审核操作'
            });
        }

        const results = [];

        for (const applicationId of applicationIds) {
            try {
                const application = await memoryStore.findOne('recruitmentApplications', { id: applicationId });

                if (!application || application.status !== 'pending') {
                    results.push({
                        applicationId,
                        success: false,
                        message: '申请不存在或已被处理'
                    });
                    continue;
                }

                const updateData = {
                    status: action === 'approve' ? 'approved' : 'rejected',
                    reviewedAt: new Date(),
                    reviewedBy: req.user?.id || 'admin',
                    reviewReason: reason,
                    updatedAt: new Date()
                };

                if (action === 'approve') {
                    const agentData = await createAgentFromApplication(application, application.expectedLevel);
                    updateData.agentId = agentData.id;
                    updateData.assignedLevel = application.expectedLevel;

                    await sendWelcomeEmail(application.email, agentData);

                    if (application.referralAgentId) {
                        await processReferralReward(application.referralAgentId, agentData.id);
                    }
                } else {
                    await sendRejectionEmail(application.email, reason);
                }

                await memoryStore.updateOne('recruitmentApplications', { id: applicationId }, updateData);

                results.push({
                    applicationId,
                    success: true,
                    message: action === 'approve' ? '审核通过' : '已拒绝'
                });
            } catch (error) {
                results.push({
                    applicationId,
                    success: false,
                    message: '处理失败: ' + error.message
                });
            }
        }

        const successCount = results.filter(r => r.success).length;

        res.json({
            success: true,
            data: results,
            message: `批量处理完成，成功处理 ${successCount}/${applicationIds.length} 个申请`
        });
    } catch (error) {
        console.error('批量审核申请失败:', error);
        res.status(500).json({
            success: false,
            message: '批量审核失败'
        });
    }
};

// 获取招商统计数据
exports.getRecruitmentStats = async (req, res) => {
    try {
        const { period = '30d' } = req.query;

        const applications = await memoryStore.findDocuments('recruitmentApplications');

        // 计算时间范围
        const now = new Date();
        let startDate;

        switch (period) {
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }

        const periodApplications = applications.filter(app =>
            new Date(app.createdAt) >= startDate
        );

        // 统计数据
        const stats = {
            total: periodApplications.length,
            pending: periodApplications.filter(app => app.status === 'pending').length,
            approved: periodApplications.filter(app => app.status === 'approved').length,
            rejected: periodApplications.filter(app => app.status === 'rejected').length,
            conversionRate: periodApplications.length > 0 ?
                (periodApplications.filter(app => app.status === 'approved').length / periodApplications.length * 100).toFixed(1) : 0,
            sourceDistribution: getSourceDistribution(periodApplications),
            regionDistribution: getRegionDistribution(periodApplications),
            levelDistribution: getLevelDistribution(periodApplications),
            dailyTrend: getDailyTrend(periodApplications, startDate, now)
        };

        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取招商统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取招商统计失败'
        });
    }
};

// ==================== 招商管理辅助函数 ====================

// 生成申请ID
function generateApplicationId() {
    return 'APP_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6).toUpperCase();
}

// 从申请创建代理商
async function createAgentFromApplication(application, level) {
    const agentId = generateAgentId();
    const referralCode = generateReferralCode();

    const agentData = {
        id: agentId,
        name: application.name,
        email: application.email,
        phone: application.phone,
        company: application.company,
        region: application.region,
        level: level,
        status: 'active',
        referralCode,
        joinDate: new Date(),
        source: 'recruitment',
        applicationId: application.id,
        createdAt: new Date(),
        updatedAt: new Date()
    };

    await memoryStore.insertOne('agents', agentData);

    return agentData;
}

// 生成推荐码
function generateReferralCode() {
    return 'REF' + Math.random().toString(36).substr(2, 6).toUpperCase();
}

// 记录推荐奖励
async function recordReferralReward(referralAgentId, applicationId) {
    await memoryStore.insertOne('referralRewards', {
        id: generateRewardId(),
        referralAgentId,
        applicationId,
        type: 'application_submitted',
        amount: 50, // 申请提交奖励
        status: 'pending',
        createdAt: new Date()
    });
}

// 处理推荐奖励
async function processReferralReward(referralAgentId, newAgentId) {
    const referralAgent = await memoryStore.findOne('agents', { id: referralAgentId });
    if (!referralAgent) return;

    const rewardAmount = AGENT_LEVELS[referralAgent.level].recruitmentBonus;

    await memoryStore.insertOne('commissions', {
        id: generateCommissionId(),
        agentId: referralAgentId,
        agentName: referralAgent.name,
        type: 'referral',
        amount: rewardAmount,
        status: 'pending',
        relatedAgentId: newAgentId,
        createdAt: new Date()
    });
}

// 发送申请确认邮件
async function sendApplicationConfirmationEmail(email, applicationId) {
    // 模拟发送邮件
    console.log(`发送确认邮件到 ${email}，申请ID: ${applicationId}`);
    return true;
}

// 发送欢迎邮件
async function sendWelcomeEmail(email, agentData) {
    // 模拟发送欢迎邮件
    console.log(`发送欢迎邮件到 ${email}，代理商ID: ${agentData.id}`);
    return true;
}

// 发送拒绝邮件
async function sendRejectionEmail(email, reason) {
    // 模拟发送拒绝邮件
    console.log(`发送拒绝邮件到 ${email}，原因: ${reason}`);
    return true;
}

// 获取来源分布
function getSourceDistribution(applications) {
    const distribution = {};
    applications.forEach(app => {
        distribution[app.source] = (distribution[app.source] || 0) + 1;
    });
    return distribution;
}

// 获取地区分布
function getRegionDistribution(applications) {
    const distribution = {};
    applications.forEach(app => {
        if (app.region) {
            distribution[app.region] = (distribution[app.region] || 0) + 1;
        }
    });
    return distribution;
}

// 获取级别分布
function getLevelDistribution(applications) {
    const distribution = {};
    applications.forEach(app => {
        const level = app.expectedLevel || 1;
        distribution[level] = (distribution[level] || 0) + 1;
    });
    return distribution;
}

// 获取每日趋势
function getDailyTrend(applications, startDate, endDate) {
    const trend = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const dayApplications = applications.filter(app => {
            const appDate = new Date(app.createdAt).toISOString().split('T')[0];
            return appDate === dateStr;
        });

        trend.push({
            date: dateStr,
            total: dayApplications.length,
            approved: dayApplications.filter(app => app.status === 'approved').length,
            rejected: dayApplications.filter(app => app.status === 'rejected').length
        });

        currentDate.setDate(currentDate.getDate() + 1);
    }

    return trend;
}

// 生成奖励ID
function generateRewardId() {
    return 'RWD_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
}

// 生成佣金ID
function generateCommissionId() {
    return 'comm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 生成代理商ID的辅助函数
function generateAgentId() {
    const prefix = 'AG';
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    return `${prefix}${timestamp}${random}`;
}

module.exports = exports;
