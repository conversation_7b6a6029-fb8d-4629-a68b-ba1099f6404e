const memoryStore = require('../models/memoryStore');
const crypto = require('crypto');

/**
 * 认证控制器
 * 处理登录、权限验证、登录日志等功能
 */

// 生成JWT Token (简化版本)
function generateToken(user) {
    const payload = {
        userId: user._id,
        username: user.username,
        roleId: user.roleId,
        timestamp: Date.now()
    };

    // 简化的token生成，实际应用中应使用JWT库
    return Buffer.from(JSON.stringify(payload)).toString('base64');
}

// 验证Token
function verifyToken(token) {
    try {
        const payload = JSON.parse(Buffer.from(token, 'base64').toString());
        // 检查token是否过期 (24小时)
        if (Date.now() - payload.timestamp > 24 * 60 * 60 * 1000) {
            return null;
        }
        return payload;
    } catch (error) {
        return null;
    }
}

// 记录登录日志
async function logLogin(userId, username, ip, userAgent, success, reason = null) {
    const loginLog = {
        userId,
        username,
        ip: ip || '127.0.0.1',
        userAgent: userAgent || 'Unknown',
        success,
        reason,
        timestamp: new Date(),
        location: '本地', // 实际应用中可以通过IP获取地理位置
        device: parseUserAgent(userAgent)
    };

    await memoryStore.createDocument('loginLogs', loginLog);
}

// 解析User-Agent
function parseUserAgent(userAgent) {
    if (!userAgent) return 'Unknown';

    if (userAgent.includes('Chrome')) return 'Chrome';
    if (userAgent.includes('Firefox')) return 'Firefox';
    if (userAgent.includes('Safari')) return 'Safari';
    if (userAgent.includes('Edge')) return 'Edge';

    return 'Unknown';
}

// 登录接口
exports.login = async (req, res) => {
    try {
        const { identifier, password } = req.body;
        const ip = req.ip || req.connection.remoteAddress;
        const userAgent = req.headers['user-agent'];

        if (!identifier || !password) {
            await logLogin(null, identifier, ip, userAgent, false, '用户名或密码为空');
            return res.json({
                success: false,
                message: '用户名和密码不能为空'
            });
        }

        // 查找用户
        const user = await memoryStore.findOneDocument('users', {
            $or: [
                { username: identifier },
                { email: identifier }
            ]
        });

        if (!user) {
            await logLogin(null, identifier, ip, userAgent, false, '用户不存在');
            return res.json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        // 验证密码 (实际应用中应使用加密)
        if (user.password !== password) {
            await logLogin(user._id, user.username, ip, userAgent, false, '密码错误');
            return res.json({
                success: false,
                message: '用户名或密码错误'
            });
        }

        // 检查用户状态
        if (user.status !== 'active') {
            await logLogin(user._id, user.username, ip, userAgent, false, '账户已被禁用');
            return res.json({
                success: false,
                message: '账户已被禁用，请联系管理员'
            });
        }

        // 更新用户登录信息
        await memoryStore.updateDocument('users', user._id, {
            lastLoginAt: new Date(),
            loginCount: (user.loginCount || 0) + 1
        });

        // 记录成功登录
        await logLogin(user._id, user.username, ip, userAgent, true);

        // 检查是否需要双因素认证
        if (user.twoFactorEnabled) {
            // 生成临时token用于双因素认证
            const tempToken = generateToken({ ...user, temp: true });

            return res.json({
                success: true,
                requiresTwoFactor: true,
                tempToken,
                message: '请输入双因素认证码'
            });
        }

        // 生成正式token
        const token = generateToken(user);

        res.json({
            success: true,
            token,
            user: {
                id: user._id,
                username: user.username,
                name: user.name,
                email: user.email,
                roleId: user.roleId
            },
            message: '登录成功'
        });

    } catch (error) {
        console.error('登录错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
};

// 双因素认证验证
exports.verify2FA = async (req, res) => {
    try {
        const { code, tempToken } = req.body;

        if (!code || !tempToken) {
            return res.json({
                success: false,
                message: '验证码和临时token不能为空'
            });
        }

        // 验证临时token
        const payload = verifyToken(tempToken);
        if (!payload || !payload.temp) {
            return res.json({
                success: false,
                message: '临时token无效或已过期'
            });
        }

        // 查找用户
        const user = await memoryStore.findOneDocument('users', { _id: payload.userId });
        if (!user) {
            return res.json({
                success: false,
                message: '用户不存在'
            });
        }

        // 验证双因素认证码 (简化版本，实际应用中应使用TOTP)
        if (code !== user.twoFactorSecret) {
            return res.json({
                success: false,
                message: '验证码错误'
            });
        }

        // 生成正式token
        const token = generateToken(user);

        res.json({
            success: true,
            token,
            user: {
                id: user._id,
                username: user.username,
                name: user.name,
                email: user.email,
                roleId: user.roleId
            },
            message: '验证成功'
        });

    } catch (error) {
        console.error('双因素认证错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
};

// 检查权限
exports.checkPermission = async (req, res) => {
    try {
        const { permission } = req.body;
        const token = req.headers.authorization?.replace('Bearer ', '');

        if (!token) {
            return res.json({
                success: false,
                hasPermission: false,
                message: '未提供认证token'
            });
        }

        const payload = verifyToken(token);
        if (!payload) {
            return res.json({
                success: false,
                hasPermission: false,
                message: 'Token无效或已过期'
            });
        }

        // 查找用户角色
        const role = await memoryStore.findOneDocument('roles', { _id: payload.roleId });
        if (!role) {
            return res.json({
                success: false,
                hasPermission: false,
                message: '角色不存在'
            });
        }

        // 检查权限
        const hasPermission = role.permissions.includes(permission);

        res.json({
            success: true,
            hasPermission,
            message: hasPermission ? '有权限' : '无权限'
        });

    } catch (error) {
        console.error('权限检查错误:', error);
        res.status(500).json({
            success: false,
            hasPermission: false,
            message: '服务器内部错误'
        });
    }
};

// 获取登录日志
exports.getLoginLogs = async (req, res) => {
    try {
        const { page = 1, limit = 20, userId, success } = req.query;

        let filter = {};
        if (userId) filter.userId = userId;
        if (success !== undefined) filter.success = success === 'true';

        const logs = await memoryStore.findDocuments('loginLogs', filter);

        // 按时间倒序排列
        logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));

        // 简单分页
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + parseInt(limit);
        const paginatedLogs = logs.slice(startIndex, endIndex);

        res.json({
            success: true,
            data: paginatedLogs,
            pagination: {
                page: parseInt(page),
                limit: parseInt(limit),
                total: logs.length,
                pages: Math.ceil(logs.length / limit)
            }
        });

    } catch (error) {
        console.error('获取登录日志错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
};

// 重新发送双因素认证码
exports.resend2FA = async (req, res) => {
    try {
        // 在实际应用中，这里应该重新发送验证码
        // 这里简化处理
        res.json({
            success: true,
            message: '验证码已重新发送'
        });
    } catch (error) {
        console.error('重发验证码错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
};

// 导出工具函数
exports.generateToken = generateToken;
exports.verifyToken = verifyToken;