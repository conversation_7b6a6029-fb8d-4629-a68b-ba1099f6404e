/**
 * 代理佣金控制器
 * 处理代理商佣金计算、结算、提现等功能
 */

const memoryStore = require('../models/memoryStore');

// 获取佣金配置
exports.getCommissionConfig = async (req, res) => {
    try {
        const config = await memoryStore.findOne('commission_config', {}) || {
            levels: [
                { level: 1, name: '普通代理', rate: 0.10, minSales: 0, maxSales: 50000 },
                { level: 2, name: '高级代理', rate: 0.15, minSales: 50000, maxSales: 200000 },
                { level: 3, name: '区域代理', rate: 0.20, minSales: 200000, maxSales: 1000000 },
                { level: 4, name: '总代理', rate: 0.25, minSales: 1000000, maxSales: null }
            ],
            settlementCycle: 'monthly', // weekly, monthly, quarterly
            minWithdraw: 100,
            maxWithdraw: 50000,
            withdrawFee: 0.01,
            autoSettle: true,
            settlementDay: 1 // 每月1号结算
        };
        
        res.json({
            success: true,
            data: config
        });
    } catch (error) {
        console.error('获取佣金配置失败:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金配置失败'
        });
    }
};

// 更新佣金配置
exports.updateCommissionConfig = async (req, res) => {
    try {
        const config = req.body;
        
        // 验证配置数据
        if (!config.levels || !Array.isArray(config.levels)) {
            return res.status(400).json({
                success: false,
                message: '佣金等级配置无效'
            });
        }
        
        await memoryStore.updateOne('commission_config', {}, config, { upsert: true });
        
        res.json({
            success: true,
            message: '佣金配置更新成功'
        });
    } catch (error) {
        console.error('更新佣金配置失败:', error);
        res.status(500).json({
            success: false,
            message: '更新佣金配置失败'
        });
    }
};

// 获取代理商佣金统计
exports.getAgentCommissionStats = async (req, res) => {
    try {
        const { agentId, startDate, endDate, period = '30d' } = req.query;
        
        // 计算时间范围
        const timeRange = calculateTimeRange(period, startDate, endDate);
        
        let query = {
            createdAt: { $gte: timeRange.start, $lte: timeRange.end }
        };
        
        if (agentId) {
            query.agentId = agentId;
        }
        
        // 获取佣金记录
        const commissions = await memoryStore.findDocuments('commissions', query);
        
        // 统计数据
        const stats = {
            totalCommission: commissions.reduce((sum, c) => sum + c.amount, 0),
            pendingCommission: commissions.filter(c => c.status === 'pending').reduce((sum, c) => sum + c.amount, 0),
            paidCommission: commissions.filter(c => c.status === 'paid').reduce((sum, c) => sum + c.amount, 0),
            totalOrders: commissions.length,
            avgCommissionRate: commissions.length > 0 ? 
                commissions.reduce((sum, c) => sum + c.rate, 0) / commissions.length : 0,
            monthlyTrend: generateMonthlyTrend(commissions, timeRange),
            topAgents: await getTopAgentsByCommission(timeRange, 10)
        };
        
        res.json({
            success: true,
            data: stats
        });
    } catch (error) {
        console.error('获取佣金统计失败:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金统计失败'
        });
    }
};

// 获取佣金记录列表
exports.getCommissionRecords = async (req, res) => {
    try {
        const { 
            page = 1, 
            limit = 20, 
            agentId, 
            status, 
            startDate, 
            endDate,
            sortBy = 'createdAt',
            sortOrder = 'desc'
        } = req.query;
        
        let query = {};
        
        if (agentId) query.agentId = agentId;
        if (status) query.status = status;
        if (startDate && endDate) {
            query.createdAt = { 
                $gte: new Date(startDate), 
                $lte: new Date(endDate) 
            };
        }
        
        const total = await memoryStore.countDocuments('commissions', query);
        const records = await memoryStore.findDocuments('commissions', query, {
            skip: (page - 1) * limit,
            limit: parseInt(limit),
            sort: { [sortBy]: sortOrder === 'desc' ? -1 : 1 }
        });
        
        // 关联代理商信息
        for (let record of records) {
            const agent = await memoryStore.findOne('agents', { id: record.agentId });
            record.agentInfo = agent ? {
                name: agent.name,
                email: agent.email,
                level: agent.level
            } : null;
        }
        
        res.json({
            success: true,
            data: {
                records,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取佣金记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金记录失败'
        });
    }
};

// 计算订单佣金
exports.calculateOrderCommission = async (req, res) => {
    try {
        const { orderId, agentId, orderAmount } = req.body;
        
        if (!orderId || !agentId || !orderAmount) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }
        
        // 获取代理商信息
        const agent = await memoryStore.findOne('agents', { id: agentId });
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }
        
        // 获取佣金配置
        const config = await memoryStore.findOne('commission_config', {});
        if (!config) {
            return res.status(500).json({
                success: false,
                message: '佣金配置未设置'
            });
        }
        
        // 根据代理商等级计算佣金率
        const levelConfig = config.levels.find(l => l.level === agent.level);
        if (!levelConfig) {
            return res.status(400).json({
                success: false,
                message: '代理商等级配置无效'
            });
        }
        
        const commissionAmount = orderAmount * levelConfig.rate;
        
        // 创建佣金记录
        const commission = {
            id: generateCommissionId(),
            orderId,
            agentId,
            agentLevel: agent.level,
            orderAmount,
            rate: levelConfig.rate,
            amount: commissionAmount,
            status: 'pending',
            createdAt: new Date(),
            settledAt: null,
            paidAt: null
        };
        
        await memoryStore.insertOne('commissions', commission);
        
        // 更新代理商统计
        await updateAgentCommissionStats(agentId, commissionAmount);
        
        res.json({
            success: true,
            data: commission,
            message: '佣金计算成功'
        });
    } catch (error) {
        console.error('计算订单佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '计算订单佣金失败'
        });
    }
};

// 批量结算佣金
exports.batchSettleCommissions = async (req, res) => {
    try {
        const { commissionIds, settlementDate } = req.body;
        
        if (!commissionIds || !Array.isArray(commissionIds)) {
            return res.status(400).json({
                success: false,
                message: '请选择要结算的佣金记录'
            });
        }
        
        const settleDate = settlementDate ? new Date(settlementDate) : new Date();
        let successCount = 0;
        let failCount = 0;
        
        for (let commissionId of commissionIds) {
            try {
                const commission = await memoryStore.findOne('commissions', { id: commissionId });
                if (commission && commission.status === 'pending') {
                    await memoryStore.updateOne('commissions', 
                        { id: commissionId }, 
                        { 
                            status: 'settled',
                            settledAt: settleDate
                        }
                    );
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (error) {
                failCount++;
            }
        }
        
        res.json({
            success: true,
            data: {
                successCount,
                failCount,
                total: commissionIds.length
            },
            message: `批量结算完成，成功${successCount}条，失败${failCount}条`
        });
    } catch (error) {
        console.error('批量结算佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '批量结算佣金失败'
        });
    }
};

// 获取提现申请列表
exports.getWithdrawRequests = async (req, res) => {
    try {
        const { page = 1, limit = 20, status, agentId } = req.query;
        
        let query = {};
        if (status) query.status = status;
        if (agentId) query.agentId = agentId;
        
        const total = await memoryStore.countDocuments('withdraw_requests', query);
        const requests = await memoryStore.findDocuments('withdraw_requests', query, {
            skip: (page - 1) * limit,
            limit: parseInt(limit),
            sort: { createdAt: -1 }
        });
        
        // 关联代理商信息
        for (let request of requests) {
            const agent = await memoryStore.findOne('agents', { id: request.agentId });
            request.agentInfo = agent ? {
                name: agent.name,
                email: agent.email,
                phone: agent.phone
            } : null;
        }
        
        res.json({
            success: true,
            data: {
                requests,
                pagination: {
                    page: parseInt(page),
                    limit: parseInt(limit),
                    total,
                    pages: Math.ceil(total / limit)
                }
            }
        });
    } catch (error) {
        console.error('获取提现申请失败:', error);
        res.status(500).json({
            success: false,
            message: '获取提现申请失败'
        });
    }
};

// 处理提现申请
exports.processWithdrawRequest = async (req, res) => {
    try {
        const { requestId } = req.params;
        const { action, reason, processedAmount } = req.body; // action: approve, reject
        
        const request = await memoryStore.findOne('withdraw_requests', { id: requestId });
        if (!request) {
            return res.status(404).json({
                success: false,
                message: '提现申请不存在'
            });
        }
        
        if (request.status !== 'pending') {
            return res.status(400).json({
                success: false,
                message: '该申请已处理'
            });
        }
        
        const updateData = {
            status: action === 'approve' ? 'approved' : 'rejected',
            processedAt: new Date(),
            processedBy: req.user?.id || 'admin',
            reason: reason || ''
        };
        
        if (action === 'approve' && processedAmount) {
            updateData.processedAmount = processedAmount;
        }
        
        await memoryStore.updateOne('withdraw_requests', { id: requestId }, updateData);
        
        // 如果批准，更新代理商余额
        if (action === 'approve') {
            const agent = await memoryStore.findOne('agents', { id: request.agentId });
            if (agent) {
                const newBalance = (agent.commissionBalance || 0) - (processedAmount || request.amount);
                await memoryStore.updateOne('agents', 
                    { id: request.agentId }, 
                    { commissionBalance: Math.max(0, newBalance) }
                );
            }
        }
        
        res.json({
            success: true,
            message: `提现申请${action === 'approve' ? '批准' : '拒绝'}成功`
        });
    } catch (error) {
        console.error('处理提现申请失败:', error);
        res.status(500).json({
            success: false,
            message: '处理提现申请失败'
        });
    }
};

// 辅助函数
function calculateTimeRange(period, startDate, endDate) {
    const now = new Date();
    let start, end;
    
    if (startDate && endDate) {
        start = new Date(startDate);
        end = new Date(endDate);
    } else {
        end = now;
        switch (period) {
            case '7d':
                start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
    }
    
    return { start, end };
}

function generateCommissionId() {
    return 'comm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateMonthlyTrend(commissions, timeRange) {
    // 生成月度趋势数据
    const months = [];
    const current = new Date(timeRange.start);
    
    while (current <= timeRange.end) {
        const monthKey = current.toISOString().substr(0, 7); // YYYY-MM
        const monthCommissions = commissions.filter(c => 
            c.createdAt.toISOString().substr(0, 7) === monthKey
        );
        
        months.push({
            month: monthKey,
            amount: monthCommissions.reduce((sum, c) => sum + c.amount, 0),
            count: monthCommissions.length
        });
        
        current.setMonth(current.getMonth() + 1);
    }
    
    return months;
}

async function getTopAgentsByCommission(timeRange, limit) {
    const commissions = await memoryStore.findDocuments('commissions', {
        createdAt: { $gte: timeRange.start, $lte: timeRange.end }
    });
    
    const agentStats = {};
    
    commissions.forEach(c => {
        if (!agentStats[c.agentId]) {
            agentStats[c.agentId] = { agentId: c.agentId, totalCommission: 0, orderCount: 0 };
        }
        agentStats[c.agentId].totalCommission += c.amount;
        agentStats[c.agentId].orderCount++;
    });
    
    const sortedAgents = Object.values(agentStats)
        .sort((a, b) => b.totalCommission - a.totalCommission)
        .slice(0, limit);
    
    // 关联代理商信息
    for (let stat of sortedAgents) {
        const agent = await memoryStore.findOne('agents', { id: stat.agentId });
        stat.agentName = agent ? agent.name : '未知代理商';
    }
    
    return sortedAgents;
}

async function updateAgentCommissionStats(agentId, commissionAmount) {
    const agent = await memoryStore.findOne('agents', { id: agentId });
    if (agent) {
        const newBalance = (agent.commissionBalance || 0) + commissionAmount;
        const newTotal = (agent.totalCommission || 0) + commissionAmount;
        
        await memoryStore.updateOne('agents', 
            { id: agentId }, 
            { 
                commissionBalance: newBalance,
                totalCommission: newTotal
            }
        );
    }
}

// ==================== 完整佣金计算引擎 ====================

// 多级佣金计算规则
const COMMISSION_RULES = {
    // 直接佣金：代理商自己销售获得的佣金
    direct: {
        1: 0.05, // 5%
        2: 0.08, // 8%
        3: 0.12, // 12%
        4: 0.15, // 15%
        5: 0.20  // 20%
    },
    // 间接佣金：下级代理商销售，上级获得的佣金
    indirect: {
        1: 0.01, // 1%
        2: 0.02, // 2%
        3: 0.03, // 3%
        4: 0.04, // 4%
        5: 0.05  // 5%
    },
    // 团队佣金：整个团队销售的额外奖励
    team: {
        3: 0.005, // 0.5%
        4: 0.01,  // 1%
        5: 0.015  // 1.5%
    },
    // 业绩奖励：达到特定业绩目标的奖励
    performance: {
        monthly: {
            50000: 1000,   // 月销售5万奖励1000元
            100000: 3000,  // 月销售10万奖励3000元
            200000: 8000,  // 月销售20万奖励8000元
            500000: 20000  // 月销售50万奖励20000元
        },
        quarterly: {
            150000: 5000,   // 季度销售15万奖励5000元
            300000: 15000,  // 季度销售30万奖励15000元
            600000: 40000,  // 季度销售60万奖励40000元
            1500000: 100000 // 季度销售150万奖励100000元
        }
    }
};

// 实时计算订单佣金
exports.calculateOrderCommission = async (req, res) => {
    try {
        const { orderId, agentId, orderAmount, productType = 'standard' } = req.body;

        if (!orderId || !agentId || !orderAmount) {
            return res.status(400).json({
                success: false,
                message: '缺少必要参数'
            });
        }

        // 获取代理商信息
        const agent = await memoryStore.findOne('agents', { id: agentId });
        if (!agent) {
            return res.status(404).json({
                success: false,
                message: '代理商不存在'
            });
        }

        // 计算多级佣金
        const commissionResult = await calculateMultiLevelCommission(agentId, orderAmount, orderId, productType);

        // 保存佣金记录
        for (const commission of commissionResult.commissions) {
            await memoryStore.insertOne('commissions', {
                id: generateCommissionId(),
                ...commission,
                orderId,
                orderAmount,
                productType,
                status: 'pending',
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }

        res.json({
            success: true,
            data: commissionResult,
            message: '佣金计算完成'
        });
    } catch (error) {
        console.error('计算订单佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '计算订单佣金失败'
        });
    }
};

// 批量计算佣金
exports.batchCalculateCommission = async (req, res) => {
    try {
        const { orders } = req.body;

        if (!orders || !Array.isArray(orders)) {
            return res.status(400).json({
                success: false,
                message: '订单数据无效'
            });
        }

        const results = [];
        let totalCommission = 0;

        for (const order of orders) {
            try {
                const commissionResult = await calculateMultiLevelCommission(
                    order.agentId,
                    order.orderAmount,
                    order.orderId,
                    order.productType
                );

                // 保存佣金记录
                for (const commission of commissionResult.commissions) {
                    await memoryStore.insertOne('commissions', {
                        id: generateCommissionId(),
                        ...commission,
                        orderId: order.orderId,
                        orderAmount: order.orderAmount,
                        productType: order.productType,
                        status: 'pending',
                        createdAt: new Date(),
                        updatedAt: new Date()
                    });
                }

                results.push({
                    orderId: order.orderId,
                    success: true,
                    commissionAmount: commissionResult.totalCommission
                });

                totalCommission += commissionResult.totalCommission;
            } catch (error) {
                results.push({
                    orderId: order.orderId,
                    success: false,
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            data: {
                results,
                totalOrders: orders.length,
                successCount: results.filter(r => r.success).length,
                totalCommission
            },
            message: '批量佣金计算完成'
        });
    } catch (error) {
        console.error('批量计算佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '批量计算佣金失败'
        });
    }
};

// 自动结算佣金
exports.autoSettleCommissions = async (req, res) => {
    try {
        const { settlementDate, agentIds } = req.body;

        // 获取待结算的佣金
        let query = { status: 'pending' };
        if (agentIds && agentIds.length > 0) {
            query.agentId = { $in: agentIds };
        }

        const pendingCommissions = await memoryStore.findDocuments('commissions', query);

        if (pendingCommissions.length === 0) {
            return res.json({
                success: true,
                data: { settledCount: 0, totalAmount: 0 },
                message: '没有待结算的佣金'
            });
        }

        // 按代理商分组
        const commissionsByAgent = {};
        pendingCommissions.forEach(commission => {
            if (!commissionsByAgent[commission.agentId]) {
                commissionsByAgent[commission.agentId] = [];
            }
            commissionsByAgent[commission.agentId].push(commission);
        });

        const settlementResults = [];
        let totalSettledAmount = 0;

        // 为每个代理商创建结算记录
        for (const [agentId, commissions] of Object.entries(commissionsByAgent)) {
            const agent = await memoryStore.findOne('agents', { id: agentId });
            if (!agent) continue;

            const totalAmount = commissions.reduce((sum, c) => sum + c.amount, 0);
            const settlementId = generateSettlementId();

            // 创建结算记录
            await memoryStore.insertOne('commission_settlements', {
                id: settlementId,
                agentId,
                agentName: agent.name,
                commissionIds: commissions.map(c => c.id),
                totalAmount,
                settlementDate: settlementDate || new Date(),
                status: 'settled',
                createdAt: new Date(),
                updatedAt: new Date()
            });

            // 更新佣金状态
            for (const commission of commissions) {
                await memoryStore.updateOne('commissions', { id: commission.id }, {
                    status: 'settled',
                    settlementId,
                    settledAt: new Date(),
                    updatedAt: new Date()
                });
            }

            // 更新代理商佣金余额
            await updateAgentCommissionBalance(agentId, totalAmount);

            settlementResults.push({
                agentId,
                agentName: agent.name,
                commissionCount: commissions.length,
                totalAmount,
                settlementId
            });

            totalSettledAmount += totalAmount;
        }

        res.json({
            success: true,
            data: {
                settledCount: pendingCommissions.length,
                totalAmount: totalSettledAmount,
                agentCount: Object.keys(commissionsByAgent).length,
                settlements: settlementResults
            },
            message: '佣金结算完成'
        });
    } catch (error) {
        console.error('自动结算佣金失败:', error);
        res.status(500).json({
            success: false,
            message: '自动结算佣金失败'
        });
    }
};

// 计算业绩奖励
exports.calculatePerformanceBonus = async (req, res) => {
    try {
        const { period = 'monthly', agentId } = req.query;

        // 计算时间范围
        const now = new Date();
        let startDate, endDate;

        if (period === 'monthly') {
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
            endDate = new Date(now.getFullYear(), now.getMonth() + 1, 0);
        } else if (period === 'quarterly') {
            const quarter = Math.floor(now.getMonth() / 3);
            startDate = new Date(now.getFullYear(), quarter * 3, 1);
            endDate = new Date(now.getFullYear(), quarter * 3 + 3, 0);
        }

        // 获取代理商列表
        let agents;
        if (agentId) {
            const agent = await memoryStore.findOne('agents', { id: agentId });
            agents = agent ? [agent] : [];
        } else {
            agents = await memoryStore.findDocuments('agents', { status: 'active' });
        }

        const bonusResults = [];

        for (const agent of agents) {
            // 计算代理商在指定期间的销售额
            const sales = await calculateAgentSales(agent.id, startDate, endDate);

            // 计算业绩奖励
            const bonus = calculatePerformanceBonus(sales, period);

            if (bonus > 0) {
                // 创建业绩奖励记录
                const bonusId = generateBonusId();
                await memoryStore.insertOne('performance_bonuses', {
                    id: bonusId,
                    agentId: agent.id,
                    agentName: agent.name,
                    period,
                    startDate,
                    endDate,
                    salesAmount: sales,
                    bonusAmount: bonus,
                    status: 'pending',
                    createdAt: new Date(),
                    updatedAt: new Date()
                });

                bonusResults.push({
                    agentId: agent.id,
                    agentName: agent.name,
                    salesAmount: sales,
                    bonusAmount: bonus,
                    bonusId
                });
            }
        }

        res.json({
            success: true,
            data: {
                period,
                startDate,
                endDate,
                bonuses: bonusResults,
                totalBonus: bonusResults.reduce((sum, b) => sum + b.bonusAmount, 0)
            },
            message: '业绩奖励计算完成'
        });
    } catch (error) {
        console.error('计算业绩奖励失败:', error);
        res.status(500).json({
            success: false,
            message: '计算业绩奖励失败'
        });
    }
};

// 获取佣金分析报告
exports.getCommissionAnalysis = async (req, res) => {
    try {
        const { period = '30d', agentId } = req.query;

        // 计算时间范围
        const now = new Date();
        let startDate;

        switch (period) {
            case '7d':
                startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
            case '90d':
                startDate = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
                break;
            default:
                startDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        }

        // 构建查询条件
        let query = {
            createdAt: { $gte: startDate }
        };

        if (agentId) {
            query.agentId = agentId;
        }

        const commissions = await memoryStore.findDocuments('commissions', query);

        // 分析数据
        const analysis = {
            totalCommission: commissions.reduce((sum, c) => sum + c.amount, 0),
            totalOrders: new Set(commissions.map(c => c.orderId)).size,
            avgCommissionPerOrder: 0,
            commissionByType: {},
            commissionByLevel: {},
            commissionByStatus: {},
            dailyTrend: [],
            topAgents: []
        };

        // 计算平均佣金
        if (analysis.totalOrders > 0) {
            analysis.avgCommissionPerOrder = analysis.totalCommission / analysis.totalOrders;
        }

        // 按类型分组
        commissions.forEach(commission => {
            const type = commission.type || 'direct';
            analysis.commissionByType[type] = (analysis.commissionByType[type] || 0) + commission.amount;

            const level = commission.agentLevel || 1;
            analysis.commissionByLevel[level] = (analysis.commissionByLevel[level] || 0) + commission.amount;

            const status = commission.status;
            analysis.commissionByStatus[status] = (analysis.commissionByStatus[status] || 0) + commission.amount;
        });

        // 计算每日趋势
        analysis.dailyTrend = calculateDailyCommissionTrend(commissions, startDate, now);

        // 计算顶级代理商
        if (!agentId) {
            analysis.topAgents = calculateTopAgents(commissions);
        }

        res.json({
            success: true,
            data: analysis
        });
    } catch (error) {
        console.error('获取佣金分析失败:', error);
        res.status(500).json({
            success: false,
            message: '获取佣金分析失败'
        });
    }
};

// ==================== 佣金计算引擎辅助函数 ====================

// 计算多级佣金
async function calculateMultiLevelCommission(agentId, orderAmount, orderId, productType = 'standard') {
    const commissions = [];

    // 获取代理商信息
    const agent = await memoryStore.findOne('agents', { id: agentId });
    if (!agent) {
        throw new Error('代理商不存在');
    }

    // 获取上级代理商链
    const uplineChain = await getUplineChain(agentId);

    // 产品类型系数
    const productMultiplier = getProductMultiplier(productType);

    // 1. 直接佣金（销售代理商）
    const directRate = COMMISSION_RULES.direct[agent.level] * productMultiplier;
    const directCommission = orderAmount * directRate;

    commissions.push({
        agentId: agent.id,
        agentName: agent.name,
        agentLevel: agent.level,
        type: 'direct',
        rate: directRate,
        amount: directCommission,
        description: '直接销售佣金'
    });

    // 2. 间接佣金（上级代理商）
    for (let i = 0; i < uplineChain.length && i < 3; i++) {
        const upline = uplineChain[i];
        const indirectRate = COMMISSION_RULES.indirect[upline.level] * productMultiplier;
        const indirectCommission = orderAmount * indirectRate;

        if (indirectCommission > 0) {
            commissions.push({
                agentId: upline.id,
                agentName: upline.name,
                agentLevel: upline.level,
                type: 'indirect',
                rate: indirectRate,
                amount: indirectCommission,
                description: `${i + 1}级间接佣金`,
                fromAgentId: agentId
            });
        }
    }

    // 3. 团队佣金（高级代理商）
    for (const upline of uplineChain) {
        if (COMMISSION_RULES.team[upline.level]) {
            const teamRate = COMMISSION_RULES.team[upline.level] * productMultiplier;
            const teamCommission = orderAmount * teamRate;

            commissions.push({
                agentId: upline.id,
                agentName: upline.name,
                agentLevel: upline.level,
                type: 'team',
                rate: teamRate,
                amount: teamCommission,
                description: '团队管理佣金',
                fromAgentId: agentId
            });
        }
    }

    return {
        orderId,
        orderAmount,
        productType,
        totalCommission: commissions.reduce((sum, c) => sum + c.amount, 0),
        commissions
    };
}

// 获取上级代理商链
async function getUplineChain(agentId) {
    const chain = [];
    let currentAgentId = agentId;

    while (currentAgentId && chain.length < 5) {
        const agent = await memoryStore.findOne('agents', { id: currentAgentId });
        if (!agent || !agent.uplineId) break;

        const upline = await memoryStore.findOne('agents', { id: agent.uplineId });
        if (!upline) break;

        chain.push(upline);
        currentAgentId = upline.uplineId;
    }

    return chain;
}

// 获取产品类型系数
function getProductMultiplier(productType) {
    const multipliers = {
        'standard': 1.0,
        'premium': 1.5,
        'enterprise': 2.0,
        'custom': 2.5
    };
    return multipliers[productType] || 1.0;
}

// 计算代理商销售额
async function calculateAgentSales(agentId, startDate, endDate) {
    // 这里应该从订单系统获取真实销售数据
    // 现在返回模拟数据
    return Math.floor(Math.random() * 500000) + 50000;
}

// 计算业绩奖励
function calculatePerformanceBonus(salesAmount, period) {
    const bonusRules = COMMISSION_RULES.performance[period];
    if (!bonusRules) return 0;

    let bonus = 0;
    const sortedThresholds = Object.keys(bonusRules).map(Number).sort((a, b) => b - a);

    for (const threshold of sortedThresholds) {
        if (salesAmount >= threshold) {
            bonus = bonusRules[threshold];
            break;
        }
    }

    return bonus;
}

// 更新代理商佣金余额
async function updateAgentCommissionBalance(agentId, amount) {
    const agent = await memoryStore.findOne('agents', { id: agentId });
    if (!agent) return;

    const newBalance = (agent.commissionBalance || 0) + amount;
    const newTotal = (agent.totalCommission || 0) + amount;

    await memoryStore.updateOne('agents', { id: agentId }, {
        commissionBalance: newBalance,
        totalCommission: newTotal,
        updatedAt: new Date()
    });
}

// 计算每日佣金趋势
function calculateDailyCommissionTrend(commissions, startDate, endDate) {
    const trend = [];
    const currentDate = new Date(startDate);

    while (currentDate <= endDate) {
        const dateStr = currentDate.toISOString().split('T')[0];
        const dayCommissions = commissions.filter(c => {
            const commissionDate = new Date(c.createdAt).toISOString().split('T')[0];
            return commissionDate === dateStr;
        });

        trend.push({
            date: dateStr,
            totalAmount: dayCommissions.reduce((sum, c) => sum + c.amount, 0),
            orderCount: new Set(dayCommissions.map(c => c.orderId)).size,
            commissionCount: dayCommissions.length
        });

        currentDate.setDate(currentDate.getDate() + 1);
    }

    return trend;
}

// 计算顶级代理商
function calculateTopAgents(commissions) {
    const agentStats = {};

    commissions.forEach(commission => {
        if (!agentStats[commission.agentId]) {
            agentStats[commission.agentId] = {
                agentId: commission.agentId,
                agentName: commission.agentName,
                totalCommission: 0,
                orderCount: new Set(),
                commissionCount: 0
            };
        }

        agentStats[commission.agentId].totalCommission += commission.amount;
        agentStats[commission.agentId].orderCount.add(commission.orderId);
        agentStats[commission.agentId].commissionCount++;
    });

    return Object.values(agentStats)
        .map(stat => ({
            ...stat,
            orderCount: stat.orderCount.size
        }))
        .sort((a, b) => b.totalCommission - a.totalCommission)
        .slice(0, 10);
}

// 生成ID的辅助函数
function generateCommissionId() {
    return 'comm_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

function generateSettlementId() {
    return 'settle_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
}

function generateBonusId() {
    return 'bonus_' + Date.now() + '_' + Math.random().toString(36).substr(2, 6);
}

module.exports = exports;
