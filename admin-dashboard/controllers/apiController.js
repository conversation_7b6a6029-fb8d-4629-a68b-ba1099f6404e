const memoryStore = require('../models/memoryStore');

/**
 * API配置控制器
 * 处理谷歌API配置、支付接口、API统计、密钥管理、Webhook等功能
 */

// 获取谷歌API配置
exports.getGoogleApiConfig = async (req, res) => {
    try {
        let config = await memoryStore.findOneDocument('settings', { type: 'google_api' });
        
        if (!config) {
            // 创建默认谷歌API配置
            config = await memoryStore.createDocument('settings', {
                type: 'google_api',
                data: {
                    translationApiKey: '',
                    documentAiApiKey: '',
                    visionApiKey: '',
                    speechApiKey: '',
                    projectId: '',
                    enabled: false,
                    quotaLimits: {
                        translation: 1000000,
                        documentAi: 100000,
                        vision: 50000,
                        speech: 30000
                    }
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: config.data
        });
        
    } catch (error) {
        console.error('获取谷歌API配置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取谷歌API配置失败'
        });
    }
};

// 更新谷歌API配置
exports.updateGoogleApiConfig = async (req, res) => {
    try {
        let config = await memoryStore.findOneDocument('settings', { type: 'google_api' });
        
        if (config) {
            config = await memoryStore.updateDocument('settings', config._id, {
                data: req.body,
                updatedAt: new Date()
            });
        } else {
            config = await memoryStore.createDocument('settings', {
                type: 'google_api',
                data: req.body,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: config.data,
            message: '谷歌API配置更新成功'
        });
        
    } catch (error) {
        console.error('更新谷歌API配置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新谷歌API配置失败'
        });
    }
};

// 获取支付接口配置
exports.getPaymentConfig = async (req, res) => {
    try {
        let config = await memoryStore.findOneDocument('settings', { type: 'payment' });
        
        if (!config) {
            // 创建默认支付配置
            config = await memoryStore.createDocument('settings', {
                type: 'payment',
                data: {
                    alipay: {
                        enabled: true,
                        appId: '202507095423512',
                        privateKey: '***',
                        publicKey: '***',
                        signType: 'RSA2',
                        gateway: 'https://openapi.alipay.com/gateway.do',
                        feeRate: 0.6
                    },
                    wechat: {
                        enabled: true,
                        mchId: '1425367890',
                        mchKey: '***',
                        appId: 'wx2507095423512',
                        appSecret: '***',
                        apiKey: '***',
                        feeRate: 0.6
                    },
                    paypal: {
                        enabled: false,
                        clientId: '',
                        clientSecret: '',
                        sandbox: true,
                        feeRate: 2.9
                    }
                },
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: config.data
        });
        
    } catch (error) {
        console.error('获取支付配置错误:', error);
        res.status(500).json({
            success: false,
            message: '获取支付配置失败'
        });
    }
};

// 更新支付接口配置
exports.updatePaymentConfig = async (req, res) => {
    try {
        const { provider } = req.params; // alipay, wechat, paypal
        
        let config = await memoryStore.findOneDocument('settings', { type: 'payment' });
        
        if (config) {
            const updatedData = { ...config.data };
            updatedData[provider] = { ...updatedData[provider], ...req.body };
            
            config = await memoryStore.updateDocument('settings', config._id, {
                data: updatedData,
                updatedAt: new Date()
            });
        } else {
            const newData = {};
            newData[provider] = req.body;
            
            config = await memoryStore.createDocument('settings', {
                type: 'payment',
                data: newData,
                createdAt: new Date(),
                updatedAt: new Date()
            });
        }
        
        res.json({
            success: true,
            data: config.data,
            message: '支付配置更新成功'
        });
        
    } catch (error) {
        console.error('更新支付配置错误:', error);
        res.status(500).json({
            success: false,
            message: '更新支付配置失败'
        });
    }
};

// 获取API使用统计
exports.getApiUsageStats = async (req, res) => {
    try {
        const { days = 30 } = req.query;
        
        // 模拟API使用统计数据
        const stats = {
            totalCalls: 156789,
            successRate: 98.7,
            avgResponseTime: 245,
            errorRate: 1.3,
            topEndpoints: [
                {
                    endpoint: '/api/documents/analyze',
                    calls: 12587,
                    successRate: 98.7,
                    avgResponseTime: 245,
                    errorRate: 1.3
                },
                {
                    endpoint: '/api/documents/translate',
                    calls: 8423,
                    successRate: 99.2,
                    avgResponseTime: 320,
                    errorRate: 0.8
                },
                {
                    endpoint: '/api/users/auth',
                    calls: 25142,
                    successRate: 99.8,
                    avgResponseTime: 87,
                    errorRate: 0.2
                },
                {
                    endpoint: '/api/payment/process',
                    calls: 3254,
                    successRate: 95.4,
                    avgResponseTime: 178,
                    errorRate: 4.6
                }
            ],
            dailyStats: generateDailyStats(parseInt(days))
        };
        
        res.json({
            success: true,
            data: stats
        });
        
    } catch (error) {
        console.error('获取API统计错误:', error);
        res.status(500).json({
            success: false,
            message: '获取API统计失败'
        });
    }
};

// 获取API密钥列表
exports.getApiKeys = async (req, res) => {
    try {
        const apiKeys = await memoryStore.findDocuments('apiKeys');
        
        res.json({
            success: true,
            data: apiKeys
        });
        
    } catch (error) {
        console.error('获取API密钥错误:', error);
        res.status(500).json({
            success: false,
            message: '获取API密钥失败'
        });
    }
};

// 创建API密钥
exports.createApiKey = async (req, res) => {
    try {
        const keyData = {
            ...req.body,
            key: generateApiKey(),
            status: 'active',
            createdAt: new Date(),
            updatedAt: new Date(),
            lastUsed: null,
            usageCount: 0
        };
        
        const apiKey = await memoryStore.createDocument('apiKeys', keyData);
        
        res.json({
            success: true,
            data: apiKey,
            message: 'API密钥创建成功'
        });
        
    } catch (error) {
        console.error('创建API密钥错误:', error);
        res.status(500).json({
            success: false,
            message: '创建API密钥失败'
        });
    }
};

// 更新API密钥
exports.updateApiKey = async (req, res) => {
    try {
        const { id } = req.params;
        const updateData = {
            ...req.body,
            updatedAt: new Date()
        };
        
        const apiKey = await memoryStore.updateDocument('apiKeys', id, updateData);
        
        if (!apiKey) {
            return res.status(404).json({
                success: false,
                message: 'API密钥不存在'
            });
        }
        
        res.json({
            success: true,
            data: apiKey,
            message: 'API密钥更新成功'
        });
        
    } catch (error) {
        console.error('更新API密钥错误:', error);
        res.status(500).json({
            success: false,
            message: '更新API密钥失败'
        });
    }
};

// 删除API密钥
exports.deleteApiKey = async (req, res) => {
    try {
        const { id } = req.params;
        
        const success = await memoryStore.deleteDocument('apiKeys', id);
        
        if (!success) {
            return res.status(404).json({
                success: false,
                message: 'API密钥不存在'
            });
        }
        
        res.json({
            success: true,
            message: 'API密钥删除成功'
        });
        
    } catch (error) {
        console.error('删除API密钥错误:', error);
        res.status(500).json({
            success: false,
            message: '删除API密钥失败'
        });
    }
};

// 生成API密钥的辅助函数
function generateApiKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = 'wp_';
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// 生成每日统计数据的辅助函数
function generateDailyStats(days) {
    const stats = [];
    const now = new Date();
    
    for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now);
        date.setDate(date.getDate() - i);
        
        stats.push({
            date: date.toISOString().split('T')[0],
            calls: Math.floor(Math.random() * 5000) + 1000,
            errors: Math.floor(Math.random() * 100) + 10,
            avgResponseTime: Math.floor(Math.random() * 200) + 150
        });
    }
    
    return stats;
}

// 测试API连接
exports.testApiConnection = async (req, res) => {
    try {
        const { type, config } = req.body;

        let testResult = {
            success: false,
            message: '',
            responseTime: 0,
            details: {}
        };

        const startTime = Date.now();

        // 模拟API测试
        switch (type) {
            case 'google_translation':
                // 模拟谷歌翻译API测试
                testResult = {
                    success: true,
                    message: '谷歌翻译API连接成功',
                    responseTime: Date.now() - startTime,
                    details: {
                        quota: '剩余配额: 95,234次',
                        region: '服务区域: 全球',
                        version: 'API版本: v3'
                    }
                };
                break;

            case 'payment_alipay':
                // 模拟支付宝API测试
                testResult = {
                    success: true,
                    message: '支付宝API连接成功',
                    responseTime: Date.now() - startTime,
                    details: {
                        merchantId: config.merchantId,
                        environment: config.environment || 'sandbox',
                        status: '商户状态正常'
                    }
                };
                break;

            case 'payment_wechat':
                // 模拟微信支付API测试
                testResult = {
                    success: true,
                    message: '微信支付API连接成功',
                    responseTime: Date.now() - startTime,
                    details: {
                        mchId: config.mchId,
                        environment: config.environment || 'sandbox',
                        status: '商户状态正常'
                    }
                };
                break;

            default:
                testResult = {
                    success: false,
                    message: '不支持的API类型',
                    responseTime: 0,
                    details: {}
                };
        }

        res.json({
            success: true,
            data: testResult
        });

    } catch (error) {
        console.error('测试API连接错误:', error);
        res.status(500).json({
            success: false,
            message: '测试API连接失败'
        });
    }
};

// 获取API密钥详情
exports.getApiKeyDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const apiKey = await memoryStore.findOneDocument('apiKeys', { _id: id });

        if (!apiKey) {
            return res.status(404).json({
                success: false,
                message: 'API密钥不存在'
            });
        }

        res.json({
            success: true,
            data: apiKey
        });

    } catch (error) {
        console.error('获取API密钥详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取API密钥详情失败'
        });
    }
};

// 轮换API密钥
exports.rotateApiKey = async (req, res) => {
    try {
        const { id } = req.params;

        const apiKey = await memoryStore.findOneDocument('apiKeys', { _id: id });
        if (!apiKey) {
            return res.status(404).json({
                success: false,
                message: 'API密钥不存在'
            });
        }

        // 生成新的API密钥
        const newKey = generateApiKey();

        const updatedApiKey = await memoryStore.updateDocument('apiKeys', id, {
            key: newKey,
            lastRotated: new Date(),
            updatedAt: new Date()
        });

        res.json({
            success: true,
            data: updatedApiKey,
            message: 'API密钥轮换成功'
        });

    } catch (error) {
        console.error('轮换API密钥错误:', error);
        res.status(500).json({
            success: false,
            message: '轮换API密钥失败'
        });
    }
};

// 获取Webhook详情
exports.getWebhookDetail = async (req, res) => {
    try {
        const { id } = req.params;
        const webhook = await memoryStore.findOneDocument('webhooks', { _id: id });

        if (!webhook) {
            return res.status(404).json({
                success: false,
                message: 'Webhook不存在'
            });
        }

        res.json({
            success: true,
            data: webhook
        });

    } catch (error) {
        console.error('获取Webhook详情错误:', error);
        res.status(500).json({
            success: false,
            message: '获取Webhook详情失败'
        });
    }
};

// 测试Webhook
exports.testWebhook = async (req, res) => {
    try {
        const { id } = req.params;

        const webhook = await memoryStore.findOneDocument('webhooks', { _id: id });
        if (!webhook) {
            return res.status(404).json({
                success: false,
                message: 'Webhook不存在'
            });
        }

        // 模拟Webhook测试
        const testPayload = {
            event: 'test',
            timestamp: new Date().toISOString(),
            data: {
                message: 'This is a test webhook'
            }
        };

        // 这里应该实际发送HTTP请求到webhook URL
        // 现在只是模拟成功响应
        const testResult = {
            success: true,
            statusCode: 200,
            responseTime: Math.floor(Math.random() * 500) + 100,
            response: 'OK'
        };

        res.json({
            success: true,
            data: testResult,
            message: 'Webhook测试成功'
        });

    } catch (error) {
        console.error('测试Webhook错误:', error);
        res.status(500).json({
            success: false,
            message: '测试Webhook失败'
        });
    }
};

// 生成API密钥的辅助函数
function generateApiKey() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = 'wp_';
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

module.exports = exports;
