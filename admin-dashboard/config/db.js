const mongoose = require('mongoose');
require('dotenv').config();

// 内存数据存储
let memoryStore = null;

const connectDB = async () => {
    try {
        // 使用环境变量中的MongoDB URI，如果不存在则使用默认值
        const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/writerpro_admin';
        
        const conn = await mongoose.connect(mongoURI, {
            useNewUrlParser: true,
            useUnifiedTopology: true
        });
        
        console.log(`MongoDB 连接成功: ${conn.connection.host}`);
        return conn;
    } catch (error) {
        console.error(`MongoDB 连接错误: ${error.message}`);
        // 使用内存数据库作为备选
        console.log('使用内存数据存储作为备选');
        
        // 动态导入内存数据存储模块
        if (!memoryStore) {
            memoryStore = require('../models/memoryStore');
        }
        
        return { isMemoryStore: true };
    }
};

// 检查是否使用内存数据存储
const isUsingMemoryStore = () => {
    return memoryStore !== null;
};

// 获取内存数据存储实例
const getMemoryStore = () => {
    return memoryStore;
};

module.exports = {
    connectDB,
    isUsingMemoryStore,
    getMemoryStore
}; 