/**
 * 响应式布局优化
 * 适配不同屏幕尺寸的设备
 */

/* 基础响应式断点 */
:root {
    --mobile-breakpoint: 768px;
    --tablet-breakpoint: 1024px;
    --desktop-breakpoint: 1200px;
    --large-desktop-breakpoint: 1400px;
}

/* 移动端优化 */
@media (max-width: 767px) {
    /* 侧边栏优化 */
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
        background: #fff;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    /* 主内容区域调整 */
    .main-content {
        margin-left: 0;
        padding: 0 15px;
    }
    
    /* 顶部导航栏 */
    .header {
        padding: 10px 15px;
        position: sticky;
        top: 0;
        z-index: 1030;
        background: #fff;
        border-bottom: 1px solid #e9ecef;
    }
    
    .menu-toggle {
        display: block !important;
        background: none;
        border: none;
        font-size: 1.2rem;
        color: #333;
        padding: 5px;
        margin-right: 10px;
    }
    
    /* 搜索栏优化 */
    .search-bar {
        display: none;
    }
    
    .search-bar.mobile-show {
        display: flex;
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: #fff;
        border-top: 1px solid #e9ecef;
        padding: 10px 15px;
    }
    
    /* 卡片布局 */
    .card {
        margin-bottom: 15px;
        border-radius: 8px;
    }
    
    .card-body {
        padding: 15px;
    }
    
    /* 表格响应式 */
    .table-responsive {
        border: none;
    }
    
    .table {
        font-size: 0.875rem;
    }
    
    .table th,
    .table td {
        padding: 8px 6px;
        white-space: nowrap;
    }
    
    /* 隐藏不重要的列 */
    .table .d-none-mobile {
        display: none !important;
    }
    
    /* 按钮组优化 */
    .btn-group {
        flex-wrap: wrap;
    }
    
    .btn-group .btn {
        margin-bottom: 5px;
    }
    
    /* 表单优化 */
    .form-row {
        flex-direction: column;
    }
    
    .form-group {
        margin-bottom: 15px;
    }
    
    .form-control {
        font-size: 16px; /* 防止iOS缩放 */
    }
    
    /* 模态框优化 */
    .modal-dialog {
        margin: 10px;
        max-width: calc(100% - 20px);
    }
    
    .modal-body {
        padding: 15px;
    }
    
    /* 统计卡片 */
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 10px;
    }
    
    .stat-card {
        padding: 15px;
        text-align: center;
    }
    
    .stat-card h3 {
        font-size: 1.5rem;
    }
    
    /* 图表容器 */
    .chart-container {
        height: 250px !important;
        margin-bottom: 20px;
    }
    
    /* 选项卡优化 */
    .nav-tabs {
        flex-wrap: nowrap;
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    .nav-tabs .nav-link {
        white-space: nowrap;
        padding: 8px 12px;
        font-size: 0.875rem;
    }
    
    /* 工具栏优化 */
    .toolbar {
        flex-direction: column;
        align-items: stretch;
    }
    
    .toolbar .btn {
        margin-bottom: 5px;
        width: 100%;
    }
    
    /* 分页优化 */
    .pagination {
        justify-content: center;
        flex-wrap: wrap;
    }
    
    .pagination .page-item {
        margin: 2px;
    }
}

/* 平板端优化 */
@media (min-width: 768px) and (max-width: 1023px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(3, 1fr);
    }
    
    .table th,
    .table td {
        padding: 10px 8px;
    }
    
    .chart-container {
        height: 300px;
    }
}

/* 桌面端优化 */
@media (min-width: 1024px) {
    .menu-toggle {
        display: none;
    }
    
    .sidebar {
        position: fixed;
        width: 250px;
    }
    
    .main-content {
        margin-left: 250px;
    }
    
    .stats-grid {
        grid-template-columns: repeat(4, 1fr);
    }
    
    .chart-container {
        height: 350px;
    }
    
    /* 大屏幕优化 */
    @media (min-width: 1400px) {
        .container-fluid {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        .stats-grid {
            grid-template-columns: repeat(5, 1fr);
        }
        
        .chart-container {
            height: 400px;
        }
    }
}

/* 语言切换器样式 */
.language-selector {
    position: relative;
    margin-left: auto;
}

.language-selector .dropdown-toggle {
    border: 1px solid #dee2e6;
    background: #fff;
    color: #495057;
    font-size: 0.875rem;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    gap: 5px;
}

.language-selector .dropdown-toggle:hover {
    background: #f8f9fa;
    border-color: #adb5bd;
}

.language-selector .dropdown-menu {
    min-width: 120px;
    border: 1px solid #dee2e6;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.language-selector .dropdown-item {
    padding: 8px 12px;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 8px;
}

.language-selector .dropdown-item:hover {
    background: #f8f9fa;
}

.language-selector .dropdown-item.active {
    background: #007bff;
    color: #fff;
}

/* 暗色主题支持 */
@media (prefers-color-scheme: dark) {
    .sidebar {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .main-content {
        background: #1a202c;
        color: #e2e8f0;
    }
    
    .card {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table th {
        border-color: #4a5568;
        background: #2d3748;
    }
    
    .table td {
        border-color: #4a5568;
    }
    
    .language-selector .dropdown-toggle {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .language-selector .dropdown-menu {
        background: #2d3748;
        border-color: #4a5568;
    }
    
    .language-selector .dropdown-item {
        color: #e2e8f0;
    }
    
    .language-selector .dropdown-item:hover {
        background: #4a5568;
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .header,
    .language-selector,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0;
        padding: 0;
    }
    
    .card {
        border: 1px solid #000;
        break-inside: avoid;
    }
    
    .table {
        font-size: 12px;
    }
    
    .chart-container {
        height: 200px;
    }
}

/* 无障碍优化 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* 高对比度模式 */
@media (prefers-contrast: high) {
    .card {
        border: 2px solid #000;
    }
    
    .btn {
        border: 2px solid #000;
    }
    
    .table th,
    .table td {
        border: 1px solid #000;
    }
}
