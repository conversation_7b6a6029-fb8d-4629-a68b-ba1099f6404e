/**
 * UI增强样式
 * 提供响应式布局、主题切换、动画效果等样式
 */

/* 主题变量 */
:root {
    --primary-color: #007bff;
    --secondary-color: #6c757d;
    --success-color: #28a745;
    --danger-color: #dc3545;
    --warning-color: #ffc107;
    --info-color: #17a2b8;
    --light-color: #f8f9fa;
    --dark-color: #343a40;
    
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --border-color: #dee2e6;
    
    --sidebar-width: 250px;
    --sidebar-collapsed-width: 60px;
    --header-height: 60px;
    
    --transition-speed: 0.3s;
    --border-radius: 0.375rem;
    --box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    --box-shadow-lg: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}

/* 暗色主题 */
[data-theme="dark"] {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --text-primary: #ffffff;
    --text-secondary: #b0b0b0;
    --border-color: #404040;
}

/* 基础样式重置 */
* {
    box-sizing: border-box;
}

body {
    background-color: var(--bg-secondary);
    color: var(--text-primary);
    transition: background-color var(--transition-speed), color var(--transition-speed);
}

/* 侧边栏响应式 */
.sidebar {
    width: var(--sidebar-width);
    transition: width var(--transition-speed), transform var(--transition-speed);
    background-color: var(--bg-primary);
    border-right: 1px solid var(--border-color);
}

.sidebar.collapsed {
    width: var(--sidebar-collapsed-width);
}

.sidebar.collapsed .sidebar-menu span {
    display: none;
}

.sidebar.collapsed .logo h1 {
    display: none;
}

/* 主内容区域适应 */
.main-content {
    margin-left: var(--sidebar-width);
    transition: margin-left var(--transition-speed);
    min-height: 100vh;
    background-color: var(--bg-secondary);
}

.sidebar.collapsed + .main-content {
    margin-left: var(--sidebar-collapsed-width);
}

/* 批量操作栏 */
.batch-action-bar {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
    padding: 15px 20px;
    display: flex;
    align-items: center;
    gap: 20px;
    z-index: 1000;
    animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

.batch-info {
    font-weight: 500;
    color: var(--text-primary);
}

.batch-actions {
    display: flex;
    gap: 10px;
}

/* 响应式表格增强 */
.table-responsive {
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
}

.data-table {
    background-color: var(--bg-primary);
    border-collapse: separate;
    border-spacing: 0;
}

.data-table th {
    background-color: var(--bg-secondary);
    border-bottom: 2px solid var(--border-color);
    font-weight: 600;
    position: sticky;
    top: 0;
    z-index: 10;
}

.data-table td {
    border-bottom: 1px solid var(--border-color);
    vertical-align: middle;
}

.data-table tr:hover {
    background-color: var(--bg-secondary);
}

/* 移动端表格优化 */
@media (max-width: 768px) {
    .mobile-table {
        font-size: 0.875rem;
    }
    
    .mobile-table th,
    .mobile-table td {
        padding: 0.5rem 0.25rem;
    }
    
    .mobile-table .btn-group {
        flex-direction: column;
        gap: 2px;
    }
    
    .mobile-table .btn-group .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.75rem;
    }
}

/* 加载状态 */
.loading {
    position: relative;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

[data-theme="dark"] .loading::after {
    background: rgba(0, 0, 0, 0.8);
}

/* 表单验证增强 */
.form-control.is-valid {
    border-color: var(--success-color);
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.25);
}

.form-control.is-invalid {
    border-color: var(--danger-color);
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
}

.invalid-feedback {
    display: block;
    color: var(--danger-color);
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

/* 搜索增强 */
.search-container {
    position: relative;
}

.search-clear {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: var(--text-secondary);
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-top: none;
    border-radius: 0 0 var(--border-radius) var(--border-radius);
    box-shadow: var(--box-shadow);
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
}

.search-suggestion {
    padding: 10px 15px;
    cursor: pointer;
    border-bottom: 1px solid var(--border-color);
}

.search-suggestion:hover {
    background-color: var(--bg-secondary);
}

.search-suggestion:last-child {
    border-bottom: none;
}

/* 卡片增强 */
.card {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: box-shadow var(--transition-speed);
}

.card:hover {
    box-shadow: var(--box-shadow-lg);
}

.card-header {
    background-color: var(--bg-secondary);
    border-bottom: 1px solid var(--border-color);
}

/* 按钮增强 */
.btn {
    border-radius: var(--border-radius);
    transition: all var(--transition-speed);
    font-weight: 500;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--box-shadow);
}

.btn:active {
    transform: translateY(0);
}

.btn-group .btn {
    border-radius: 0;
}

.btn-group .btn:first-child {
    border-radius: var(--border-radius) 0 0 var(--border-radius);
}

.btn-group .btn:last-child {
    border-radius: 0 var(--border-radius) var(--border-radius) 0;
}

/* 模态框增强 */
.modal-content {
    background-color: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow-lg);
}

.modal-header {
    border-bottom: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

.modal-footer {
    border-top: 1px solid var(--border-color);
    background-color: var(--bg-secondary);
}

/* 通知增强 */
.alert {
    border-radius: var(--border-radius);
    border: none;
    box-shadow: var(--box-shadow);
    animation: slideInRight 0.3s ease-out;
}

@keyframes slideInRight {
    from {
        opacity: 0;
        transform: translateX(100%);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

/* 主题切换按钮 */
.theme-toggle {
    background: none;
    border: 1px solid var(--border-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--text-primary);
    cursor: pointer;
    transition: all var(--transition-speed);
}

.theme-toggle:hover {
    background-color: var(--bg-secondary);
    transform: rotate(180deg);
}

/* 响应式设计 */
@media (max-width: 1200px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
}

@media (max-width: 992px) {
    .sidebar {
        position: fixed;
        left: -250px;
        z-index: 1050;
        height: 100vh;
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .main-content {
        margin-left: 0;
    }
    
    .batch-action-bar {
        left: 10px;
        right: 10px;
        transform: none;
        flex-direction: column;
        gap: 10px;
    }
}

@media (max-width: 768px) {
    .mobile-optimized .card {
        margin-bottom: 1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    .mobile-optimized .btn-group {
        flex-direction: column;
        width: 100%;
    }
    
    .mobile-optimized .btn-group .btn {
        border-radius: var(--border-radius);
        margin-bottom: 0.25rem;
    }
    
    .mobile-optimized .table-responsive {
        border: none;
        box-shadow: none;
    }
}

@media (max-width: 576px) {
    .batch-action-bar {
        bottom: 10px;
        left: 10px;
        right: 10px;
        padding: 10px;
    }
    
    .batch-actions {
        flex-wrap: wrap;
        gap: 5px;
    }
    
    .batch-actions .btn {
        flex: 1;
        min-width: 0;
        font-size: 0.75rem;
        padding: 0.375rem 0.5rem;
    }
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
    background: var(--border-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--text-secondary);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 打印样式 */
@media print {
    .sidebar,
    .batch-action-bar,
    .btn,
    .pagination {
        display: none !important;
    }
    
    .main-content {
        margin-left: 0 !important;
    }
    
    .card {
        border: 1px solid #000 !important;
        box-shadow: none !important;
    }
}
