/**
 * 批量操作组件样式
 */

.batch-actions-bar {
    position: sticky;
    top: 0;
    z-index: 100;
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0.375rem 0.375rem 0 0;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.batch-info {
    display: flex;
    align-items: center;
    font-weight: 500;
    color: #495057;
}

.batch-info i {
    color: #0d6efd;
}

.batch-buttons {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.batch-actions-bar .dropdown-menu {
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
    border: 1px solid #dee2e6;
}

.batch-actions-bar .dropdown-item {
    padding: 0.5rem 1rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.2s ease;
}

.batch-actions-bar .dropdown-item:hover {
    background-color: #f8f9fa;
    color: #0d6efd;
}

.batch-actions-bar .dropdown-item i {
    width: 1rem;
    text-align: center;
}

/* 批量选择复选框样式 */
.batch-checkbox,
.select-all {
    cursor: pointer;
    transform: scale(1.1);
}

.batch-checkbox:checked,
.select-all:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.batch-checkbox:indeterminate,
.select-all:indeterminate {
    background-color: #0d6efd;
    border-color: #0d6efd;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 20 20'%3e%3cpath fill='none' stroke='%23fff' stroke-linecap='round' stroke-linejoin='round' stroke-width='3' d='M6 10h8'/%3e%3c/svg%3e");
}

/* 表格行选中状态 */
.table tbody tr.selected {
    background-color: rgba(13, 110, 253, 0.1);
}

.table tbody tr.selected td {
    border-color: rgba(13, 110, 253, 0.2);
}

/* 批量操作进度模态框 */
#batchProgressModal .progress {
    height: 0.75rem;
}

#batchProgressModal .progress-bar {
    transition: width 0.3s ease;
}

/* 批量操作确认对话框 */
.batch-confirm-modal .modal-header {
    background-color: #fff3cd;
    border-bottom: 1px solid #ffeaa7;
}

.batch-confirm-modal .modal-title {
    color: #856404;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .batch-actions-bar {
        padding: 0.75rem !important;
    }
    
    .batch-actions-bar .d-flex {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }
    
    .batch-info {
        justify-content: center;
        text-align: center;
    }
    
    .batch-buttons {
        justify-content: center;
    }
    
    .batch-buttons .dropdown {
        flex: 1;
    }
    
    .batch-buttons .dropdown .btn {
        width: 100%;
    }
}

/* 批量操作动画效果 */
.batch-action-item {
    transition: all 0.2s ease;
}

.batch-action-item:hover {
    transform: translateY(-1px);
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

/* 批量操作成功/失败状态 */
.batch-result-success {
    color: #198754;
    background-color: #d1e7dd;
    border: 1px solid #badbcc;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin: 0.25rem 0;
}

.batch-result-error {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c2c7;
    border-radius: 0.375rem;
    padding: 0.5rem;
    margin: 0.25rem 0;
}

/* 批量操作工具提示 */
.batch-tooltip {
    position: relative;
}

.batch-tooltip::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: #000;
    color: #fff;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.2s ease;
    z-index: 1000;
}

.batch-tooltip:hover::after {
    opacity: 1;
    visibility: visible;
}

/* 键盘快捷键提示 */
.keyboard-shortcut {
    font-size: 0.75rem;
    color: #6c757d;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 0.125rem 0.25rem;
    margin-left: 0.5rem;
}

/* 批量操作统计信息 */
.batch-stats {
    display: flex;
    gap: 1rem;
    padding: 0.5rem;
    background-color: #f8f9fa;
    border-radius: 0.375rem;
    margin: 0.5rem 0;
}

.batch-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.batch-stat-number {
    font-size: 1.25rem;
    font-weight: bold;
    color: #0d6efd;
}

.batch-stat-label {
    font-size: 0.75rem;
    color: #6c757d;
}

/* 暗色主题支持 */
[data-bs-theme="dark"] .batch-actions-bar {
    background-color: #212529;
    border-bottom-color: #495057;
}

[data-bs-theme="dark"] .batch-info {
    color: #adb5bd;
}

[data-bs-theme="dark"] .batch-actions-bar .dropdown-menu {
    background-color: #343a40;
    border-color: #495057;
}

[data-bs-theme="dark"] .batch-actions-bar .dropdown-item {
    color: #adb5bd;
}

[data-bs-theme="dark"] .batch-actions-bar .dropdown-item:hover {
    background-color: #495057;
    color: #fff;
}

[data-bs-theme="dark"] .table tbody tr.selected {
    background-color: rgba(13, 110, 253, 0.2);
}

[data-bs-theme="dark"] .batch-result-success {
    color: #75b798;
    background-color: #051b11;
    border-color: #0a3622;
}

[data-bs-theme="dark"] .batch-result-error {
    color: #ea868f;
    background-color: #2c0b0e;
    border-color: #58151c;
}
