const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isUsingMemoryStore, getMemoryStore } = require('../config/db');

const UserSchema = new Schema({
    username: {
        type: String,
        required: true,
        unique: true
    },
    password: {
        type: String,
        required: true
    },
    name: {
        type: String,
        required: true
    },
    email: {
        type: String,
        required: true,
        unique: true
    },
    role: {
        type: String,
        enum: ['admin', 'finance_admin', 'content_admin', 'customer_service', 'agent'],
        default: 'customer_service'
    },
    status: {
        type: String,
        enum: ['active', 'inactive', 'locked'],
        default: 'active'
    },
    avatar: {
        type: String,
        default: ''
    },
    lastLogin: {
        type: Date,
        default: null
    },
    twoFactorEnabled: {
        type: Boolean,
        default: false
    },
    twoFactorSecret: {
        type: String,
        default: ''
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// 更新时自动更新updatedAt字段
UserSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

// 内存存储适配器
class UserMemoryAdapter {
    static async findOne(filter) {
        const store = getMemoryStore();
        return store.findOneDocument('users', filter);
    }
    
    static async find(filter) {
        const store = getMemoryStore();
        return store.findDocuments('users', filter);
    }
    
    static async findById(id) {
        const store = getMemoryStore();
        return store.findOneDocument('users', { _id: id });
    }
    
    static async countDocuments(filter) {
        const store = getMemoryStore();
        return store.countDocuments('users', filter);
    }
    
    static async create(data) {
        const store = getMemoryStore();
        return store.createDocument('users', data);
    }
    
    static async findByIdAndUpdate(id, update) {
        const store = getMemoryStore();
        return store.updateDocument('users', id, update);
    }
    
    static async findByIdAndDelete(id) {
        const store = getMemoryStore();
        return store.deleteDocument('users', id);
    }
}

// 根据存储类型导出不同的模型
module.exports = isUsingMemoryStore() 
    ? UserMemoryAdapter 
    : mongoose.model('User', UserSchema); 