const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const IpBlacklistSchema = new Schema({
    ip: {
        type: String,
        required: true,
        unique: true
    },
    reason: {
        type: String,
        required: true
    },
    createdBy: {
        type: String,
        default: '系统自动'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    expiryDate: {
        type: Date,
        default: null // null表示永久拉黑
    },
    status: {
        type: String,
        enum: ['active', 'expired'],
        default: 'active'
    }
});

// 检查IP是否在黑名单中的静态方法
IpBlacklistSchema.statics.isBlacklisted = async function(ip) {
    const blacklistedIp = await this.findOne({ 
        ip, 
        status: 'active',
        $or: [
            { expiryDate: null },
            { expiryDate: { $gt: new Date() } }
        ]
    });
    
    return !!blacklistedIp;
};

module.exports = mongoose.model('IpBlacklist', IpBlacklistSchema); 