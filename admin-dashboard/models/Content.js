const mongoose = require('mongoose');
const Schema = mongoose.Schema;
const { isUsingMemoryStore, getMemoryStore } = require('../config/db');

const ContentSchema = new Schema({
    title: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['agreement', 'announcement', 'page', 'qrcode'],
        required: true
    },
    content: {
        type: String,
        required: true
    },
    path: {
        type: String,
        default: ''
    },
    version: {
        type: String,
        default: 'v1.0'
    },
    status: {
        type: String,
        enum: ['published', 'draft'],
        default: 'draft'
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    publishedAt: {
        type: Date
    }
});

// 更新时自动更新updatedAt字段
ContentSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    
    // 如果状态变为published，设置publishedAt
    if (this.status === 'published' && !this.publishedAt) {
        this.publishedAt = Date.now();
    }
    
    next();
});

// 获取最新版本的内容
ContentSchema.statics.getLatestByType = async function(type) {
    return this.findOne({ type, status: 'published' })
        .sort({ publishedAt: -1 })
        .exec();
};

// 获取内容历史版本
ContentSchema.statics.getHistory = async function(type, title) {
    return this.find({ type, title, status: 'published' })
        .sort({ publishedAt: -1 })
        .exec();
};

// 内存存储适配器
class ContentMemoryAdapter {
    static async findOne(filter) {
        const store = getMemoryStore();
        return store.findOneDocument('contents', filter);
    }
    
    static async find(filter) {
        const store = getMemoryStore();
        return store.findDocuments('contents', filter);
    }
    
    static async findById(id) {
        const store = getMemoryStore();
        return store.findOneDocument('contents', { _id: id });
    }
    
    static async countDocuments(filter) {
        const store = getMemoryStore();
        return store.countDocuments('contents', filter);
    }
    
    static async create(data) {
        const store = getMemoryStore();
        return store.createDocument('contents', data);
    }
    
    static async findByIdAndUpdate(id, update) {
        const store = getMemoryStore();
        return store.updateDocument('contents', id, update);
    }
    
    static async findByIdAndDelete(id) {
        const store = getMemoryStore();
        return store.deleteDocument('contents', id);
    }
    
    // 静态方法实现
    static async getLatestByType(type) {
        const store = getMemoryStore();
        const contents = await store.findDocuments('contents', { type, status: 'published' });
        return contents.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt))[0] || null;
    }
    
    static async getHistory(type, title) {
        const store = getMemoryStore();
        const contents = await store.findDocuments('contents', { type, title, status: 'published' });
        return contents.sort((a, b) => new Date(b.publishedAt) - new Date(a.publishedAt));
    }
}

// 根据存储类型导出不同的模型
module.exports = isUsingMemoryStore() 
    ? ContentMemoryAdapter 
    : mongoose.model('Content', ContentSchema); 