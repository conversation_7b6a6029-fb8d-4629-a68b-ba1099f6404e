/**
 * 内存数据存储模块
 * 当MongoDB连接失败时使用内存存储作为备选
 */

// 存储各种集合的数据
const collections = {
    users: [],
    roles: [],
    permissions: [],
    contents: [],
    coupons: [],
    apiKeys: [],
    ipBlacklist: [],
    agents: [],
    transactions: [],
    settings: [],
    loginLogs: [],
    operationLogs: [],
    systemLogs: [],
    announcements: [],
    backups: [],
    agentLevels: [],
    agentApplications: [],
    refunds: [],
    reports: [],
    pricingPlans: [],
    operationLogs: [],
    agreements: [],
    pages: [],
    apiKeys: [],
    webhooks: [],
    coupons: [],
    campaigns: [],
    referrals: [],
    pointsTransactions: [],
    riskRules: [],
    approvals: [],
    backups: [],
    securityLogs: [],
    ipBlacklist: [],
    agentLevels: [],
    agentApplications: [],
    commissions: [],
    refunds: [],
    orders: [],
    pricingPlans: [],
    operationLogs: [],
    coupons: [],
    campaigns: [],
    referrals: [],
    pointsTransactions: [],
    riskRules: [],
    approvals: [],
    securityLogs: [],
    backups: [],
    ipBlacklist: [],
    roles: []
};

// 生成唯一ID
const generateId = () => {
    return Math.random().toString(36).substring(2, 15) + 
           Math.random().toString(36).substring(2, 15);
};

// 通用CRUD操作
const createDocument = (collection, document) => {
    if (!collections[collection]) {
        collections[collection] = [];
    }
    
    const newDocument = {
        ...document,
        _id: document._id || generateId(),
        createdAt: document.createdAt || new Date(),
        updatedAt: document.updatedAt || new Date()
    };
    
    collections[collection].push(newDocument);
    return Promise.resolve(newDocument);
};

const findDocuments = (collection, filter = {}) => {
    if (!collections[collection]) {
        return Promise.resolve([]);
    }
    
    let results = [...collections[collection]];
    
    // 简单的过滤实现
    if (Object.keys(filter).length > 0) {
        results = results.filter(doc => {
            return Object.keys(filter).every(key => {
                if (key === '$or') {
                    return filter[key].some(condition => {
                        const conditionKey = Object.keys(condition)[0];
                        if (condition[conditionKey].$regex) {
                            const regex = new RegExp(condition[conditionKey].$regex, condition[conditionKey].$options || '');
                            return regex.test(doc[conditionKey]);
                        }
                        return doc[conditionKey] === condition[conditionKey];
                    });
                }
                return doc[key] === filter[key];
            });
        });
    }
    
    return Promise.resolve(results);
};

const findOneDocument = (collection, filter = {}) => {
    return findDocuments(collection, filter)
        .then(docs => docs.length > 0 ? docs[0] : null);
};

const updateDocument = (collection, id, update) => {
    if (!collections[collection]) {
        return Promise.resolve(null);
    }
    
    const index = collections[collection].findIndex(doc => doc._id === id);
    if (index === -1) {
        return Promise.resolve(null);
    }
    
    collections[collection][index] = {
        ...collections[collection][index],
        ...update,
        updatedAt: new Date()
    };
    
    return Promise.resolve(collections[collection][index]);
};

const deleteDocument = (collection, id) => {
    if (!collections[collection]) {
        return Promise.resolve(null);
    }
    
    const index = collections[collection].findIndex(doc => doc._id === id);
    if (index === -1) {
        return Promise.resolve(null);
    }
    
    const deletedDoc = collections[collection][index];
    collections[collection].splice(index, 1);
    
    return Promise.resolve(deletedDoc);
};

const countDocuments = (collection, filter = {}) => {
    return findDocuments(collection, filter)
        .then(docs => docs.length);
};

// 初始化默认数据
const initDefaultData = () => {
    // 创建默认权限
    if (collections.permissions.length === 0) {
        const permissions = [
            { _id: 'dashboard.view', name: '查看仪表盘', module: 'dashboard', action: 'view' },
            { _id: 'user.view', name: '查看用户', module: 'user', action: 'view' },
            { _id: 'user.create', name: '创建用户', module: 'user', action: 'create' },
            { _id: 'user.edit', name: '编辑用户', module: 'user', action: 'edit' },
            { _id: 'user.delete', name: '删除用户', module: 'user', action: 'delete' },
            { _id: 'agent.view', name: '查看代理', module: 'agent', action: 'view' },
            { _id: 'agent.create', name: '创建代理', module: 'agent', action: 'create' },
            { _id: 'agent.edit', name: '编辑代理', module: 'agent', action: 'edit' },
            { _id: 'agent.delete', name: '删除代理', module: 'agent', action: 'delete' },
            { _id: 'finance.view', name: '查看财务', module: 'finance', action: 'view' },
            { _id: 'finance.export', name: '导出财务数据', module: 'finance', action: 'export' },
            { _id: 'settings.view', name: '查看设置', module: 'settings', action: 'view' },
            { _id: 'settings.edit', name: '编辑设置', module: 'settings', action: 'edit' },
            { _id: 'content.view', name: '查看内容', module: 'content', action: 'view' },
            { _id: 'content.edit', name: '编辑内容', module: 'content', action: 'edit' },
            { _id: 'api.view', name: '查看API配置', module: 'api', action: 'view' },
            { _id: 'api.edit', name: '编辑API配置', module: 'api', action: 'edit' },
            { _id: 'marketing.view', name: '查看营销工具', module: 'marketing', action: 'view' },
            { _id: 'marketing.edit', name: '编辑营销工具', module: 'marketing', action: 'edit' },
            { _id: 'security.view', name: '查看安全中心', module: 'security', action: 'view' },
            { _id: 'security.edit', name: '编辑安全设置', module: 'security', action: 'edit' },
            { _id: 'monitor.view', name: '查看系统监控', module: 'monitor', action: 'view' },
            { _id: 'role.view', name: '查看角色', module: 'role', action: 'view' },
            { _id: 'role.edit', name: '编辑角色', module: 'role', action: 'edit' }
        ];

        permissions.forEach(permission => {
            createDocument('permissions', permission);
        });

        console.log('已创建默认权限');
    }

    // 创建默认角色
    if (collections.roles.length === 0) {
        const roles = [
            {
                _id: 'super-admin',
                name: '超级管理员',
                description: '拥有所有权限的超级管理员',
                permissions: collections.permissions.map(p => p._id),
                status: 'active'
            },
            {
                _id: 'finance-admin',
                name: '财务管理员',
                description: '负责财务相关功能的管理员',
                permissions: ['dashboard.view', 'finance.view', 'finance.export', 'user.view'],
                status: 'active'
            },
            {
                _id: 'content-admin',
                name: '内容管理员',
                description: '负责内容管理的管理员',
                permissions: ['dashboard.view', 'content.view', 'content.edit', 'marketing.view', 'marketing.edit'],
                status: 'active'
            },
            {
                _id: 'customer-service',
                name: '客服',
                description: '客服人员角色',
                permissions: ['dashboard.view', 'user.view', 'content.view'],
                status: 'active'
            }
        ];

        roles.forEach(role => {
            createDocument('roles', role);
        });

        console.log('已创建默认角色');
    }

    // 创建默认管理员用户
    if (collections.users.length === 0) {
        createDocument('users', {
            _id: 'admin-user-id',
            username: 'admin',
            password: 'admin123',
            name: '系统管理员',
            email: '<EMAIL>',
            roleId: 'super-admin',
            status: 'active',
            twoFactorEnabled: true,
            twoFactorSecret: '101010',
            lastLoginAt: null,
            loginCount: 0,
            createdAt: new Date(),
            updatedAt: new Date()
        });

        console.log('已创建默认管理员用户');
    }

    // 创建默认代理级别
    if (collections.agentLevels.length === 0) {
        const agentLevels = [
            {
                _id: 'level-bronze',
                name: '铜牌代理',
                code: 'BRONZE',
                minPerformance: 0,
                commissionRate: 5,
                benefits: '基础代理权益，享受5%佣金',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                _id: 'level-silver',
                name: '银牌代理',
                code: 'SILVER',
                minPerformance: 10000,
                commissionRate: 8,
                benefits: '银牌代理权益，享受8%佣金，优先客服支持',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                _id: 'level-gold',
                name: '金牌代理',
                code: 'GOLD',
                minPerformance: 50000,
                commissionRate: 12,
                benefits: '金牌代理权益，享受12%佣金，专属客服，营销支持',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        agentLevels.forEach(level => {
            createDocument('agentLevels', level);
        });

        console.log('已创建默认代理级别');
    }

    // 创建默认代理商
    if (collections.agents.length === 0) {
        const agents = [
            {
                _id: 'agent-001',
                name: '北京科技有限公司',
                contactPerson: '张经理',
                phone: '***********',
                email: '<EMAIL>',
                address: '北京市朝阳区科技园区',
                level: 'level-gold',
                status: 'active',
                commissionRate: 12,
                parentId: null,
                totalRevenue: 125000,
                totalCommission: 15000,
                customerCount: 45,
                subAgentCount: 3,
                createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
                updatedAt: new Date()
            },
            {
                _id: 'agent-002',
                name: '上海商贸公司',
                contactPerson: '李总',
                phone: '13800138002',
                email: '<EMAIL>',
                address: '上海市浦东新区商务区',
                level: 'level-silver',
                status: 'active',
                commissionRate: 8,
                parentId: null,
                totalRevenue: 68000,
                totalCommission: 5440,
                customerCount: 28,
                subAgentCount: 1,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
                updatedAt: new Date()
            },
            {
                _id: 'agent-003',
                name: '深圳创新企业',
                contactPerson: '王先生',
                phone: '13800138003',
                email: '<EMAIL>',
                address: '深圳市南山区高新园',
                level: 'level-bronze',
                status: 'pending',
                commissionRate: 5,
                parentId: 'agent-001',
                totalRevenue: 15000,
                totalCommission: 750,
                customerCount: 8,
                subAgentCount: 0,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date()
            }
        ];

        agents.forEach(agent => {
            createDocument('agents', agent);
        });

        console.log('已创建默认代理商');
    }

    // 创建默认交易记录
    if (collections.transactions.length === 0) {
        const transactions = [
            {
                _id: 'txn-001',
                orderId: 'ORD-2024-001',
                userId: 'user-001',
                amount: 299,
                type: 'subscription',
                status: 'completed',
                paymentMethod: 'alipay',
                description: '专业版月度订阅',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'txn-002',
                orderId: 'ORD-2024-002',
                userId: 'user-002',
                amount: 99,
                type: 'one-time',
                status: 'completed',
                paymentMethod: 'wechat',
                description: '基础版月度订阅',
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'txn-003',
                orderId: 'ORD-2024-003',
                userId: 'user-003',
                amount: 599,
                type: 'upgrade',
                status: 'pending',
                paymentMethod: 'alipay',
                description: '企业版年度订阅',
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        transactions.forEach(transaction => {
            createDocument('transactions', transaction);
        });

        console.log('已创建默认交易记录');
    }

    // 创建默认退款记录
    if (collections.refunds.length === 0) {
        const refunds = [
            {
                _id: 'refund-001',
                refundId: 'REF-2024-001',
                orderId: 'ORD-2024-001',
                userId: 'user-001',
                amount: 299,
                reason: '服务不满意',
                status: 'pending',
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'refund-002',
                refundId: 'REF-2024-002',
                orderId: 'ORD-2024-002',
                userId: 'user-002',
                amount: 99,
                reason: '误操作',
                status: 'completed',
                processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        refunds.forEach(refund => {
            createDocument('refunds', refund);
        });

        console.log('已创建默认退款记录');
    }

    // 创建默认价格方案
    if (collections.pricingPlans.length === 0) {
        const pricingPlans = [
            {
                _id: 'plan-basic-monthly',
                productName: '基础版月度订阅',
                productType: 'monthly',
                category: 'basic',
                originalPrice: 99,
                discountPrice: 79,
                productDescription: '适合个人用户的基础写作功能',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                _id: 'plan-pro-monthly',
                productName: '专业版月度订阅',
                productType: 'monthly',
                category: 'pro',
                originalPrice: 299,
                discountPrice: 249,
                productDescription: '适合专业用户的高级写作功能',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            },
            {
                _id: 'plan-enterprise-yearly',
                productName: '企业版年度订阅',
                productType: 'yearly',
                category: 'enterprise',
                originalPrice: 5999,
                discountPrice: 4999,
                productDescription: '适合企业用户的全功能版本',
                status: 'active',
                createdAt: new Date(),
                updatedAt: new Date()
            }
        ];

        pricingPlans.forEach(plan => {
            createDocument('pricingPlans', plan);
        });

        console.log('已创建默认价格方案');
    }

    // 创建默认操作日志
    if (collections.operationLogs.length === 0) {
        const operationLogs = [
            {
                _id: 'log-001',
                userId: 'admin',
                username: 'admin',
                module: 'system',
                operation: 'login',
                details: '管理员登录系统',
                level: 'info',
                ip: '127.0.0.1',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                _id: 'log-002',
                userId: 'admin',
                username: 'admin',
                module: 'user',
                operation: 'create_user',
                details: '创建新用户: testuser',
                level: 'info',
                ip: '127.0.0.1',
                timestamp: new Date(Date.now() - 1 * 60 * 60 * 1000)
            },
            {
                _id: 'log-003',
                userId: 'admin',
                username: 'admin',
                module: 'settings',
                operation: 'update_pricing',
                details: '更新价格方案: 专业版月度订阅',
                level: 'info',
                ip: '127.0.0.1',
                timestamp: new Date(Date.now() - 30 * 60 * 1000)
            }
        ];

        operationLogs.forEach(log => {
            createDocument('operationLogs', log);
        });

        console.log('已创建默认操作日志');
    }

    // 创建默认协议
    if (collections.agreements.length === 0) {
        const agreements = [
            {
                _id: 'agreement-terms',
                name: '服务条款',
                type: 'terms',
                version: 'v2.1',
                status: 'active',
                content: '<h1>服务条款</h1><p>欢迎使用WriterPro智能写作助手...</p>',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'agreement-privacy',
                name: '隐私政策',
                type: 'privacy',
                version: 'v2.3',
                status: 'active',
                content: '<h1>隐私政策</h1><p>我们重视您的隐私保护...</p>',
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'agreement-user',
                name: '用户注册协议',
                type: 'user',
                version: 'v2.1',
                status: 'active',
                content: '<h1>用户注册协议</h1><p>在注册成为用户前，请仔细阅读...</p>',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            }
        ];

        agreements.forEach(agreement => {
            createDocument('agreements', agreement);
        });

        console.log('已创建默认协议');
    }

    // 创建默认公告
    if (collections.announcements.length === 0) {
        const announcements = [
            {
                _id: 'announcement-001',
                title: '系统升级通知',
                type: 'system',
                priority: 'high',
                content: '<p>为了提供更好的服务，系统将于本周末进行升级维护...</p>',
                status: 'published',
                startTime: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                endTime: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'announcement-002',
                title: '新功能上线公告',
                type: 'feature',
                priority: 'normal',
                content: '<p>我们很高兴地宣布，WriterPro新增了AI智能纠错功能...</p>',
                status: 'published',
                startTime: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                endTime: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            }
        ];

        announcements.forEach(announcement => {
            createDocument('announcements', announcement);
        });

        console.log('已创建默认公告');
    }

    // 创建默认页面内容
    if (collections.pages.length === 0) {
        const pages = [
            {
                _id: 'page-home',
                name: '首页',
                slug: 'home',
                title: 'WriterPro - 智能写作助手',
                content: '<h1>欢迎使用WriterPro</h1><p>专业的AI写作助手，让创作更简单...</p>',
                status: 'published',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'page-about',
                name: '关于我们',
                slug: 'about',
                title: '关于WriterPro',
                content: '<h1>关于我们</h1><p>WriterPro是一款基于人工智能的写作助手...</p>',
                status: 'published',
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'page-help',
                name: '帮助中心',
                slug: 'help',
                title: '帮助中心',
                content: '<h1>帮助中心</h1><h2>常见问题</h2><p>Q: 如何开始使用WriterPro？</p>',
                status: 'published',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            }
        ];

        pages.forEach(page => {
            createDocument('pages', page);
        });

        console.log('已创建默认页面内容');
    }

    // 创建默认API密钥
    if (collections.apiKeys.length === 0) {
        const apiKeys = [
            {
                _id: 'apikey-001',
                name: '主要API密钥',
                key: 'wp_abc123def456ghi789jkl012mno345pqr',
                projectId: 'writerpro-main',
                services: ['translation', 'document', 'vision'],
                dailyLimit: 10000,
                status: 'active',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                lastUsed: new Date(Date.now() - 2 * 60 * 60 * 1000),
                usageCount: 8547
            },
            {
                _id: 'apikey-002',
                name: '测试环境密钥',
                key: 'wp_test789xyz456abc123def890ghi567jkl',
                projectId: 'writerpro-test',
                services: ['translation', 'speech'],
                dailyLimit: 1000,
                status: 'active',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                lastUsed: new Date(Date.now() - 30 * 60 * 1000),
                usageCount: 1247
            }
        ];

        apiKeys.forEach(apiKey => {
            createDocument('apiKeys', apiKey);
        });

        console.log('已创建默认API密钥');
    }

    // 创建默认Webhook配置
    if (collections.webhooks.length === 0) {
        const webhooks = [
            {
                _id: 'webhook-001',
                name: '支付成功通知',
                url: 'https://api.example.com/webhooks/payment',
                events: ['payment.success', 'payment.failed', 'payment.refund'],
                enabled: true,
                secret: 'whsec_abc123def456',
                successRate: 98.5,
                lastCall: new Date(Date.now() - 2 * 60 * 1000),
                avgResponseTime: 245,
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'webhook-002',
                name: '用户注册通知',
                url: 'https://api.example.com/webhooks/user',
                events: ['user.created', 'user.updated', 'user.deleted'],
                enabled: true,
                secret: 'whsec_xyz789abc456',
                successRate: 99.2,
                lastCall: new Date(Date.now() - 5 * 60 * 1000),
                avgResponseTime: 180,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            }
        ];

        webhooks.forEach(webhook => {
            createDocument('webhooks', webhook);
        });

        console.log('已创建默认Webhook配置');
    }

    // 创建默认优惠券
    if (collections.coupons.length === 0) {
        const coupons = [
            {
                _id: 'coupon-001',
                name: '夏季促销',
                code: 'SUMMER25',
                type: 'percentage',
                value: 25,
                minAmount: 100,
                totalCount: 500,
                usedCount: 158,
                savings: 12450,
                userLimit: 'none',
                startDate: new Date('2025-07-01'),
                endDate: new Date('2025-08-31'),
                status: 'active',
                description: '夏季限时优惠，全场商品享受25%折扣',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'coupon-002',
                name: '新用户专享',
                code: 'NEWUSER50',
                type: 'percentage',
                value: 50,
                minAmount: 50,
                totalCount: 1000,
                usedCount: 247,
                savings: 8650,
                userLimit: 'new',
                startDate: new Date('2025-06-01'),
                endDate: new Date('2025-12-31'),
                status: 'active',
                description: '新用户首次购买享受50%折扣',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'coupon-003',
                name: '会员专享',
                code: 'VIP20',
                type: 'fixed',
                value: 20,
                minAmount: 200,
                totalCount: 800,
                usedCount: 0,
                savings: 0,
                userLimit: 'vip',
                startDate: new Date('2025-07-15'),
                endDate: new Date('2025-09-15'),
                status: 'pending',
                description: 'VIP会员专享20元优惠券',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            }
        ];

        coupons.forEach(coupon => {
            createDocument('coupons', coupon);
        });

        console.log('已创建默认优惠券');
    }

    // 创建默认推广活动
    if (collections.campaigns.length === 0) {
        const campaigns = [
            {
                _id: 'campaign-001',
                name: '夏日狂欢节',
                type: 'seasonal',
                description: '夏季主题营销活动，多重优惠叠加',
                startDate: new Date('2025-07-01'),
                endDate: new Date('2025-07-31'),
                budget: 50000,
                spent: 32500,
                participants: 1247,
                conversions: 389,
                status: 'active',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'campaign-002',
                name: '新用户增长计划',
                type: 'acquisition',
                description: '针对新用户的获客活动',
                startDate: new Date('2025-06-01'),
                endDate: new Date('2025-08-31'),
                budget: 30000,
                spent: 18750,
                participants: 856,
                conversions: 234,
                status: 'active',
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            }
        ];

        campaigns.forEach(campaign => {
            createDocument('campaigns', campaign);
        });

        console.log('已创建默认推广活动');
    }

    // 创建默认邀请记录
    if (collections.referrals.length === 0) {
        const referrals = [
            {
                _id: 'referral-001',
                inviterId: 'user-001',
                inviteeId: 'user-004',
                inviteCode: 'INV123ABC',
                status: 'completed',
                inviterReward: 50,
                inviteeReward: 30,
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'referral-002',
                inviterId: 'user-002',
                inviteeId: 'user-005',
                inviteCode: 'INV456DEF',
                status: 'completed',
                inviterReward: 50,
                inviteeReward: 30,
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'referral-003',
                inviterId: 'user-001',
                inviteeId: null,
                inviteCode: 'INV789GHI',
                status: 'pending',
                inviterReward: 0,
                inviteeReward: 0,
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                completedAt: null
            }
        ];

        referrals.forEach(referral => {
            createDocument('referrals', referral);
        });

        console.log('已创建默认邀请记录');
    }

    // 创建默认积分交易记录
    if (collections.pointsTransactions.length === 0) {
        const pointsTransactions = [
            {
                _id: 'points-001',
                userId: 'user-001',
                type: 'earn',
                amount: 100,
                reason: '注册奖励',
                orderId: null,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-002',
                userId: 'user-001',
                type: 'earn',
                amount: 50,
                reason: '邀请好友',
                orderId: null,
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-003',
                userId: 'user-001',
                type: 'spend',
                amount: -30,
                reason: '兑换优惠券',
                orderId: 'order-001',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-004',
                userId: 'user-002',
                type: 'earn',
                amount: 100,
                reason: '注册奖励',
                orderId: null,
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000)
            }
        ];

        pointsTransactions.forEach(transaction => {
            createDocument('pointsTransactions', transaction);
        });

        console.log('已创建默认积分交易记录');
    }

    // 创建默认风控规则
    if (collections.riskRules.length === 0) {
        const riskRules = [
            {
                _id: 'risk-rule-001',
                name: '异常登录检测',
                type: 'login',
                riskLevel: 'high',
                condition: '短时间内多次登录失败',
                threshold: 5,
                timeWindow: 300, // 5分钟
                action: 'block',
                status: 'active',
                triggerCount: 23,
                description: '检测短时间内多次登录失败的异常行为',
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'risk-rule-002',
                name: '大额交易监控',
                type: 'payment',
                riskLevel: 'medium',
                condition: '单笔交易金额超过阈值',
                threshold: 10000,
                timeWindow: 0,
                action: 'alert',
                status: 'active',
                triggerCount: 8,
                description: '监控大额交易，及时发现异常支付行为',
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'risk-rule-003',
                name: 'API调用频率限制',
                type: 'api',
                riskLevel: 'low',
                condition: 'API调用频率超过限制',
                threshold: 1000,
                timeWindow: 3600, // 1小时
                action: 'throttle',
                status: 'active',
                triggerCount: 156,
                description: '限制API调用频率，防止恶意攻击',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        riskRules.forEach(rule => {
            createDocument('riskRules', rule);
        });

        console.log('已创建默认风控规则');
    }

    // 创建默认审批申请
    if (collections.approvals.length === 0) {
        const approvals = [
            {
                _id: 'approval-001',
                type: 'user_delete',
                title: '删除用户账户',
                description: '申请删除用户 <EMAIL> 的账户及相关数据',
                applicant: 'admin',
                applicantName: '系统管理员',
                riskLevel: 'high',
                status: 'pending',
                priority: 'normal',
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                _id: 'approval-002',
                type: 'data_export',
                title: '导出用户数据',
                description: '申请导出所有用户数据用于数据分析',
                applicant: 'data_analyst',
                applicantName: '数据分析师',
                riskLevel: 'medium',
                status: 'pending',
                priority: 'urgent',
                createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                _id: 'approval-003',
                type: 'system_config',
                title: '修改系统配置',
                description: '申请修改支付接口配置参数',
                applicant: 'tech_lead',
                applicantName: '技术负责人',
                riskLevel: 'high',
                status: 'approved',
                priority: 'normal',
                processedBy: 'admin',
                processedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        approvals.forEach(approval => {
            createDocument('approvals', approval);
        });

        console.log('已创建默认审批申请');
    }

    // 创建默认备份记录
    if (collections.backups.length === 0) {
        const backups = [
            {
                _id: 'backup-001',
                name: 'backup_2025-07-09_1720512000000',
                type: 'full',
                status: 'completed',
                size: 856743210, // ~857MB
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
            },
            {
                _id: 'backup-002',
                name: 'backup_2025-07-08_1720425600000',
                type: 'incremental',
                status: 'completed',
                size: 234567890, // ~235MB
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000)
            },
            {
                _id: 'backup-003',
                name: 'backup_2025-07-07_1720339200000',
                type: 'full',
                status: 'completed',
                size: 923456789, // ~923MB
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 6 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 6 * 60 * 1000)
            }
        ];

        backups.forEach(backup => {
            createDocument('backups', backup);
        });

        console.log('已创建默认备份记录');
    }

    // 创建默认安全日志
    if (collections.securityLogs.length === 0) {
        const securityLogs = [
            {
                _id: 'security-log-001',
                level: 'warning',
                event: 'login_failed',
                message: '用户登录失败次数过多',
                ip: '*************',
                userId: 'user-001',
                details: { attempts: 5, timeWindow: '5分钟' },
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                _id: 'security-log-002',
                level: 'info',
                event: 'password_changed',
                message: '用户修改密码',
                ip: '*************',
                userId: 'user-002',
                details: { success: true },
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                _id: 'security-log-003',
                level: 'error',
                event: 'unauthorized_access',
                message: '未授权访问敏感接口',
                ip: '*********',
                userId: null,
                details: { endpoint: '/api/admin/users', method: 'DELETE' },
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)
            }
        ];

        securityLogs.forEach(log => {
            createDocument('securityLogs', log);
        });

        console.log('已创建默认安全日志');
    }

    // 创建默认IP黑名单
    if (collections.ipBlacklist.length === 0) {
        const ipBlacklist = [
            {
                _id: 'ip-blacklist-001',
                ip: '*************',
                reason: '多次恶意登录尝试',
                status: 'active',
                createdBy: 'admin',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'ip-blacklist-002',
                ip: '*********',
                reason: '未授权API访问',
                status: 'active',
                createdBy: 'security_admin',
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            }
        ];

        ipBlacklist.forEach(ip => {
            createDocument('ipBlacklist', ip);
        });

        console.log('已创建默认IP黑名单');
    }

    // 创建默认代理级别
    if (collections.agentLevels.length === 0) {
        const agentLevels = [
            {
                _id: 'level-001',
                name: '青铜代理',
                code: 'bronze',
                order: 1,
                minPerformance: 0,
                commissionRate: 5,
                benefits: '基础代理权益，享受5%佣金比例',
                status: 'active',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'level-002',
                name: '白银代理',
                code: 'silver',
                order: 2,
                minPerformance: 10000,
                commissionRate: 8,
                benefits: '中级代理权益，享受8%佣金比例，专属客服支持',
                status: 'active',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'level-003',
                name: '黄金代理',
                code: 'gold',
                order: 3,
                minPerformance: 50000,
                commissionRate: 12,
                benefits: '高级代理权益，享受12%佣金比例，优先技术支持，专属培训',
                status: 'active',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'level-004',
                name: '钻石代理',
                code: 'diamond',
                order: 4,
                minPerformance: 100000,
                commissionRate: 15,
                benefits: '顶级代理权益，享受15%佣金比例，专属客户经理，定制化服务',
                status: 'active',
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
            }
        ];

        agentLevels.forEach(level => {
            createDocument('agentLevels', level);
        });

        console.log('已创建默认代理级别');
    }

    // 创建默认代理申请
    if (collections.agentApplications.length === 0) {
        const agentApplications = [
            {
                _id: 'app-001',
                companyName: '上海科技有限公司',
                contactName: '张经理',
                contactPhone: '***********',
                contactEmail: '<EMAIL>',
                businessLicense: 'BL202507001',
                address: '上海市浦东新区张江高科技园区',
                businessScope: '软件开发、技术服务',
                expectedLevel: 'silver',
                applicationReason: '我们公司专注于企业级软件服务，有丰富的客户资源和推广经验',
                status: 'pending',
                submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'app-002',
                companyName: '北京营销策划公司',
                contactName: '李总',
                contactPhone: '***********',
                contactEmail: '<EMAIL>',
                businessLicense: 'BL202507002',
                address: '北京市朝阳区CBD商务区',
                businessScope: '营销策划、广告推广',
                expectedLevel: 'gold',
                applicationReason: '拥有10年营销推广经验，服务过多家知名企业',
                status: 'approved',
                submittedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                processedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                processedBy: 'admin',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'app-003',
                companyName: '深圳创新科技',
                contactName: '王总监',
                contactPhone: '***********',
                contactEmail: '<EMAIL>',
                businessLicense: 'BL202507003',
                address: '深圳市南山区科技园',
                businessScope: '互联网技术、产品推广',
                expectedLevel: 'bronze',
                applicationReason: '初创公司，希望通过代理合作拓展业务',
                status: 'rejected',
                submittedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                processedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                processedBy: 'admin',
                rejectReason: '公司成立时间较短，建议积累更多经验后再申请',
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            }
        ];

        agentApplications.forEach(app => {
            createDocument('agentApplications', app);
        });

        console.log('已创建默认代理申请');
    }

    // 创建默认佣金记录
    if (collections.commissions.length === 0) {
        const commissions = [
            {
                _id: 'commission-001',
                agentId: 'agent-001',
                agentName: '上海科技代理',
                orderId: 'order-001',
                orderAmount: 1000,
                commissionRate: 8,
                amount: 80,
                type: 'direct',
                status: 'paid',
                settlementDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'commission-002',
                agentId: 'agent-001',
                agentName: '上海科技代理',
                orderId: 'order-002',
                orderAmount: 2500,
                commissionRate: 8,
                amount: 200,
                type: 'direct',
                status: 'pending',
                settlementDate: null,
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'commission-003',
                agentId: 'agent-002',
                agentName: '北京营销代理',
                orderId: 'order-003',
                orderAmount: 5000,
                commissionRate: 12,
                amount: 600,
                type: 'direct',
                status: 'paid',
                settlementDate: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            }
        ];

        commissions.forEach(commission => {
            createDocument('commissions', commission);
        });

        console.log('已创建默认佣金记录');
    }

    // 创建默认退款记录
    if (collections.refunds.length === 0) {
        const refunds = [
            {
                _id: 'refund-001',
                orderId: 'order-001',
                userId: 'user-001',
                username: '张三',
                amount: 299,
                reason: '产品不符合预期',
                status: 'pending',
                type: 'full',
                paymentMethod: 'alipay',
                submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'refund-002',
                orderId: 'order-002',
                userId: 'user-002',
                username: '李四',
                amount: 599,
                reason: '误操作购买',
                status: 'completed',
                type: 'full',
                paymentMethod: 'wechat',
                submittedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                processedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                processedBy: 'admin',
                processReason: '符合退款条件，已处理',
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'refund-003',
                orderId: 'order-003',
                userId: 'user-003',
                username: '王五',
                amount: 199,
                reason: '服务质量问题',
                status: 'rejected',
                type: 'partial',
                paymentMethod: 'alipay',
                submittedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                processedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                processedBy: 'admin',
                processReason: '不符合退款政策',
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            }
        ];

        refunds.forEach(refund => {
            createDocument('refunds', refund);
        });

        console.log('已创建默认退款记录');
    }

    // 创建默认订单记录
    if (collections.orders.length === 0) {
        const orders = [
            {
                _id: 'order-001',
                orderNo: 'WP202507100001',
                userId: 'user-001',
                username: '张三',
                productName: 'WriterPro专业版',
                amount: 299,
                status: 'completed',
                paymentMethod: 'alipay',
                paymentStatus: 'paid',
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                paidAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000),
                updatedAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000 + 5 * 60 * 1000)
            },
            {
                _id: 'order-002',
                orderNo: 'WP202507100002',
                userId: 'user-002',
                username: '李四',
                productName: 'WriterPro企业版',
                amount: 599,
                status: 'completed',
                paymentMethod: 'wechat',
                paymentStatus: 'paid',
                createdAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000),
                paidAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 3 * 60 * 1000),
                updatedAt: new Date(Date.now() - 8 * 24 * 60 * 60 * 1000 + 3 * 60 * 1000)
            },
            {
                _id: 'order-003',
                orderNo: 'WP202507100003',
                userId: 'user-003',
                username: '王五',
                productName: 'WriterPro基础版',
                amount: 199,
                status: 'pending',
                paymentMethod: 'alipay',
                paymentStatus: 'pending',
                createdAt: new Date(Date.now() - 1 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
            },
            {
                _id: 'order-004',
                orderNo: 'WP202507100004',
                userId: 'user-001',
                username: '张三',
                productName: 'WriterPro专业版续费',
                amount: 299,
                status: 'completed',
                paymentMethod: 'alipay',
                paymentStatus: 'paid',
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                paidAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 2 * 60 * 1000)
            },
            {
                _id: 'order-005',
                orderNo: 'WP202507100005',
                userId: 'user-004',
                username: '赵六',
                productName: 'WriterPro专业版',
                amount: 299,
                status: 'completed',
                paymentMethod: 'wechat',
                paymentStatus: 'paid',
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                paidAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 1 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000 + 1 * 60 * 1000)
            }
        ];

        orders.forEach(order => {
            createDocument('orders', order);
        });

        console.log('已创建默认订单记录');
    }

    // 创建默认价格方案
    if (collections.pricingPlans.length === 0) {
        const pricingPlans = [
            {
                _id: 'plan-001',
                productName: 'WriterPro基础版',
                productCode: 'basic',
                originalPrice: 299,
                currentPrice: 199,
                discountPercentage: 33,
                features: [
                    '基础AI写作功能',
                    '每月10万字额度',
                    '基础模板库',
                    '邮件支持'
                ],
                duration: 'monthly',
                status: 'active',
                sortOrder: 1,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'plan-002',
                productName: 'WriterPro专业版',
                productCode: 'professional',
                originalPrice: 599,
                currentPrice: 399,
                discountPercentage: 33,
                features: [
                    '高级AI写作功能',
                    '每月50万字额度',
                    '专业模板库',
                    '多语言支持',
                    '优先客服支持'
                ],
                duration: 'monthly',
                status: 'active',
                sortOrder: 2,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'plan-003',
                productName: 'WriterPro企业版',
                productCode: 'enterprise',
                originalPrice: 1299,
                currentPrice: 999,
                discountPercentage: 23,
                features: [
                    '企业级AI写作功能',
                    '无限字数额度',
                    '定制模板库',
                    '多语言支持',
                    'API接口',
                    '专属客户经理',
                    '私有化部署'
                ],
                duration: 'monthly',
                status: 'active',
                sortOrder: 3,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        pricingPlans.forEach(plan => {
            createDocument('pricingPlans', plan);
        });

        console.log('已创建默认价格方案');
    }

    // 创建默认操作日志
    if (collections.operationLogs.length === 0) {
        const operationLogs = [
            {
                _id: 'log-001',
                userId: 'admin',
                username: '系统管理员',
                module: 'users',
                operation: 'create',
                details: '创建用户: 张三',
                level: 'info',
                ip: '*************',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                _id: 'log-002',
                userId: 'admin',
                username: '系统管理员',
                module: 'settings',
                operation: 'update_pricing',
                details: '更新价格方案: WriterPro专业版',
                level: 'warning',
                ip: '*************',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                _id: 'log-003',
                userId: 'admin',
                username: '系统管理员',
                module: 'finance',
                operation: 'process_refund',
                details: '处理退款申请: 订单WP202507100001',
                level: 'info',
                ip: '*************',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000)
            },
            {
                _id: 'log-004',
                userId: 'admin',
                username: '系统管理员',
                module: 'security',
                operation: 'login_failed',
                details: '登录失败: 密码错误',
                level: 'error',
                ip: '*********',
                timestamp: new Date(Date.now() - 8 * 60 * 60 * 1000)
            },
            {
                _id: 'log-005',
                userId: 'admin',
                username: '系统管理员',
                module: 'agents',
                operation: 'approve_application',
                details: '批准代理申请: 北京营销策划公司',
                level: 'info',
                ip: '*************',
                timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000)
            }
        ];

        operationLogs.forEach(log => {
            createDocument('operationLogs', log);
        });

        console.log('已创建默认操作日志');
    }

    // 创建默认优惠券
    if (collections.coupons.length === 0) {
        const coupons = [
            {
                _id: 'coupon-001',
                code: 'SUMMER25',
                name: '夏季促销',
                description: '夏季限时优惠，全场商品享受25%折扣',
                discountType: 'percentage',
                discountValue: 25,
                minOrderAmount: 100,
                usageLimit: 500,
                userLimit: 1,
                usedCount: 158,
                startDate: new Date('2025-07-01'),
                endDate: new Date('2025-08-31'),
                status: 'active',
                applicableProducts: [],
                excludeProducts: [],
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'coupon-002',
                code: 'NEWUSER50',
                name: '新用户专享',
                description: '新用户注册专享50%折扣优惠',
                discountType: 'percentage',
                discountValue: 50,
                minOrderAmount: 50,
                usageLimit: 1000,
                userLimit: 1,
                usedCount: 247,
                startDate: new Date('2025-06-01'),
                endDate: new Date('2025-12-31'),
                status: 'active',
                userRestriction: 'new_users_only',
                applicableProducts: [],
                excludeProducts: [],
                createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'coupon-003',
                code: 'VIP20',
                name: '会员专享',
                description: 'VIP会员专享20元现金抵扣',
                discountType: 'fixed',
                discountValue: 20,
                minOrderAmount: 200,
                usageLimit: 800,
                userLimit: 2,
                usedCount: 0,
                startDate: new Date('2025-07-15'),
                endDate: new Date('2025-09-15'),
                status: 'pending',
                userRestriction: 'vip_only',
                applicableProducts: [],
                excludeProducts: [],
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        coupons.forEach(coupon => {
            createDocument('coupons', coupon);
        });

        console.log('已创建默认优惠券');
    }

    // 创建默认推广活动
    if (collections.campaigns.length === 0) {
        const campaigns = [
            {
                _id: 'campaign-001',
                name: '夏季推广活动',
                description: '夏季大促销，多重优惠等你来',
                type: 'promotion',
                budget: 50000,
                spent: 23500,
                startDate: new Date('2025-07-01'),
                endDate: new Date('2025-08-31'),
                status: 'active',
                participants: 1250,
                conversions: 320,
                targetAudience: 'all_users',
                channels: ['email', 'sms', 'push'],
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'campaign-002',
                name: '新用户获客活动',
                description: '针对新用户的专项获客活动',
                type: 'referral',
                budget: 30000,
                spent: 18200,
                startDate: new Date('2025-06-15'),
                endDate: new Date('2025-09-15'),
                status: 'active',
                participants: 850,
                conversions: 280,
                targetAudience: 'new_users',
                channels: ['social_media', 'referral'],
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'campaign-003',
                name: '积分兑换活动',
                description: '积分兑换好礼，多种奖品等你来换',
                type: 'points',
                budget: 20000,
                spent: 8500,
                startDate: new Date('2025-07-10'),
                endDate: new Date('2025-08-10'),
                status: 'active',
                participants: 450,
                conversions: 150,
                targetAudience: 'vip_users',
                channels: ['app', 'email'],
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        campaigns.forEach(campaign => {
            createDocument('campaigns', campaign);
        });

        console.log('已创建默认推广活动');
    }

    // 创建默认邀请记录
    if (collections.referrals.length === 0) {
        const referrals = [
            {
                _id: 'referral-001',
                inviterId: 'user-001',
                inviterName: '张三',
                inviteeId: 'user-015',
                inviteeName: '李四',
                inviteCode: 'REF001',
                status: 'completed',
                inviterReward: 50,
                inviteeReward: 30,
                rewardStatus: 'paid',
                inviteDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                completedDate: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000),
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 18 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'referral-002',
                inviterId: 'user-002',
                inviterName: '王五',
                inviteeId: 'user-016',
                inviteeName: '赵六',
                inviteCode: 'REF002',
                status: 'pending',
                inviterReward: 50,
                inviteeReward: 30,
                rewardStatus: 'pending',
                inviteDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                completedDate: null,
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            }
        ];

        referrals.forEach(referral => {
            createDocument('referrals', referral);
        });

        console.log('已创建默认邀请记录');
    }

    // 创建默认积分交易记录
    if (collections.pointsTransactions.length === 0) {
        const pointsTransactions = [
            {
                _id: 'points-001',
                userId: 'user-001',
                userName: '张三',
                type: 'earn',
                amount: 100,
                source: 'sign_in',
                description: '每日签到奖励',
                orderId: null,
                balance: 1250,
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-002',
                userId: 'user-001',
                userName: '张三',
                type: 'earn',
                amount: 200,
                source: 'purchase',
                description: '购买商品获得积分',
                orderId: 'order-001',
                balance: 1150,
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-003',
                userId: 'user-002',
                userName: '李四',
                type: 'spend',
                amount: 500,
                source: 'redemption',
                description: '积分兑换优惠券',
                orderId: null,
                balance: 800,
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-004',
                userId: 'user-003',
                userName: '王五',
                type: 'earn',
                amount: 50,
                source: 'referral',
                description: '邀请好友奖励',
                orderId: null,
                balance: 650,
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-005',
                userId: 'user-004',
                userName: '赵六',
                type: 'earn',
                amount: 300,
                source: 'activity',
                description: '参与活动奖励',
                orderId: null,
                balance: 950,
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'points-006',
                userId: 'user-005',
                userName: '孙七',
                type: 'spend',
                amount: 200,
                source: 'redemption',
                description: '积分兑换礼品',
                orderId: null,
                balance: 400,
                createdAt: new Date(Date.now() - 12 * 24 * 60 * 60 * 1000)
            }
        ];

        pointsTransactions.forEach(transaction => {
            createDocument('pointsTransactions', transaction);
        });

        console.log('已创建默认积分交易记录');
    }

    // 创建默认风控规则
    if (collections.riskRules.length === 0) {
        const riskRules = [
            {
                _id: 'risk-rule-001',
                name: '异常登录检测',
                type: 'login',
                riskLevel: 'high',
                condition: '短时间内多次登录失败',
                action: '锁定账户30分钟',
                threshold: 5,
                timeWindow: 300, // 5分钟
                status: 'active',
                triggerCount: 23,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'risk-rule-002',
                name: '大额支付风控',
                type: 'payment',
                riskLevel: 'high',
                condition: '单笔支付金额超过10000元',
                action: '需要人工审核',
                threshold: 10000,
                timeWindow: 0,
                status: 'active',
                triggerCount: 8,
                createdAt: new Date(Date.now() - 25 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'risk-rule-003',
                name: 'API频率限制',
                type: 'api',
                riskLevel: 'medium',
                condition: '每分钟API调用超过1000次',
                action: '限制API访问',
                threshold: 1000,
                timeWindow: 60,
                status: 'active',
                triggerCount: 156,
                createdAt: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'risk-rule-004',
                name: '敏感数据访问',
                type: 'data',
                riskLevel: 'high',
                condition: '非工作时间访问敏感数据',
                action: '记录日志并通知管理员',
                threshold: 1,
                timeWindow: 0,
                status: 'active',
                triggerCount: 12,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        riskRules.forEach(rule => {
            createDocument('riskRules', rule);
        });

        console.log('已创建默认风控规则');
    }

    // 创建默认审批记录
    if (collections.approvals.length === 0) {
        const approvals = [
            {
                _id: 'approval-001',
                operationType: '大额退款',
                description: '用户申请退款金额：¥5,800',
                applicant: '张三',
                applicantId: 'user-001',
                priority: 'high',
                status: 'pending',
                details: {
                    orderId: 'WP202507100001',
                    amount: 5800,
                    reason: '产品质量问题'
                },
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
            },
            {
                _id: 'approval-002',
                operationType: '用户数据导出',
                description: '批量导出用户数据请求',
                applicant: '李四',
                applicantId: 'admin-002',
                priority: 'urgent',
                status: 'pending',
                details: {
                    dataType: 'user_profiles',
                    count: 10000,
                    purpose: '数据分析'
                },
                createdAt: new Date(Date.now() - 4 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 4 * 60 * 60 * 1000)
            },
            {
                _id: 'approval-003',
                operationType: '系统配置修改',
                description: '修改支付接口配置',
                applicant: '王五',
                applicantId: 'admin-003',
                priority: 'normal',
                status: 'approved',
                approver: '系统管理员',
                approvedAt: new Date(Date.now() - 6 * 60 * 60 * 1000),
                details: {
                    configType: 'payment_gateway',
                    changes: '更新支付宝API密钥'
                },
                createdAt: new Date(Date.now() - 8 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 6 * 60 * 60 * 1000)
            }
        ];

        approvals.forEach(approval => {
            createDocument('approvals', approval);
        });

        console.log('已创建默认审批记录');
    }

    // 创建默认安全日志
    if (collections.securityLogs.length === 0) {
        const securityLogs = [
            {
                _id: 'log-001',
                timestamp: new Date(Date.now() - 30 * 60 * 1000),
                level: 'warning',
                event: '异常登录尝试',
                description: '用户从异常IP地址尝试登录',
                source: 'auth_service',
                ip: '*************',
                userId: 'user-001',
                details: {
                    attempts: 3,
                    location: '北京市',
                    userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                }
            },
            {
                _id: 'log-002',
                timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000),
                level: 'error',
                event: 'API访问被拒绝',
                description: 'API密钥无效或已过期',
                source: 'api_gateway',
                ip: '*********',
                userId: null,
                details: {
                    endpoint: '/api/users',
                    method: 'GET',
                    apiKey: 'wp_invalid_key'
                }
            },
            {
                _id: 'log-003',
                timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000),
                level: 'info',
                event: '管理员登录',
                description: '管理员成功登录系统',
                source: 'admin_panel',
                ip: '************',
                userId: 'admin',
                details: {
                    loginMethod: 'password',
                    sessionId: 'sess_abc123'
                }
            },
            {
                _id: 'log-004',
                timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000),
                level: 'warning',
                event: '大额支付触发风控',
                description: '支付金额超过风控阈值',
                source: 'payment_service',
                ip: '*************',
                userId: 'user-005',
                details: {
                    amount: 15000,
                    orderId: 'WP202507100005',
                    riskScore: 85
                }
            }
        ];

        securityLogs.forEach(log => {
            createDocument('securityLogs', log);
        });

        console.log('已创建默认安全日志');
    }

    // 创建默认备份记录
    if (collections.backups.length === 0) {
        const backups = [
            {
                _id: 'backup-001',
                name: '每日自动备份_20250710',
                type: 'auto',
                description: '系统每日自动备份',
                size: 2147483648, // 2GB
                status: 'completed',
                path: '/backups/auto/backup_20250710.tar.gz',
                checksum: 'sha256:a1b2c3d4e5f6...',
                createdAt: new Date(Date.now() - 12 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 11 * 60 * 60 * 1000)
            },
            {
                _id: 'backup-002',
                name: '手动备份_系统升级前',
                type: 'manual',
                description: '系统升级前的手动备份',
                size: 3221225472, // 3GB
                status: 'completed',
                path: '/backups/manual/backup_upgrade_20250709.tar.gz',
                checksum: 'sha256:f6e5d4c3b2a1...',
                createdAt: new Date(Date.now() - 36 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 35 * 60 * 60 * 1000)
            },
            {
                _id: 'backup-003',
                name: '每周备份_20250707',
                type: 'scheduled',
                description: '每周定时备份',
                size: 5368709120, // 5GB
                status: 'completed',
                path: '/backups/weekly/backup_20250707.tar.gz',
                checksum: 'sha256:123456789abc...',
                createdAt: new Date(Date.now() - 72 * 60 * 60 * 1000),
                completedAt: new Date(Date.now() - 71 * 60 * 60 * 1000)
            },
            {
                _id: 'backup-004',
                name: '增量备份_20250710_02',
                type: 'incremental',
                description: '增量备份',
                size: 536870912, // 512MB
                status: 'running',
                path: '/backups/incremental/backup_20250710_02.tar.gz',
                checksum: null,
                createdAt: new Date(Date.now() - 30 * 60 * 1000),
                completedAt: null
            }
        ];

        backups.forEach(backup => {
            createDocument('backups', backup);
        });

        console.log('已创建默认备份记录');
    }

    // 创建默认IP黑名单
    if (collections.ipBlacklist.length === 0) {
        const ipBlacklist = [
            {
                _id: 'ip-001',
                ip: '**************',
                reason: '恶意攻击',
                description: '多次尝试暴力破解登录密码',
                addedBy: '系统管理员',
                addedById: 'admin',
                status: 'active',
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30天后过期
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'ip-002',
                ip: '**********',
                reason: 'SQL注入尝试',
                description: '检测到SQL注入攻击尝试',
                addedBy: '安全系统',
                addedById: 'system',
                status: 'active',
                expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7天后过期
                createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'ip-003',
                ip: '************',
                reason: '异常API调用',
                description: '短时间内大量API调用，疑似爬虫',
                addedBy: '系统管理员',
                addedById: 'admin',
                status: 'active',
                expiresAt: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000), // 14天后过期
                createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'ip-004',
                ip: '************',
                reason: '垃圾注册',
                description: '批量注册虚假账户',
                addedBy: '系统管理员',
                addedById: 'admin',
                status: 'expired',
                expiresAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 已过期
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            }
        ];

        ipBlacklist.forEach(ip => {
            createDocument('ipBlacklist', ip);
        });

        console.log('已创建默认IP黑名单');
    }

    // 创建默认角色
    if (collections.roles.length === 0) {
        const roles = [
            {
                _id: 'role-001',
                name: '超级管理员',
                description: '拥有系统所有权限的最高级别管理员',
                permissions: [
                    'system.view', 'system.edit', 'system.security', 'system.api', 'system.backup',
                    'user.view', 'user.add', 'user.edit', 'user.delete', 'user.export', 'user.import',
                    'role.view', 'role.add', 'role.edit', 'role.delete', 'role.assign',
                    'agent.view', 'agent.add', 'agent.edit', 'agent.delete', 'agent.commission', 'agent.approve',
                    'finance.view', 'finance.transaction', 'finance.refund', 'finance.report', 'finance.withdraw',
                    'content.view', 'content.add', 'content.edit', 'content.delete', 'content.publish',
                    'marketing.view', 'marketing.coupon', 'marketing.campaign', 'marketing.referral', 'marketing.points',
                    'security.view', 'security.rules', 'security.blacklist', 'security.approval', 'security.backup'
                ],
                level: 10,
                status: 'active',
                userCount: 1,
                createdAt: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-002',
                name: '系统管理员',
                description: '负责系统配置和用户管理的管理员',
                permissions: [
                    'system.view', 'system.edit',
                    'user.view', 'user.add', 'user.edit', 'user.export',
                    'role.view', 'role.assign',
                    'content.view', 'content.edit',
                    'security.view'
                ],
                level: 8,
                status: 'active',
                userCount: 3,
                createdAt: new Date(Date.now() - 80 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-003',
                name: '财务管理员',
                description: '负责财务数据管理和交易处理的管理员',
                permissions: [
                    'finance.view', 'finance.transaction', 'finance.refund', 'finance.report',
                    'user.view',
                    'agent.view', 'agent.commission'
                ],
                level: 6,
                status: 'active',
                userCount: 2,
                createdAt: new Date(Date.now() - 70 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-004',
                name: '内容管理员',
                description: '负责内容发布和管理的管理员',
                permissions: [
                    'content.view', 'content.add', 'content.edit', 'content.publish',
                    'marketing.view', 'marketing.coupon', 'marketing.campaign'
                ],
                level: 5,
                status: 'active',
                userCount: 4,
                createdAt: new Date(Date.now() - 60 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-005',
                name: '客服管理员',
                description: '负责用户服务和基础数据查看的管理员',
                permissions: [
                    'user.view',
                    'agent.view',
                    'content.view',
                    'finance.view'
                ],
                level: 3,
                status: 'active',
                userCount: 8,
                createdAt: new Date(Date.now() - 50 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-006',
                name: '数据分析师',
                description: '负责数据分析和报表查看的角色',
                permissions: [
                    'user.view', 'user.export',
                    'agent.view',
                    'finance.view', 'finance.report',
                    'marketing.view'
                ],
                level: 4,
                status: 'active',
                userCount: 2,
                createdAt: new Date(Date.now() - 40 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            },
            {
                _id: 'role-007',
                name: '试用管理员',
                description: '试用期管理员，权限受限',
                permissions: [
                    'user.view',
                    'content.view',
                    'finance.view'
                ],
                level: 2,
                status: 'inactive',
                userCount: 0,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000)
            }
        ];

        roles.forEach(role => {
            createDocument('roles', role);
        });

        console.log('已创建默认角色');
    }
    
    // 创建默认内容
    if (collections.contents.length === 0) {
        // 用户协议
        createDocument('contents', {
            _id: 'agreement-id',
            title: '用户服务协议',
            type: 'agreement',
            content: '<h1>WriterPro用户服务协议</h1><p>这是默认的用户协议内容。</p>',
            version: 'v1.0',
            status: 'published',
            publishedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        });
        
        // 隐私政策
        createDocument('contents', {
            _id: 'privacy-id',
            title: '隐私政策',
            type: 'agreement',
            content: '<h1>WriterPro隐私政策</h1><p>这是默认的隐私政策内容。</p>',
            version: 'v1.0',
            status: 'published',
            publishedAt: new Date(),
            createdAt: new Date(),
            updatedAt: new Date()
        });
        
        console.log('已创建默认内容');
    }
};

// 初始化默认数据
initDefaultData();

module.exports = {
    createDocument,
    findDocuments,
    findOneDocument,
    updateDocument,
    deleteDocument,
    countDocuments,
    collections
}; 