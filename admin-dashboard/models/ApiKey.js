const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const ApiKeySchema = new Schema({
    name: {
        type: String,
        required: true
    },
    key: {
        type: String,
        required: true,
        unique: true
    },
    type: {
        type: String,
        enum: ['google', 'payment', 'client'],
        required: true
    },
    provider: {
        type: String,
        default: ''
    },
    projectId: {
        type: String,
        default: ''
    },
    services: [{
        type: String
    }],
    limit: {
        type: String,
        default: ''
    },
    status: {
        type: String,
        enum: ['active', 'inactive'],
        default: 'active'
    },
    expiryDate: {
        type: Date
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    },
    lastUsed: {
        type: Date
    }
});

// 更新时自动更新updatedAt字段
ApiKeySchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    next();
});

module.exports = mongoose.model('ApiKey', ApiKeySchema); 