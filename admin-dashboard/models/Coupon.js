const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const CouponSchema = new Schema({
    code: {
        type: String,
        required: true,
        unique: true,
        uppercase: true
    },
    name: {
        type: String,
        required: true
    },
    type: {
        type: String,
        enum: ['percentage', 'fixed', 'free'],
        default: 'percentage'
    },
    value: {
        type: Number,
        required: true
    },
    limitType: {
        type: String,
        enum: ['none', 'new', 'vip', 'custom'],
        default: 'none'
    },
    startDate: {
        type: Date,
        required: true
    },
    endDate: {
        type: Date,
        required: true
    },
    totalAmount: {
        type: Number,
        required: true,
        default: 100
    },
    usedAmount: {
        type: Number,
        default: 0
    },
    description: {
        type: String,
        default: ''
    },
    status: {
        type: String,
        enum: ['active', 'pending', 'expired', 'disabled'],
        default: 'active'
    },
    createdBy: {
        type: Schema.Types.ObjectId,
        ref: 'User'
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});

// 更新时自动更新updatedAt字段
CouponSchema.pre('save', function(next) {
    this.updatedAt = Date.now();
    
    // 根据日期自动设置状态
    const now = new Date();
    if (now < this.startDate) {
        this.status = 'pending';
    } else if (now > this.endDate) {
        this.status = 'expired';
    } else if (this.status !== 'disabled') {
        this.status = 'active';
    }
    
    next();
});

module.exports = mongoose.model('Coupon', CouponSchema); 