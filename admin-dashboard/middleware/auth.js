/**
 * 认证中间件
 */

// 检查用户是否已登录
const isAuthenticated = (req, res, next) => {
    if (req.session && req.session.user) {
        return next();
    }
    
    // API请求返回401状态码
    if (req.path.startsWith('/api/')) {
        return res.status(401).json({ success: false, message: '未授权，请先登录' });
    }
    
    // 页面请求重定向到登录页
    res.redirect('/');
};

// 检查用户是否具有指定角色
const hasRole = (roles) => {
    return (req, res, next) => {
        if (!req.session || !req.session.user) {
            return res.status(401).json({ success: false, message: '未授权，请先登录' });
        }
        
        if (roles.includes(req.session.user.role)) {
            return next();
        }
        
        return res.status(403).json({ success: false, message: '权限不足，无法访问此资源' });
    };
};

module.exports = {
    isAuthenticated,
    hasRole
}; 