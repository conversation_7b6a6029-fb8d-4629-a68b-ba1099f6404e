/**
 * 展开式导航菜单功能
 */
class ExpandableNav {
    constructor() {
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadDefaultContent();
        this.preventSidebarCollapse();
    }

    preventSidebarCollapse() {
        // 确保侧边栏在桌面端保持展开状态
        const adminLayout = document.querySelector('.admin-layout');
        if (adminLayout && window.innerWidth > 768) {
            adminLayout.classList.remove('sidebar-collapsed');
        }

        // 监听窗口大小变化
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768) {
                adminLayout.classList.remove('sidebar-collapsed');
                adminLayout.classList.remove('sidebar-mobile');
            }
        });
    }

    bindEvents() {
        // 绑定主菜单点击事件
        document.querySelectorAll('.menu-item.has-submenu > .menu-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggleSubmenu(link.parentElement);
            });
        });

        // 绑定子菜单点击事件
        document.querySelectorAll('.submenu a[data-content]').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('子菜单点击:', link.dataset.content); // 调试日志
                this.loadContent(link.dataset.content, link);
            });
        });

        // 绑定仪表盘点击事件
        const dashboardLink = document.querySelector('.menu-link[data-content="dashboard"]');
        if (dashboardLink) {
            dashboardLink.addEventListener('click', (e) => {
                e.preventDefault();
                console.log('仪表盘点击'); // 调试日志
                this.loadContent('dashboard', e.target.closest('.menu-link'));
            });
        }
    }

    toggleSubmenu(menuItem) {
        const isExpanded = menuItem.classList.contains('expanded');
        
        // 关闭其他展开的菜单
        document.querySelectorAll('.menu-item.expanded').forEach(item => {
            if (item !== menuItem) {
                item.classList.remove('expanded');
            }
        });

        // 切换当前菜单
        if (isExpanded) {
            menuItem.classList.remove('expanded');
        } else {
            menuItem.classList.add('expanded');
        }
    }

    async loadContent(contentType, clickedElement) {
        try {
            console.log('加载内容:', contentType); // 调试日志

            // 更新活动状态
            this.updateActiveState(clickedElement);

            // 更新页面标题
            this.updatePageTitle(contentType);

            // 显示加载状态
            const dynamicContent = document.getElementById('dynamic-content');
            if (dynamicContent) {
                dynamicContent.innerHTML = `
                    <div class="loading-state">
                        <div class="spinner"></div>
                        <p>加载中...</p>
                    </div>
                `;
            }

            // 加载内容
            const content = await this.getContentHTML(contentType);

            if (dynamicContent) {
                dynamicContent.innerHTML = content;
                console.log('内容已加载'); // 调试日志
            } else {
                console.error('找不到 dynamic-content 元素');
            }

            // 初始化内容相关的功能
            this.initContentFeatures(contentType);
        } catch (error) {
            console.error('加载内容时出错:', error);
            const dynamicContent = document.getElementById('dynamic-content');
            if (dynamicContent) {
                dynamicContent.innerHTML = this.getErrorContent(contentType, error);
            }
        }
    }

    updateActiveState(clickedElement) {
        // 移除所有活动状态
        document.querySelectorAll('.menu-item.active').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelectorAll('.submenu a.active').forEach(link => {
            link.classList.remove('active');
        });

        // 添加新的活动状态
        if (clickedElement.closest('.submenu')) {
            // 子菜单项
            clickedElement.classList.add('active');
            clickedElement.closest('.menu-item').classList.add('active');
        } else {
            // 主菜单项
            clickedElement.closest('.menu-item').classList.add('active');
        }
    }

    updatePageTitle(contentType) {
        const titles = {
            'dashboard': '仪表盘',
            'users-list': '用户列表',
            'users-analytics': '用户分析',
            'users-permissions': '权限管理',
            'users-activity': '活动日志',
            'workorders-list': '工单列表',
            'workorders-assign': '工单分配',
            'workorders-status': '状态管理',
            'workorders-reports': '工单报告',
            'experts-list': '专家列表',
            'experts-skills': '技能管理',
            'experts-performance': '绩效评估',
            'experts-schedule': '排班管理',
            'agents-list': '代理列表',
            'agents-commission': '佣金管理',
            'agents-performance': '业绩统计',
            'agents-hierarchy': '层级管理',
            'finance-overview': '财务概览',
            'finance-transactions': '交易记录',
            'finance-reports': '财务报表',
            'finance-settings': '财务设置',
            'content-list': '内容列表',
            'content-categories': '分类管理',
            'content-templates': '模板管理',
            'content-review': '内容审核',
            'api-keys': 'API密钥',
            'api-docs': 'API文档',
            'api-monitor': 'API监控',
            'api-limits': '限流设置',
            'marketing-campaigns': '营销活动',
            'marketing-coupons': '优惠券',
            'marketing-analytics': '营销分析',
            'marketing-automation': '自动化',
            'security-logs': '安全日志',
            'security-blacklist': '黑名单',
            'security-settings': '安全设置',
            'security-monitor': '实时监控',
            'settings-general': '基本设置',
            'settings-email': '邮件配置',
            'settings-backup': '备份管理',
            'settings-logs': '系统日志'
        };

        const title = titles[contentType] || '管理后台';
        document.querySelector('.header h2').textContent = title;
        document.title = `${title} - WriterPro 管理后台`;
    }

    async getContentHTML(contentType) {
        // 直接调用对应的函数，如果不存在则返回默认内容
        try {
            switch(contentType) {
                case 'dashboard':
                    return await this.getDashboardContent();
                case 'users-list':
                    return await this.getUsersListContent();
                case 'users-analytics':
                    return await this.getUsersAnalyticsContent();
                case 'users-permissions':
                    return await this.getUsersPermissionsContent();
                case 'users-activity':
                    return await this.getUsersActivityContent();
                case 'workorders-list':
                    return await this.getWorkordersListContent();
                case 'workorders-assign':
                    return await this.getWorkordersAssignContent();
                case 'workorders-status':
                    return this.getDefaultContent('工单状态管理');
                case 'workorders-reports':
                    return this.getDefaultContent('工单报告');
                case 'experts-list':
                    return this.getDefaultContent('专家列表');
                case 'experts-skills':
                    return this.getDefaultContent('技能管理');
                case 'experts-performance':
                    return this.getDefaultContent('绩效评估');
                case 'experts-schedule':
                    return this.getDefaultContent('排班管理');
                case 'agents-list':
                    return await this.getAgentsListContent();
                case 'agents-commission':
                    return await this.getAgentsCommissionContent();
                case 'agents-performance':
                    return await this.getAgentsPerformanceContent();
                case 'agents-hierarchy':
                    return await this.getAgentsHierarchyContent();
                case 'finance-overview':
                    return await this.getFinanceOverviewContent();
                case 'finance-transactions':
                    return await this.getFinanceTransactionsContent();
                case 'finance-reports':
                    return this.getDefaultContent('财务报表');
                case 'finance-settings':
                    return await this.getPriceSettingsContent();
                case 'content-list':
                    return await this.getContentListContent();
                case 'content-categories':
                    return this.getDefaultContent('分类管理');
                case 'content-templates':
                    return this.getDefaultContent('模板管理');
                case 'content-review':
                    return this.getDefaultContent('内容审核');
                case 'api-keys':
                    return await this.getApiKeysContent();
                case 'api-docs':
                    return this.getDefaultContent('API文档');
                case 'api-monitor':
                    return this.getDefaultContent('API监控');
                case 'api-limits':
                    return this.getDefaultContent('限流设置');
                case 'marketing-campaigns':
                    return await this.getMarketingCampaignsContent();
                case 'marketing-coupons':
                    return this.getDefaultContent('优惠券管理');
                case 'marketing-analytics':
                    return this.getDefaultContent('营销分析');
                case 'marketing-automation':
                    return this.getDefaultContent('营销自动化');
                case 'security-logs':
                    return await this.getSecurityLogsContent();
                case 'security-blacklist':
                    return this.getDefaultContent('黑名单管理');
                case 'security-settings':
                    return this.getDefaultContent('安全设置');
                case 'security-monitor':
                    return this.getDefaultContent('实时监控');
                case 'settings-general':
                    return await this.getSettingsGeneralContent();
                case 'settings-email':
                    return this.getDefaultContent('邮件配置');
                case 'settings-backup':
                    return this.getDefaultContent('备份管理');
                case 'settings-logs':
                    return this.getDefaultContent('系统日志');
                default:
                    return this.getDefaultContent(contentType);
            }
        } catch (error) {
            console.error('获取内容失败:', error);
            return this.getErrorContent(contentType, error);
        }
    }

    // 辅助方法
    getAgentLevelText(level) {
        const levels = {
            'bronze': '铜牌代理',
            'silver': '银牌代理',
            'gold': '金牌代理',
            'diamond': '钻石代理'
        };
        return levels[level] || '普通代理';
    }

    getErrorContent(title, error) {
        return `
            <div class="page-header">
                <h3>${title}</h3>
            </div>
            <div class="content-card">
                <div class="error-state">
                    <i class="bi bi-exclamation-triangle" style="font-size: 48px; color: #ff4d4f;"></i>
                    <h4>加载失败</h4>
                    <p>无法加载数据: ${error.message || '未知错误'}</p>
                    <button class="btn btn-primary" onclick="location.reload()">重新加载</button>
                </div>
            </div>
        `;
    }

    getDashboardContent() {
        return `
            <!-- 欢迎卡片 -->
            <div class="welcome-card">
                <div class="welcome-info">
                    <h3>欢迎回来，管理员</h3>
                    <p>这里是今日数据概览</p>
                </div>
                <div class="welcome-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-download"></i>
                        <span>导出报告</span>
                    </button>
                </div>
            </div>
            
            <!-- 数据卡片 -->
            <div class="stats-cards">
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-info">
                        <h4>总用户数</h4>
                        <div class="stat-value">12,345</div>
                        <div class="stat-change increase">
                            <i class="bi bi-arrow-up"></i>
                            <span>8.5%</span>
                            <span class="period">较上月</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                        <i class="bi bi-currency-yen"></i>
                    </div>
                    <div class="stat-info">
                        <h4>本月收入</h4>
                        <div class="stat-value">¥45,678</div>
                        <div class="stat-change increase">
                            <i class="bi bi-arrow-up"></i>
                            <span>12.3%</span>
                            <span class="period">较上月</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                        <i class="bi bi-clipboard-check"></i>
                    </div>
                    <div class="stat-info">
                        <h4>待处理工单</h4>
                        <div class="stat-value">23</div>
                        <div class="stat-change decrease">
                            <i class="bi bi-arrow-down"></i>
                            <span>15.2%</span>
                            <span class="period">较昨日</span>
                        </div>
                    </div>
                </div>
                
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                        <i class="bi bi-exclamation-triangle"></i>
                    </div>
                    <div class="stat-info">
                        <h4>系统警告</h4>
                        <div class="stat-value">5</div>
                        <div class="stat-change increase">
                            <i class="bi bi-arrow-up"></i>
                            <span>2</span>
                            <span class="period">新增</span>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async getUsersListContent() {
        console.log('🔄 开始加载用户列表内容...');

        // 直接使用简化的用户列表，确保显示真实数据
        return await this.getSimpleUsersList();
    }

    // 加载用户管理脚本
    loadUserManagementScript() {
        if (!document.querySelector('script[src*="users.js"]')) {
            const script = document.createElement('script');
            script.src = '/admin-dashboard/users.js';
            script.onload = () => {
                console.log('用户管理脚本加载完成');
                // 初始化用户管理功能
                if (typeof loadUsers === 'function') {
                    setTimeout(() => {
                        loadUsers();
                        loadStatistics();
                    }, 100);
                }
            };
            document.head.appendChild(script);
        }
    }

    // 简化的用户列表（备用方案）
    async getSimpleUsersList() {
        try {
            console.log('📡 正在获取用户数据...');

            // 获取真实用户数据 - 连接到管理后台API
            const response = await fetch('/api/users', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token') || 'demo-token'}`,
                    'Content-Type': 'application/json'
                }
            });

            let users = [];
            if (response.ok) {
                const data = await response.json();
                users = data.data || data.users || [];
                console.log('✅ 成功获取用户数据:', users.length, '个用户');
                console.log('📋 用户数据预览:', users.slice(0, 2));
            } else {
                console.log('⚠️ API调用失败，使用模拟数据');
                // 使用模拟数据
                users = [
                    { id: 1, username: '张三', email: '<EMAIL>', status: 'active', createdAt: '2024-01-15' },
                    { id: 2, username: '李四', email: '<EMAIL>', status: 'active', createdAt: '2024-01-16' },
                    { id: 3, username: '王五', email: '<EMAIL>', status: 'inactive', createdAt: '2024-01-17' }
                ];
            }

            const userRows = users.map(user => `
                <tr>
                    <td>${user.id}</td>
                    <td>${user.username || user.name}</td>
                    <td>${user.email}</td>
                    <td>${new Date(user.createdAt || user.created_at).toLocaleDateString()}</td>
                    <td><span class="status-badge ${user.status === 'active' ? 'active' : 'warning'}">${user.status === 'active' ? '活跃' : '禁用'}</span></td>
                    <td>
                        <button class="btn-icon" title="编辑" onclick="editUser('${user.id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn-icon" title="删除" onclick="deleteUser('${user.id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

            return `
                <div class="page-header">
                    <h3>用户列表</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="showAddUserModal()">
                            <i class="bi bi-plus"></i>
                            添加用户
                        </button>
                        <button class="btn btn-secondary" onclick="refreshUsersList()">
                            <i class="bi bi-arrow-clockwise"></i>
                            刷新
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="search-filters">
                            <input type="text" id="userSearch" placeholder="搜索用户..." class="search-input" onkeyup="searchUsers()">
                            <select id="userStatusFilter" class="filter-select" onchange="filterUsers()">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>用户ID</th>
                                    <th>用户名</th>
                                    <th>邮箱</th>
                                    <th>注册时间</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="usersTableBody">
                                ${userRows}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('获取用户数据失败:', error);
            return this.getErrorContent('用户列表', error);
        }
    }

    async getUsersAnalyticsContent() {
        try {
            // 获取真实的用户分析数据
            const response = await fetch('/api/admin/user-analytics', {
                headers: {
                    'Authorization': `Bearer ${localStorage.getItem('admin_token') || 'demo-token'}`
                }
            });

            let analytics = {};
            if (response.ok) {
                analytics = await response.json();
            } else {
                // 使用模拟数据
                analytics = {
                    totalUsers: 1234,
                    newUsers: 89,
                    activeUsers: 567,
                    churnUsers: 23,
                    userGrowth: 12.5,
                    newUserGrowth: 8.3,
                    activeUserGrowth: 15.2,
                    churnRate: -2.1,
                    retentionRate: 78.5
                };
            }

            return `
                <div class="page-header">
                    <h3>用户分析</h3>
                    <div class="page-actions">
                        <select id="analyticsTimeRange" class="filter-select" onchange="updateUserAnalytics()">
                            <option value="7">最近7天</option>
                            <option value="30" selected>最近30天</option>
                            <option value="90">最近90天</option>
                        </select>
                        <button class="btn btn-secondary" onclick="exportAnalytics()">
                            <i class="bi bi-download"></i>
                            导出报告
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                            <i class="bi bi-people"></i>
                        </div>
                        <div class="stat-info">
                            <h4>总用户数</h4>
                            <div class="stat-value">${analytics.totalUsers || 0}</div>
                            <div class="stat-change ${analytics.userGrowth >= 0 ? 'increase' : 'decrease'}">
                                <i class="bi bi-arrow-${analytics.userGrowth >= 0 ? 'up' : 'down'}"></i>
                                <span>${Math.abs(analytics.userGrowth || 0)}%</span>
                                <span class="period">较上期</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                            <i class="bi bi-person-plus"></i>
                        </div>
                        <div class="stat-info">
                            <h4>新增用户</h4>
                            <div class="stat-value">${analytics.newUsers || 0}</div>
                            <div class="stat-change ${analytics.newUserGrowth >= 0 ? 'increase' : 'decrease'}">
                                <i class="bi bi-arrow-${analytics.newUserGrowth >= 0 ? 'up' : 'down'}"></i>
                                <span>${Math.abs(analytics.newUserGrowth || 0)}%</span>
                                <span class="period">较上期</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <div class="stat-info">
                            <h4>活跃用户</h4>
                            <div class="stat-value">${analytics.activeUsers || 0}</div>
                            <div class="stat-change ${analytics.activeUserGrowth >= 0 ? 'increase' : 'decrease'}">
                                <i class="bi bi-arrow-${analytics.activeUserGrowth >= 0 ? 'up' : 'down'}"></i>
                                <span>${Math.abs(analytics.activeUserGrowth || 0)}%</span>
                                <span class="period">较上期</span>
                            </div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(255, 77, 79, 0.1); color: #ff4d4f;">
                            <i class="bi bi-person-x"></i>
                        </div>
                        <div class="stat-info">
                            <h4>留存率</h4>
                            <div class="stat-value">${analytics.retentionRate || 0}%</div>
                            <div class="stat-change ${analytics.retentionRate >= 70 ? 'increase' : 'decrease'}">
                                <i class="bi bi-arrow-${analytics.retentionRate >= 70 ? 'up' : 'down'}"></i>
                                <span>7天留存</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>用户增长趋势</h4>
                        <div class="card-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="refreshGrowthChart()">
                                <i class="bi bi-arrow-clockwise"></i>
                                刷新
                            </button>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="user-growth-chart" style="height: 300px;"></canvas>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="content-card">
                            <div class="card-header">
                                <h4>用户地域分布</h4>
                            </div>
                            <div class="chart-container">
                                <canvas id="user-location-chart" style="height: 250px;"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="content-card">
                            <div class="card-header">
                                <h4>用户活跃度</h4>
                            </div>
                            <div class="activity-stats">
                                <div class="activity-item">
                                    <span class="activity-label">日活跃用户</span>
                                    <span class="activity-value">${Math.floor((analytics.activeUsers || 0) * 0.3)}</span>
                                </div>
                                <div class="activity-item">
                                    <span class="activity-label">周活跃用户</span>
                                    <span class="activity-value">${Math.floor((analytics.activeUsers || 0) * 0.7)}</span>
                                </div>
                                <div class="activity-item">
                                    <span class="activity-label">月活跃用户</span>
                                    <span class="activity-value">${analytics.activeUsers || 0}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // 初始化图表
                    setTimeout(() => {
                        if (typeof Chart !== 'undefined') {
                            initUserAnalyticsCharts(${JSON.stringify(analytics)});
                        } else {
                            // 加载Chart.js
                            const script = document.createElement('script');
                            script.src = 'https://cdn.jsdelivr.net/npm/chart.js';
                            script.onload = () => initUserAnalyticsCharts(${JSON.stringify(analytics)});
                            document.head.appendChild(script);
                        }
                    }, 100);
                </script>
            `;
        } catch (error) {
            console.error('获取用户分析数据失败:', error);
            return this.getErrorContent('用户分析', error);
        }
    }

    async getAgentsListContent() {
        try {
            const agents = await apiClient.getAgents();

            const agentRows = agents.data ? agents.data.map(agent => `
                <tr>
                    <td>${agent.id || agent._id}</td>
                    <td>${agent.name}</td>
                    <td>${agent.phone || agent.email}</td>
                    <td><span class="level-badge level-${agent.level}">${this.getAgentLevelText(agent.level)}</span></td>
                    <td>${agent.commissionRate}%</td>
                    <td><span class="status-badge ${agent.status === 'active' ? 'active' : 'warning'}">${agent.status === 'active' ? '活跃' : '禁用'}</span></td>
                    <td>
                        <button class="btn-icon" title="查看详情" onclick="viewAgent('${agent.id || agent._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn-icon" title="编辑" onclick="editAgent('${agent.id || agent._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn-icon" title="佣金详情" onclick="viewAgentCommission('${agent.id || agent._id}')">
                            <i class="bi bi-currency-yen"></i>
                        </button>
                    </td>
                </tr>
            `).join('') : '<tr><td colspan="7" class="text-center">暂无数据</td></tr>';

            return `
                <div class="page-header">
                    <h3>代理列表</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary" onclick="showAddAgentModal()">
                            <i class="bi bi-plus"></i>
                            添加代理
                        </button>
                        <button class="btn btn-secondary" onclick="exportAgents()">
                            <i class="bi bi-download"></i>
                            导出数据
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="search-filters">
                            <input type="text" id="agentSearch" placeholder="搜索代理..." class="search-input" onkeyup="searchAgents()">
                            <select id="agentLevelFilter" class="filter-select" onchange="filterAgents()">
                                <option value="">全部等级</option>
                                <option value="bronze">铜牌代理</option>
                                <option value="silver">银牌代理</option>
                                <option value="gold">金牌代理</option>
                                <option value="diamond">钻石代理</option>
                            </select>
                            <select id="agentStatusFilter" class="filter-select" onchange="filterAgents()">
                                <option value="">全部状态</option>
                                <option value="active">活跃</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>代理ID</th>
                                    <th>代理名称</th>
                                    <th>联系方式</th>
                                    <th>等级</th>
                                    <th>佣金率</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody id="agentsTableBody">
                                ${agentRows}
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            console.error('获取代理列表失败:', error);
            return this.getErrorContent('代理列表', error);
        }
    }

    getAgentsCommissionContent() {
        return `
            <div class="page-header">
                <h3>佣金管理</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-calculator"></i>
                        计算佣金
                    </button>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                        <i class="bi bi-currency-yen"></i>
                    </div>
                    <div class="stat-info">
                        <h4>本月佣金总额</h4>
                        <div class="stat-value">¥25,680</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                        <i class="bi bi-clock"></i>
                    </div>
                    <div class="stat-info">
                        <h4>待结算佣金</h4>
                        <div class="stat-value">¥8,450</div>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h4>佣金记录</h4>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>代理</th>
                                <th>订单号</th>
                                <th>佣金金额</th>
                                <th>佣金率</th>
                                <th>结算状态</th>
                                <th>结算时间</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>李代理</td>
                                <td>ORD20240115001</td>
                                <td>¥150.00</td>
                                <td>15%</td>
                                <td><span class="status-badge success">已结算</span></td>
                                <td>2024-01-15</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getFinanceOverviewContent() {
        return `
            <div class="page-header">
                <h3>财务概览</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-download"></i>
                        导出报告
                    </button>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                        <i class="bi bi-currency-yen"></i>
                    </div>
                    <div class="stat-info">
                        <h4>总收入</h4>
                        <div class="stat-value">¥125,680</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                        <i class="bi bi-graph-up"></i>
                    </div>
                    <div class="stat-info">
                        <h4>本月收入</h4>
                        <div class="stat-value">¥25,680</div>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h4>收入趋势</h4>
                </div>
                <div class="chart-container">
                    <div style="height: 300px; display: flex; align-items: center; justify-content: center; color: #999;">
                        财务图表加载中...
                    </div>
                </div>
            </div>
        `;
    }

    getContentListContent() {
        return `
            <div class="page-header">
                <h3>内容列表</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-plus"></i>
                        添加内容
                    </button>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <div class="search-filters">
                        <input type="text" placeholder="搜索内容..." class="search-input">
                        <select class="filter-select">
                            <option>全部类型</option>
                            <option>文章</option>
                            <option>模板</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>标题</th>
                                <th>类型</th>
                                <th>作者</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>示例文章标题</td>
                                <td>文章</td>
                                <td>管理员</td>
                                <td>2024-01-15</td>
                                <td><span class="status-badge active">已发布</span></td>
                                <td>
                                    <button class="btn-icon" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn-icon" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getApiKeysContent() {
        return `
            <div class="page-header">
                <h3>API密钥管理</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-key"></i>
                        生成新密钥
                    </button>
                </div>
            </div>

            <div class="content-card">
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>密钥名称</th>
                                <th>密钥</th>
                                <th>权限</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>默认API密钥</td>
                                <td>sk-**********************</td>
                                <td>读写</td>
                                <td>2024-01-01</td>
                                <td><span class="status-badge active">活跃</span></td>
                                <td>
                                    <button class="btn-icon" title="查看">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                    <button class="btn-icon" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getMarketingCampaignsContent() {
        return `
            <div class="page-header">
                <h3>营销活动</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-plus"></i>
                        创建活动
                    </button>
                </div>
            </div>

            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                        <i class="bi bi-megaphone"></i>
                    </div>
                    <div class="stat-info">
                        <h4>活跃活动</h4>
                        <div class="stat-value">5</div>
                    </div>
                </div>

                <div class="stat-card">
                    <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                        <i class="bi bi-people"></i>
                    </div>
                    <div class="stat-info">
                        <h4>参与用户</h4>
                        <div class="stat-value">1,234</div>
                    </div>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h4>活动列表</h4>
                </div>
                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>活动名称</th>
                                <th>类型</th>
                                <th>开始时间</th>
                                <th>结束时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>新年促销活动</td>
                                <td>优惠券</td>
                                <td>2024-01-01</td>
                                <td>2024-01-31</td>
                                <td><span class="status-badge active">进行中</span></td>
                                <td>
                                    <button class="btn-icon" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getSecurityLogsContent() {
        return `
            <div class="page-header">
                <h3>安全日志</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-download"></i>
                        导出日志
                    </button>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <div class="search-filters">
                        <input type="text" placeholder="搜索日志..." class="search-input">
                        <select class="filter-select">
                            <option>全部级别</option>
                            <option>警告</option>
                            <option>错误</option>
                        </select>
                    </div>
                </div>

                <div class="table-container">
                    <table class="data-table">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>级别</th>
                                <th>事件</th>
                                <th>IP地址</th>
                                <th>用户</th>
                                <th>详情</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>2024-01-15 10:30:25</td>
                                <td><span class="status-badge warning">警告</span></td>
                                <td>登录失败</td>
                                <td>*************</td>
                                <td>admin</td>
                                <td>
                                    <button class="btn-icon" title="查看详情">
                                        <i class="bi bi-eye"></i>
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        `;
    }

    getSettingsGeneralContent() {
        return `
            <div class="page-header">
                <h3>基本设置</h3>
                <div class="page-actions">
                    <button class="btn btn-primary">
                        <i class="bi bi-check"></i>
                        保存设置
                    </button>
                </div>
            </div>

            <div class="content-card">
                <div class="card-header">
                    <h4>系统配置</h4>
                </div>
                <div style="padding: 24px;">
                    <div class="form-group">
                        <label>网站名称</label>
                        <input type="text" value="WriterPro" class="form-control">
                    </div>

                    <div class="form-group">
                        <label>网站描述</label>
                        <textarea class="form-control" rows="3">专业的写作助手平台</textarea>
                    </div>

                    <div class="form-group">
                        <label>联系邮箱</label>
                        <input type="email" value="<EMAIL>" class="form-control">
                    </div>
                </div>
            </div>
        `;
    }

    // 用户权限管理
    async getUsersPermissionsContent() {
        try {
            return `
                <div class="page-header">
                    <h3>权限管理</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary">
                            <i class="bi bi-plus"></i>
                            添加角色
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>用户角色权限</h4>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>角色名称</th>
                                    <th>权限范围</th>
                                    <th>用户数量</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>管理员</td>
                                    <td>全部权限</td>
                                    <td>5</td>
                                    <td><span class="status-badge active">启用</span></td>
                                    <td>
                                        <button class="btn-icon" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>普通用户</td>
                                    <td>基础功能</td>
                                    <td>1,234</td>
                                    <td><span class="status-badge active">启用</span></td>
                                    <td>
                                        <button class="btn-icon" title="编辑">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('权限管理', error);
        }
    }

    // 用户活动日志
    async getUsersActivityContent() {
        try {
            return `
                <div class="page-header">
                    <h3>活动日志</h3>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="bi bi-download"></i>
                            导出日志
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="search-filters">
                            <input type="text" placeholder="搜索用户..." class="search-input">
                            <select class="filter-select">
                                <option>全部操作</option>
                                <option>登录</option>
                                <option>注册</option>
                                <option>文档处理</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作</th>
                                    <th>IP地址</th>
                                    <th>状态</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>2024-01-17 10:30:25</td>
                                    <td>张三</td>
                                    <td>文档优化</td>
                                    <td>*************</td>
                                    <td><span class="status-badge success">成功</span></td>
                                </tr>
                                <tr>
                                    <td>2024-01-17 10:25:15</td>
                                    <td>李四</td>
                                    <td>用户登录</td>
                                    <td>*************</td>
                                    <td><span class="status-badge success">成功</span></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('活动日志', error);
        }
    }

    // 工单列表
    async getWorkordersListContent() {
        try {
            return `
                <div class="page-header">
                    <h3>工单列表</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary">
                            <i class="bi bi-plus"></i>
                            创建工单
                        </button>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(250, 173, 20, 0.1); color: #faad14;">
                            <i class="bi bi-clock"></i>
                        </div>
                        <div class="stat-info">
                            <h4>待处理</h4>
                            <div class="stat-value">23</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                            <i class="bi bi-arrow-repeat"></i>
                        </div>
                        <div class="stat-info">
                            <h4>处理中</h4>
                            <div class="stat-value">15</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                            <i class="bi bi-check-circle"></i>
                        </div>
                        <div class="stat-info">
                            <h4>已完成</h4>
                            <div class="stat-value">156</div>
                        </div>
                    </div>
                </div>

                <div class="content-card">
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>工单号</th>
                                    <th>标题</th>
                                    <th>用户</th>
                                    <th>优先级</th>
                                    <th>状态</th>
                                    <th>创建时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>#WO001</td>
                                    <td>文档处理异常</td>
                                    <td>张三</td>
                                    <td><span class="priority-badge high">高</span></td>
                                    <td><span class="status-badge warning">待处理</span></td>
                                    <td>2024-01-17 09:30</td>
                                    <td>
                                        <button class="btn-icon" title="查看">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                        <button class="btn-icon" title="分配">
                                            <i class="bi bi-person-plus"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('工单列表', error);
        }
    }

    // 工单分配
    async getWorkordersAssignContent() {
        try {
            return `
                <div class="page-header">
                    <h3>工单分配</h3>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>专家工作负载</h4>
                    </div>
                    <div class="expert-workload">
                        <div class="expert-card">
                            <div class="expert-info">
                                <h5>专家A</h5>
                                <p>技术支持专家</p>
                            </div>
                            <div class="workload-stats">
                                <div class="workload-item">
                                    <span class="label">进行中</span>
                                    <span class="value">5</span>
                                </div>
                                <div class="workload-item">
                                    <span class="label">今日完成</span>
                                    <span class="value">12</span>
                                </div>
                            </div>
                            <div class="workload-bar">
                                <div class="progress-bar" style="width: 60%"></div>
                            </div>
                        </div>

                        <div class="expert-card">
                            <div class="expert-info">
                                <h5>专家B</h5>
                                <p>内容审核专家</p>
                            </div>
                            <div class="workload-stats">
                                <div class="workload-item">
                                    <span class="label">进行中</span>
                                    <span class="value">3</span>
                                </div>
                                <div class="workload-item">
                                    <span class="label">今日完成</span>
                                    <span class="value">8</span>
                                </div>
                            </div>
                            <div class="workload-bar">
                                <div class="progress-bar" style="width: 40%"></div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('工单分配', error);
        }
    }

    // 代理业绩统计
    async getAgentsPerformanceContent() {
        try {
            return `
                <div class="page-header">
                    <h3>业绩统计</h3>
                    <div class="page-actions">
                        <select class="filter-select">
                            <option>本月</option>
                            <option>上月</option>
                            <option>本季度</option>
                        </select>
                    </div>
                </div>

                <div class="stats-grid">
                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(82, 196, 26, 0.1); color: #52c41a;">
                            <i class="bi bi-trophy"></i>
                        </div>
                        <div class="stat-info">
                            <h4>最佳代理</h4>
                            <div class="stat-value">李代理</div>
                            <div class="stat-change">¥15,680</div>
                        </div>
                    </div>

                    <div class="stat-card">
                        <div class="stat-icon" style="background-color: rgba(24, 144, 255, 0.1); color: #1890ff;">
                            <i class="bi bi-graph-up"></i>
                        </div>
                        <div class="stat-info">
                            <h4>平均业绩</h4>
                            <div class="stat-value">¥8,450</div>
                        </div>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>代理业绩排行</h4>
                    </div>
                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>排名</th>
                                    <th>代理名称</th>
                                    <th>本月业绩</th>
                                    <th>订单数</th>
                                    <th>转化率</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>1</td>
                                    <td>李代理</td>
                                    <td>¥15,680</td>
                                    <td>45</td>
                                    <td>85%</td>
                                </tr>
                                <tr>
                                    <td>2</td>
                                    <td>王代理</td>
                                    <td>¥12,340</td>
                                    <td>38</td>
                                    <td>78%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('业绩统计', error);
        }
    }

    // 代理层级管理
    async getAgentsHierarchyContent() {
        try {
            return `
                <div class="page-header">
                    <h3>层级管理</h3>
                    <div class="page-actions">
                        <button class="btn btn-primary">
                            <i class="bi bi-diagram-3"></i>
                            查看层级图
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>代理层级结构</h4>
                    </div>
                    <div class="hierarchy-tree">
                        <div class="tree-node">
                            <div class="node-content">
                                <i class="bi bi-person-badge"></i>
                                <span>总代理 - 张总</span>
                                <span class="node-stats">下级: 5人</span>
                            </div>
                            <div class="tree-children">
                                <div class="tree-node">
                                    <div class="node-content">
                                        <i class="bi bi-person"></i>
                                        <span>一级代理 - 李代理</span>
                                        <span class="node-stats">下级: 3人</span>
                                    </div>
                                </div>
                                <div class="tree-node">
                                    <div class="node-content">
                                        <i class="bi bi-person"></i>
                                        <span>一级代理 - 王代理</span>
                                        <span class="node-stats">下级: 2人</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('层级管理', error);
        }
    }

    // 财务交易记录
    async getFinanceTransactionsContent() {
        try {
            return `
                <div class="page-header">
                    <h3>交易记录</h3>
                    <div class="page-actions">
                        <button class="btn btn-secondary">
                            <i class="bi bi-download"></i>
                            导出记录
                        </button>
                    </div>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <div class="search-filters">
                            <input type="text" placeholder="搜索交易..." class="search-input">
                            <select class="filter-select">
                                <option>全部类型</option>
                                <option>充值</option>
                                <option>消费</option>
                                <option>退款</option>
                            </select>
                            <select class="filter-select">
                                <option>全部状态</option>
                                <option>成功</option>
                                <option>失败</option>
                                <option>处理中</option>
                            </select>
                        </div>
                    </div>

                    <div class="table-container">
                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>交易号</th>
                                    <th>用户</th>
                                    <th>类型</th>
                                    <th>金额</th>
                                    <th>状态</th>
                                    <th>时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>TXN20240117001</td>
                                    <td>张三</td>
                                    <td>充值</td>
                                    <td>¥100.00</td>
                                    <td><span class="status-badge success">成功</span></td>
                                    <td>2024-01-17 10:30</td>
                                    <td>
                                        <button class="btn-icon" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                <tr>
                                    <td>TXN20240117002</td>
                                    <td>李四</td>
                                    <td>消费</td>
                                    <td>¥25.00</td>
                                    <td><span class="status-badge success">成功</span></td>
                                    <td>2024-01-17 10:25</td>
                                    <td>
                                        <button class="btn-icon" title="查看详情">
                                            <i class="bi bi-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            `;
        } catch (error) {
            return this.getErrorContent('交易记录', error);
        }
    }

    getDefaultContent(contentType) {
        return `
            <div class="page-header">
                <h3>${contentType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}</h3>
            </div>

            <div class="content-card">
                <div class="empty-state">
                    <i class="bi bi-gear" style="font-size: 48px; color: #d9d9d9;"></i>
                    <h4>功能开发中</h4>
                    <p>该功能正在开发中，敬请期待...</p>
                </div>
            </div>
        `;
    }

    loadDefaultContent() {
        this.loadContent('dashboard', document.querySelector('.menu-link[data-content="dashboard"]'));
    }

    initContentFeatures(contentType) {
        // 根据内容类型初始化特定功能
        switch(contentType) {
            case 'users-list':
                this.initUsersListFeatures();
                break;
            case 'dashboard':
                this.initDashboardFeatures();
                break;
            // 可以添加更多内容类型的初始化
        }
    }

    initUsersListFeatures() {
        // 初始化用户列表相关功能
        console.log('初始化用户列表功能');
    }

    initDashboardFeatures() {
        // 初始化仪表盘相关功能
        console.log('初始化仪表盘功能');
    }
}

// 全局函数：初始化用户分析图表
function initUserAnalyticsCharts(analytics) {
    // 用户增长趋势图
    const growthCtx = document.getElementById('user-growth-chart');
    if (growthCtx) {
        new Chart(growthCtx, {
            type: 'line',
            data: {
                labels: ['1月', '2月', '3月', '4月', '5月', '6月', '7月'],
                datasets: [{
                    label: '新增用户',
                    data: [65, 89, 80, 81, 96, 105, analytics.newUsers || 89],
                    borderColor: '#1890ff',
                    backgroundColor: 'rgba(24, 144, 255, 0.1)',
                    tension: 0.4
                }, {
                    label: '活跃用户',
                    data: [280, 320, 350, 400, 450, 500, analytics.activeUsers || 567],
                    borderColor: '#52c41a',
                    backgroundColor: 'rgba(82, 196, 26, 0.1)',
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }

    // 用户地域分布图
    const locationCtx = document.getElementById('user-location-chart');
    if (locationCtx) {
        new Chart(locationCtx, {
            type: 'doughnut',
            data: {
                labels: ['北京', '上海', '广州', '深圳', '其他'],
                datasets: [{
                    data: [30, 25, 20, 15, 10],
                    backgroundColor: [
                        '#1890ff',
                        '#52c41a',
                        '#faad14',
                        '#f5222d',
                        '#722ed1'
                    ]
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom'
                    }
                }
            }
        });
    }

    // 获取价格设置内容
    async getPriceSettingsContent() {
        try {
            console.log('📡 正在获取价格设置...');

            // 获取当前价格设置
            const response = await fetch('/api/settings/prices');
            let priceSettings = {
                aiPrice: 0.01,
                expertPrice: 50
            };

            if (response.ok) {
                const data = await response.json();
                if (data.success && data.data) {
                    priceSettings = data.data;
                }
            }

            console.log('✅ 价格设置获取成功:', priceSettings);

            return `
                <div class="page-header">
                    <h3>价格设置</h3>
                    <p>管理学术专家卡片和内容优化卡片的价格</p>
                </div>

                <div class="content-card">
                    <div class="card-header">
                        <h4>服务价格配置</h4>
                        <button class="btn btn-primary" onclick="savePriceSettings()">
                            <i class="bi bi-save"></i>
                            保存设置
                        </button>
                    </div>

                    <div class="price-settings-form">
                        <div class="price-setting-item">
                            <div class="setting-info">
                                <h5>AI内容优化卡片</h5>
                                <p>AI自动优化文档内容的价格（每千字）</p>
                            </div>
                            <div class="setting-control">
                                <div class="input-group">
                                    <span class="input-prefix">¥</span>
                                    <input type="number"
                                           id="aiPrice"
                                           value="${priceSettings.aiPrice}"
                                           step="0.01"
                                           min="0"
                                           placeholder="0.01">
                                    <span class="input-suffix">/ 千字</span>
                                </div>
                            </div>
                        </div>

                        <div class="price-setting-item">
                            <div class="setting-info">
                                <h5>学术专家卡片</h5>
                                <p>专业学术专家人工优化文档的价格（每千字）</p>
                            </div>
                            <div class="setting-control">
                                <div class="input-group">
                                    <span class="input-prefix">¥</span>
                                    <input type="number"
                                           id="expertPrice"
                                           value="${priceSettings.expertPrice}"
                                           step="0.01"
                                           min="0"
                                           placeholder="50.00">
                                    <span class="input-suffix">/ 千字</span>
                                </div>
                            </div>
                        </div>

                        <div class="price-preview">
                            <h5>价格预览</h5>
                            <div class="preview-cards">
                                <div class="preview-card">
                                    <h6>AI内容优化</h6>
                                    <div class="preview-price">¥<span id="aiPreviewPrice">${priceSettings.aiPrice}</span>/千字</div>
                                    <div class="preview-example">
                                        示例：5000字文档 = ¥<span id="aiExamplePrice">${(priceSettings.aiPrice * 5).toFixed(2)}</span>
                                    </div>
                                </div>
                                <div class="preview-card">
                                    <h6>学术专家优化</h6>
                                    <div class="preview-price">¥<span id="expertPreviewPrice">${priceSettings.expertPrice}</span>/千字</div>
                                    <div class="preview-example">
                                        示例：5000字文档 = ¥<span id="expertExamplePrice">${(priceSettings.expertPrice * 5).toFixed(2)}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <script>
                    // 实时更新价格预览
                    document.getElementById('aiPrice').addEventListener('input', function() {
                        const price = parseFloat(this.value) || 0;
                        document.getElementById('aiPreviewPrice').textContent = price.toFixed(2);
                        document.getElementById('aiExamplePrice').textContent = (price * 5).toFixed(2);
                    });

                    document.getElementById('expertPrice').addEventListener('input', function() {
                        const price = parseFloat(this.value) || 0;
                        document.getElementById('expertPreviewPrice').textContent = price.toFixed(2);
                        document.getElementById('expertExamplePrice').textContent = (price * 5).toFixed(2);
                    });
                </script>
            `;

        } catch (error) {
            console.error('❌ 获取价格设置失败:', error);
            return this.getDefaultContent('价格设置加载失败');
        }
    }
}

// 全局函数：更新用户分析
async function updateUserAnalytics() {
    const timeRange = document.getElementById('analyticsTimeRange').value;
    console.log('更新用户分析，时间范围:', timeRange);
    // 这里可以重新加载数据
}

// 全局函数：导出分析报告
function exportAnalytics() {
    console.log('导出用户分析报告');
    alert('导出功能开发中...');
}

// 全局函数：刷新增长图表
function refreshGrowthChart() {
    console.log('刷新增长图表');
    // 重新获取数据并更新图表
}


}

// 初始化函数已在HTML中调用，这里不需要重复初始化

// ==================== 价格管理功能函数 ====================

// 保存价格设置
function savePriceSettings() {
    console.log('💾 保存价格设置...');

    const aiPrice = parseFloat(document.getElementById('aiPrice').value) || 0;
    const expertPrice = parseFloat(document.getElementById('expertPrice').value) || 0;

    if (aiPrice < 0 || expertPrice < 0) {
        alert('❌ 价格不能为负数！');
        return;
    }

    const priceData = {
        aiPrice: aiPrice,
        expertPrice: expertPrice
    };

    // 显示加载状态
    const saveButton = event.target;
    const originalText = saveButton.innerHTML;
    saveButton.innerHTML = '<i class="bi bi-hourglass-split"></i> 保存中...';
    saveButton.disabled = true;

    fetch('/api/settings/prices', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('admin_token') || 'demo-token'}`
        },
        body: JSON.stringify(priceData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(`✅ 价格设置保存成功！\n\nAI内容优化：¥${aiPrice}/千字\n学术专家优化：¥${expertPrice}/千字\n\n前端页面将在下次刷新时生效。`);
            console.log('✅ 价格设置保存成功');
        } else {
            alert('❌ 保存失败: ' + (data.message || '未知错误'));
            console.error('保存失败:', data.message);
        }
    })
    .catch(error => {
        console.error('保存价格设置失败:', error);
        alert('❌ 保存失败: ' + error.message);
    })
    .finally(() => {
        // 恢复按钮状态
        saveButton.innerHTML = originalText;
        saveButton.disabled = false;
    });
}

// ==================== 用户管理功能函数 ====================

// 编辑用户
function editUser(userId) {
    console.log('✏️ 编辑用户:', userId);

    // 从表格中获取用户数据
    const userRow = document.querySelector(`button[onclick="editUser('${userId}')"]`).closest('tr');
    const cells = userRow.cells;

    const userData = {
        id: userId,
        username: cells[1].textContent,
        email: cells[2].textContent,
        status: cells[4].querySelector('.status-badge').classList.contains('active') ? 'active' : 'inactive'
    };

    console.log('📋 用户数据:', userData);
    showEditUserModal(userData);
}

// 删除用户
function deleteUser(userId) {
    console.log('🗑️ 删除用户:', userId);

    // 获取用户名用于确认对话框
    const userRow = document.querySelector(`button[onclick="deleteUser('${userId}')"]`).closest('tr');
    const username = userRow.cells[1].textContent;

    if (confirm(`确定要删除用户 "${username}" 吗？\n\n⚠️ 警告：此操作将同时删除该用户的所有相关数据（文档等），且不可撤销！`)) {
        // 显示加载状态
        const button = event.target.closest('button');
        const originalHTML = button.innerHTML;
        button.innerHTML = '<i class="bi bi-hourglass-split"></i>';
        button.disabled = true;

        // 调用真实的删除API
        fetch(`/api/users/${userId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${localStorage.getItem('admin_token') || 'demo-token'}`
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                // 删除成功，从表格中移除行
                userRow.style.transition = 'opacity 0.3s ease';
                userRow.style.opacity = '0.5';

                setTimeout(() => {
                    userRow.remove();
                    alert(`✅ 用户 "${username}" 已从数据库中永久删除！`);
                }, 300);

                console.log('✅ 用户删除成功');
            } else {
                alert('❌ 删除失败: ' + (data.message || '未知错误'));
                console.error('删除失败:', data.message);
            }
        })
        .catch(error => {
            console.error('删除用户失败:', error);
            alert('❌ 删除失败: ' + error.message);
        })
        .finally(() => {
            // 恢复按钮状态
            button.innerHTML = originalHTML;
            button.disabled = false;
        });
    }
}

// 显示添加用户模态框
function showAddUserModal() {
    console.log('显示添加用户模态框');

    const modalHTML = `
        <div id="userModal" class="modal-overlay" onclick="closeUserModal(event)">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>添加用户</h3>
                    <button class="modal-close" onclick="closeUserModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="userForm" onsubmit="submitUserForm(event)">
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" name="username" required>
                        </div>
                        <div class="form-group">
                            <label for="email">邮箱 *</label>
                            <input type="email" id="email" name="email" required>
                        </div>
                        <div class="form-group">
                            <label for="password">密码 *</label>
                            <input type="password" id="password" name="password" required>
                        </div>
                        <div class="form-group">
                            <label for="status">状态</label>
                            <select id="status" name="status">
                                <option value="active">活跃</option>
                                <option value="inactive">禁用</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                            <button type="submit" class="btn btn-primary">添加用户</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// 显示编辑用户模态框
function showEditUserModal(user) {
    console.log('显示编辑用户模态框:', user);

    const modalHTML = `
        <div id="userModal" class="modal-overlay" onclick="closeUserModal(event)">
            <div class="modal-content" onclick="event.stopPropagation()">
                <div class="modal-header">
                    <h3>编辑用户</h3>
                    <button class="modal-close" onclick="closeUserModal()">&times;</button>
                </div>
                <div class="modal-body">
                    <form id="userForm" onsubmit="submitUserForm(event, ${user.id})">
                        <div class="form-group">
                            <label for="username">用户名 *</label>
                            <input type="text" id="username" name="username" value="${user.username || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="email">邮箱 *</label>
                            <input type="email" id="email" name="email" value="${user.email || ''}" required>
                        </div>
                        <div class="form-group">
                            <label for="password">新密码 (留空则不修改)</label>
                            <input type="password" id="password" name="password">
                        </div>
                        <div class="form-group">
                            <label for="status">状态</label>
                            <select id="status" name="status">
                                <option value="active" ${user.status === 'active' ? 'selected' : ''}>活跃</option>
                                <option value="inactive" ${user.status === 'inactive' ? 'selected' : ''}>禁用</option>
                            </select>
                        </div>
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="closeUserModal()">取消</button>
                            <button type="submit" class="btn btn-primary">保存修改</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    `;

    document.body.insertAdjacentHTML('beforeend', modalHTML);
}

// 关闭用户模态框
function closeUserModal(event) {
    if (event && event.target !== event.currentTarget) return;

    const modal = document.getElementById('userModal');
    if (modal) {
        modal.remove();
    }
}

// 提交用户表单
function submitUserForm(event, userId = null) {
    event.preventDefault();

    const form = event.target;
    const formData = new FormData(form);
    const userData = {
        username: formData.get('username'),
        email: formData.get('email'),
        status: formData.get('status')
    };

    // 只有在密码不为空时才包含密码
    const password = formData.get('password');
    if (password && password.trim()) {
        userData.password = password;
    }

    const isEdit = userId !== null;

    // 显示加载状态
    const submitButton = form.querySelector('button[type="submit"]');
    const originalText = submitButton.textContent;
    submitButton.textContent = '处理中...';
    submitButton.disabled = true;

    // 调用真实的API进行添加或编辑
    const url = isEdit ? `/api/users/${userId}` : '/api/users';
    const method = isEdit ? 'PUT' : 'POST';

    fetch(url, {
        method: method,
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${localStorage.getItem('admin_token') || 'demo-token'}`
        },
        body: JSON.stringify(userData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            if (isEdit) {
                // 编辑成功：更新表格中的数据
                const userRow = document.querySelector(`button[onclick="editUser('${userId}')"]`).closest('tr');
                if (userRow) {
                    userRow.cells[1].textContent = userData.username;
                    userRow.cells[2].textContent = userData.email;

                    const statusBadge = userRow.cells[4].querySelector('.status-badge');
                    statusBadge.className = `status-badge ${userData.status === 'active' ? 'active' : 'warning'}`;
                    statusBadge.textContent = userData.status === 'active' ? '活跃' : '禁用';
                }

                alert(`✅ 用户 "${userData.username}" 更新成功！数据已保存到数据库。`);
            } else {
                // 添加成功：刷新用户列表以显示新用户
                alert(`✅ 用户 "${userData.username}" 添加成功！数据已保存到数据库。`);
                refreshUsersList(); // 刷新列表以显示新用户
            }

            closeUserModal();
            console.log('✅ 用户操作成功');
        } else {
            alert('❌ 操作失败: ' + (data.message || '未知错误'));
            console.error('操作失败:', data.message);
        }
    })
    .catch(error => {
        console.error('提交用户表单失败:', error);
        alert('❌ 操作失败: ' + error.message);
    })
    .finally(() => {
        // 重置按钮状态
        submitButton.textContent = originalText;
        submitButton.disabled = false;
    });
}

// 刷新用户列表
function refreshUsersList() {
    console.log('刷新用户列表');

    // 重新加载用户列表内容
    if (window.expandableNav) {
        window.expandableNav.loadContent('users-list');
    } else {
        // 备用方案：刷新页面
        location.reload();
    }
}

// 搜索用户
function searchUsers() {
    const searchTerm = document.getElementById('userSearch').value.toLowerCase();
    const tableRows = document.querySelectorAll('#usersTableBody tr');

    tableRows.forEach(row => {
        const username = row.cells[1].textContent.toLowerCase();
        const email = row.cells[2].textContent.toLowerCase();

        if (username.includes(searchTerm) || email.includes(searchTerm)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}

// 筛选用户
function filterUsers() {
    const statusFilter = document.getElementById('userStatusFilter').value;
    const tableRows = document.querySelectorAll('#usersTableBody tr');

    tableRows.forEach(row => {
        const statusCell = row.cells[4];
        const statusBadge = statusCell.querySelector('.status-badge');

        if (!statusFilter || statusBadge.classList.contains(statusFilter)) {
            row.style.display = '';
        } else {
            row.style.display = 'none';
        }
    });
}
