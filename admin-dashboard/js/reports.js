/**
 * 报表中心JavaScript
 * 处理报表生成、模板管理、历史记录等功能
 */

class ReportsManager {
    constructor() {
        this.templates = [];
        this.reportHistory = [];
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadTemplates();
        this.loadReportHistory();
        this.initializeMetrics();
    }
    
    bindEvents() {
        // 创建模板按钮
        document.getElementById('createTemplateBtn')?.addEventListener('click', () => {
            this.showTemplateModal();
        });
        
        // 保存模板按钮
        document.getElementById('saveTemplateBtn')?.addEventListener('click', () => {
            this.saveTemplate();
        });
        
        // 生成报表按钮
        document.getElementById('generateReportBtn')?.addEventListener('click', () => {
            this.generateCustomReport();
        });
        
        // 刷新历史按钮
        document.getElementById('refreshHistoryBtn')?.addEventListener('click', () => {
            this.loadReportHistory();
        });
        
        // 清空历史按钮
        document.getElementById('clearHistoryBtn')?.addEventListener('click', () => {
            this.clearHistory();
        });
        
        // 报表类型变化
        document.getElementById('reportType')?.addEventListener('change', (e) => {
            this.updateMetricsForType(e.target.value);
        });
        
        // 时间范围变化
        document.getElementById('reportPeriod')?.addEventListener('change', (e) => {
            const customRange = document.getElementById('customDateRange');
            if (e.target.value === 'custom') {
                customRange.style.display = 'block';
            } else {
                customRange.style.display = 'none';
            }
        });
    }
    
    async generateQuickReport(type) {
        try {
            this.showLoading(`正在生成${this.getReportTypeName(type)}...`);
            
            // 模拟报表生成
            await this.simulateReportGeneration();
            
            const reportData = this.generateMockReportData(type);
            const reportId = this.generateReportId();
            
            // 添加到历史记录
            const report = {
                id: reportId,
                name: this.getReportTypeName(type),
                type: type,
                generatedAt: new Date(),
                status: 'completed',
                fileSize: this.getRandomFileSize(),
                downloadUrl: '#'
            };
            
            this.reportHistory.unshift(report);
            this.updateReportHistoryTable();
            
            // 模拟下载
            this.downloadReport(report, reportData);
            
            this.showNotification(`${this.getReportTypeName(type)}生成成功`, 'success');
            
        } catch (error) {
            console.error('生成快速报表失败:', error);
            this.showNotification('报表生成失败', 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    showTemplateModal(template = null) {
        const modal = new bootstrap.Modal(document.getElementById('templateModal'));
        
        if (template) {
            document.getElementById('templateModalTitle').textContent = '编辑报表模板';
            document.getElementById('templateName').value = template.name;
            document.getElementById('templateDescription').value = template.description;
            document.getElementById('templateType').value = template.type;
            document.getElementById('templatePeriod').value = template.period;
        } else {
            document.getElementById('templateModalTitle').textContent = '创建报表模板';
            document.getElementById('templateForm').reset();
        }
        
        modal.show();
    }
    
    saveTemplate() {
        const name = document.getElementById('templateName').value;
        const description = document.getElementById('templateDescription').value;
        const type = document.getElementById('templateType').value;
        const period = document.getElementById('templatePeriod').value;
        
        if (!name || !type) {
            this.showNotification('请填写必填字段', 'warning');
            return;
        }
        
        const template = {
            id: this.generateTemplateId(),
            name,
            description,
            type,
            period,
            createdAt: new Date()
        };
        
        this.templates.push(template);
        this.renderTemplates();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('templateModal'));
        modal.hide();
        
        this.showNotification('模板保存成功', 'success');
    }
    
    showCustomReportModal() {
        const modal = new bootstrap.Modal(document.getElementById('customReportModal'));
        modal.show();
    }
    
    async generateCustomReport() {
        try {
            const reportName = document.getElementById('reportName').value;
            const reportType = document.getElementById('reportType').value;
            const reportPeriod = document.getElementById('reportPeriod').value;
            const reportFormat = document.getElementById('reportFormat').value;
            
            if (!reportName || !reportType) {
                this.showNotification('请填写必填字段', 'warning');
                return;
            }
            
            this.showLoading('正在生成自定义报表...');
            
            // 模拟报表生成
            await this.simulateReportGeneration();
            
            const reportData = this.generateMockReportData(reportType);
            const reportId = this.generateReportId();
            
            // 添加到历史记录
            const report = {
                id: reportId,
                name: reportName,
                type: reportType,
                generatedAt: new Date(),
                status: 'completed',
                fileSize: this.getRandomFileSize(),
                format: reportFormat,
                downloadUrl: '#'
            };
            
            this.reportHistory.unshift(report);
            this.updateReportHistoryTable();
            
            // 模拟下载
            this.downloadReport(report, reportData);
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('customReportModal'));
            modal.hide();
            
            this.showNotification('自定义报表生成成功', 'success');
            
        } catch (error) {
            console.error('生成自定义报表失败:', error);
            this.showNotification('报表生成失败', 'error');
        } finally {
            this.hideLoading();
        }
    }
    
    loadTemplates() {
        // 加载默认模板
        this.templates = [
            {
                id: 'template-001',
                name: '用户增长报表',
                description: '分析用户注册和活跃度趋势',
                type: 'user_report',
                period: '30d',
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
            },
            {
                id: 'template-002',
                name: '收入分析报表',
                description: '详细的收入来源和趋势分析',
                type: 'revenue_report',
                period: '30d',
                createdAt: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
            },
            {
                id: 'template-003',
                name: '代理商绩效报表',
                description: '代理商销售业绩和佣金统计',
                type: 'agent_report',
                period: '30d',
                createdAt: new Date(Date.now() - 21 * 24 * 60 * 60 * 1000)
            }
        ];
        
        this.renderTemplates();
    }
    
    renderTemplates() {
        const container = document.getElementById('reportTemplates');
        if (!container) return;
        
        container.innerHTML = this.templates.map(template => `
            <div class="col-md-4 mb-3">
                <div class="template-card">
                    <div class="template-header">
                        <h6>${template.name}</h6>
                        <div class="template-actions">
                            <button class="btn btn-sm btn-outline-primary" onclick="reportsManager.useTemplate('${template.id}')" title="使用模板">
                                <i class="bi bi-play"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-secondary" onclick="reportsManager.editTemplate('${template.id}')" title="编辑">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-sm btn-outline-danger" onclick="reportsManager.deleteTemplate('${template.id}')" title="删除">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                    <div class="template-body">
                        <p class="template-description">${template.description}</p>
                        <div class="template-meta">
                            <span class="badge bg-secondary">${this.getReportTypeName(template.type)}</span>
                            <span class="badge bg-info">${this.getPeriodName(template.period)}</span>
                        </div>
                        <small class="text-muted">创建于 ${template.createdAt.toLocaleDateString()}</small>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    loadReportHistory() {
        // 加载示例历史记录
        this.reportHistory = [
            {
                id: 'report-001',
                name: '用户增长日报',
                type: 'daily',
                generatedAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                status: 'completed',
                fileSize: '2.3 MB',
                format: 'excel'
            },
            {
                id: 'report-002',
                name: '收入分析周报',
                type: 'weekly',
                generatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                status: 'completed',
                fileSize: '5.7 MB',
                format: 'pdf'
            },
            {
                id: 'report-003',
                name: '代理商绩效月报',
                type: 'monthly',
                generatedAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                status: 'completed',
                fileSize: '8.1 MB',
                format: 'excel'
            },
            {
                id: 'report-004',
                name: '营销效果分析',
                type: 'custom',
                generatedAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                status: 'failed',
                fileSize: '-',
                format: 'csv'
            }
        ];
        
        this.updateReportHistoryTable();
    }
    
    updateReportHistoryTable() {
        const tbody = document.getElementById('reportHistoryTable');
        if (!tbody) return;
        
        tbody.innerHTML = this.reportHistory.map(report => `
            <tr>
                <td>
                    <div class="d-flex align-items-center">
                        <i class="bi bi-file-earmark-text me-2"></i>
                        ${report.name}
                    </div>
                </td>
                <td>
                    <span class="badge bg-secondary">${this.getReportTypeName(report.type)}</span>
                </td>
                <td>${report.generatedAt.toLocaleString()}</td>
                <td>
                    <span class="badge bg-${report.status === 'completed' ? 'success' : report.status === 'failed' ? 'danger' : 'warning'}">
                        ${this.getStatusName(report.status)}
                    </span>
                </td>
                <td>${report.fileSize}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${report.status === 'completed' ? `
                            <button class="btn btn-outline-primary" onclick="reportsManager.downloadReportById('${report.id}')" title="下载">
                                <i class="bi bi-download"></i>
                            </button>
                            <button class="btn btn-outline-info" onclick="reportsManager.previewReport('${report.id}')" title="预览">
                                <i class="bi bi-eye"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="reportsManager.deleteReport('${report.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    useTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;
        
        // 使用模板生成报表
        this.generateQuickReport(template.type);
    }
    
    editTemplate(templateId) {
        const template = this.templates.find(t => t.id === templateId);
        if (!template) return;
        
        this.showTemplateModal(template);
    }
    
    deleteTemplate(templateId) {
        if (!confirm('确定要删除此模板吗？')) return;
        
        this.templates = this.templates.filter(t => t.id !== templateId);
        this.renderTemplates();
        this.showNotification('模板删除成功', 'success');
    }
    
    deleteReport(reportId) {
        if (!confirm('确定要删除此报表吗？')) return;
        
        this.reportHistory = this.reportHistory.filter(r => r.id !== reportId);
        this.updateReportHistoryTable();
        this.showNotification('报表删除成功', 'success');
    }
    
    clearHistory() {
        if (!confirm('确定要清空所有报表历史吗？此操作不可恢复。')) return;
        
        this.reportHistory = [];
        this.updateReportHistoryTable();
        this.showNotification('历史记录已清空', 'success');
    }
    
    downloadReportById(reportId) {
        const report = this.reportHistory.find(r => r.id === reportId);
        if (!report) return;
        
        const reportData = this.generateMockReportData(report.type);
        this.downloadReport(report, reportData);
    }
    
    downloadReport(report, data) {
        // 模拟文件下载
        const content = this.formatReportData(data, report.format || 'excel');
        const blob = new Blob([content], { 
            type: this.getMimeType(report.format || 'excel') 
        });
        
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${report.name}_${new Date().toISOString().split('T')[0]}.${this.getFileExtension(report.format || 'excel')}`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }
    
    previewReport(reportId) {
        const report = this.reportHistory.find(r => r.id === reportId);
        if (!report) return;
        
        // 简单的预览功能
        alert(`报表预览：${report.name}\n生成时间：${report.generatedAt.toLocaleString()}\n文件大小：${report.fileSize}`);
    }
    
    initializeMetrics() {
        this.metrics = {
            user_report: [
                { id: 'total_users', name: '总用户数', checked: true },
                { id: 'new_users', name: '新增用户', checked: true },
                { id: 'active_users', name: '活跃用户', checked: true },
                { id: 'user_retention', name: '用户留存率', checked: false }
            ],
            revenue_report: [
                { id: 'total_revenue', name: '总收入', checked: true },
                { id: 'monthly_revenue', name: '月收入', checked: true },
                { id: 'payment_methods', name: '支付方式分布', checked: false },
                { id: 'refund_rate', name: '退款率', checked: false }
            ],
            agent_report: [
                { id: 'total_agents', name: '代理商总数', checked: true },
                { id: 'agent_sales', name: '销售业绩', checked: true },
                { id: 'commission_paid', name: '已付佣金', checked: true },
                { id: 'top_performers', name: '优秀代理商', checked: false }
            ],
            marketing_report: [
                { id: 'campaign_performance', name: '活动效果', checked: true },
                { id: 'coupon_usage', name: '优惠券使用', checked: true },
                { id: 'conversion_rate', name: '转化率', checked: false },
                { id: 'roi', name: '投资回报率', checked: false }
            ]
        };
    }
    
    updateMetricsForType(type) {
        const container = document.getElementById('reportMetrics');
        if (!container || !this.metrics[type]) return;
        
        container.innerHTML = this.metrics[type].map(metric => `
            <div class="col-md-6">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="${metric.id}" 
                           ${metric.checked ? 'checked' : ''} id="metric_${metric.id}">
                    <label class="form-check-label" for="metric_${metric.id}">
                        ${metric.name}
                    </label>
                </div>
            </div>
        `).join('');
    }
    
    // 辅助方法
    generateReportId() {
        return 'report-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    generateTemplateId() {
        return 'template-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    getReportTypeName(type) {
        const names = {
            'daily': '日报',
            'weekly': '周报',
            'monthly': '月报',
            'custom': '自定义报表',
            'user_report': '用户报表',
            'revenue_report': '收入报表',
            'agent_report': '代理商报表',
            'marketing_report': '营销报表'
        };
        return names[type] || type;
    }
    
    getPeriodName(period) {
        const names = {
            '7d': '7天',
            '30d': '30天',
            '90d': '90天'
        };
        return names[period] || period;
    }
    
    getStatusName(status) {
        const names = {
            'completed': '已完成',
            'failed': '失败',
            'processing': '处理中'
        };
        return names[status] || status;
    }
    
    getRandomFileSize() {
        const sizes = ['1.2 MB', '2.5 MB', '3.8 MB', '5.1 MB', '7.3 MB', '9.6 MB'];
        return sizes[Math.floor(Math.random() * sizes.length)];
    }
    
    getMimeType(format) {
        const types = {
            'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'csv': 'text/csv',
            'pdf': 'application/pdf'
        };
        return types[format] || 'application/octet-stream';
    }
    
    getFileExtension(format) {
        const extensions = {
            'excel': 'xlsx',
            'csv': 'csv',
            'pdf': 'pdf'
        };
        return extensions[format] || 'xlsx';
    }
    
    generateMockReportData(type) {
        // 生成模拟报表数据
        return {
            type,
            generatedAt: new Date(),
            data: {
                summary: {
                    totalRecords: Math.floor(Math.random() * 10000) + 1000,
                    period: '30天',
                    generatedBy: '系统管理员'
                },
                details: Array.from({ length: 50 }, (_, i) => ({
                    id: i + 1,
                    date: new Date(Date.now() - i * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
                    value: Math.floor(Math.random() * 1000) + 100
                }))
            }
        };
    }
    
    formatReportData(data, format) {
        switch (format) {
            case 'csv':
                return this.formatAsCSV(data);
            case 'excel':
                return this.formatAsExcel(data);
            case 'pdf':
                return this.formatAsPDF(data);
            default:
                return JSON.stringify(data, null, 2);
        }
    }
    
    formatAsCSV(data) {
        let csv = '日期,数值\n';
        data.data.details.forEach(item => {
            csv += `${item.date},${item.value}\n`;
        });
        return csv;
    }
    
    formatAsExcel(data) {
        // 简化的Excel格式模拟
        return this.formatAsCSV(data);
    }
    
    formatAsPDF(data) {
        // 简化的PDF格式模拟
        return `报表数据\n生成时间: ${data.generatedAt}\n总记录数: ${data.data.summary.totalRecords}`;
    }
    
    async simulateReportGeneration() {
        // 模拟报表生成延迟
        return new Promise(resolve => {
            setTimeout(resolve, 2000 + Math.random() * 3000);
        });
    }
    
    showLoading(message = '处理中...') {
        // 显示加载状态
        const loadingEl = document.createElement('div');
        loadingEl.id = 'reportLoading';
        loadingEl.className = 'position-fixed top-50 start-50 translate-middle';
        loadingEl.style.zIndex = '9999';
        loadingEl.innerHTML = `
            <div class="card">
                <div class="card-body text-center">
                    <div class="spinner-border text-primary mb-3" role="status"></div>
                    <div>${message}</div>
                </div>
            </div>
        `;
        document.body.appendChild(loadingEl);
    }
    
    hideLoading() {
        const loadingEl = document.getElementById('reportLoading');
        if (loadingEl) {
            loadingEl.remove();
        }
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.reportsManager = new ReportsManager();
});

// 导出
window.ReportsManager = ReportsManager;
