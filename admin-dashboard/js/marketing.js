/**
 * 营销工具页面JavaScript
 * 处理优惠券管理、推广活动、邀请返利、积分系统等功能
 */

class MarketingManager {
    constructor() {
        this.currentTab = 'coupons';
        this.currentPage = 1;
        this.pageSize = 20;
        this.charts = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadMarketingOverview();
        this.loadCoupons();
        this.loadCampaigns();
        this.loadReferralSettings();
        this.loadPointsSettings();
        this.initCharts();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('.tab-nav .tab-item').forEach(tab => {
            tab.addEventListener('click', (e) => {
                e.preventDefault();
                const targetTab = e.target.getAttribute('href').replace('#', '');
                this.switchTab(targetTab);
            });
        });
        
        // 创建按钮
        document.getElementById('createCouponBtn')?.addEventListener('click', () => {
            this.showCreateCouponModal();
        });
        
        document.getElementById('createCampaignBtn')?.addEventListener('click', () => {
            this.showCreateCampaignModal();
        });
        
        // 表单提交
        document.getElementById('couponForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCoupon();
        });
        
        document.getElementById('campaignForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCampaign();
        });
        
        document.getElementById('referralSettingsForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveReferralSettings();
        });
        
        document.getElementById('pointsSettingsForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePointsSettings();
        });
        
        // 搜索和筛选
        document.getElementById('couponSearchBtn')?.addEventListener('click', () => {
            this.loadCoupons();
        });
        
        document.getElementById('campaignSearchBtn')?.addEventListener('click', () => {
            this.loadCampaigns();
        });
        
        // 状态筛选
        document.getElementById('couponStatusFilter')?.addEventListener('change', () => {
            this.loadCoupons();
        });
        
        document.getElementById('campaignStatusFilter')?.addEventListener('change', () => {
            this.loadCampaigns();
        });
    }
    
    switchTab(tabName) {
        // 更新选项卡状态
        document.querySelectorAll('.tab-nav .tab-item').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[href="#${tabName}"]`).classList.add('active');
        
        // 更新内容区域
        document.querySelectorAll('.tab-pane').forEach(pane => {
            pane.classList.remove('active');
        });
        document.getElementById(tabName).classList.add('active');
        
        this.currentTab = tabName;
        this.handleTabSwitch();
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'coupons':
                this.loadCoupons();
                break;
            case 'campaigns':
                this.loadCampaigns();
                break;
            case 'referrals':
                this.loadReferralSettings();
                break;
            case 'points':
                this.loadPointsSettings();
                break;
            case 'analytics':
                this.loadMarketingAnalytics();
                break;
        }
    }
    
    async loadMarketingOverview() {
        try {
            const response = await fetch('/api/marketing/overview');
            const result = await response.json();
            
            if (result.success) {
                this.updateOverviewCards(result.data);
            }
        } catch (error) {
            console.error('加载营销概览失败:', error);
        }
    }
    
    updateOverviewCards(data) {
        document.getElementById('activeCoupons').textContent = data.activeCoupons || 0;
        document.getElementById('newCoupons').textContent = data.newCoupons || 0;
        document.getElementById('invitedUsers').textContent = data.invitedUsers || 0;
        document.getElementById('inviteConversion').textContent = `${data.inviteConversion || 0}%`;
        document.getElementById('totalPoints').textContent = data.totalPoints || 0;
        document.getElementById('monthlyPoints').textContent = data.monthlyPoints || 0;
        document.getElementById('marketingROI').textContent = `${data.marketingROI || 0}%`;
        document.getElementById('roiGrowth').textContent = `+${data.roiGrowth || 0}%`;
    }
    
    async loadCoupons() {
        try {
            const search = document.getElementById('couponSearch')?.value || '';
            const status = document.getElementById('couponStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/marketing/coupons?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderCouponsTable(result.data);
                this.renderPagination(result.pagination, 'couponsPagination');
            }
        } catch (error) {
            console.error('加载优惠券列表失败:', error);
        }
    }
    
    renderCouponsTable(coupons) {
        const tbody = document.querySelector('#coupons table tbody');
        if (!tbody) return;
        
        tbody.innerHTML = coupons.map(coupon => `
            <tr>
                <td><code>${coupon.code}</code></td>
                <td>${coupon.name}</td>
                <td>
                    <span class="badge bg-${this.getDiscountTypeColor(coupon.discountType)}">
                        ${this.getDiscountTypeText(coupon.discountType)}
                    </span>
                </td>
                <td>
                    ${coupon.discountType === 'percentage' ? 
                        `${coupon.discountValue}%` : 
                        `¥${coupon.discountValue}`}
                </td>
                <td>${this.getUsageLimitText(coupon)}</td>
                <td>
                    ${new Date(coupon.startDate).toLocaleDateString()} 至 
                    ${new Date(coupon.endDate).toLocaleDateString()}
                </td>
                <td>
                    <span class="status ${coupon.status}">${this.getStatusText(coupon.status)}</span>
                </td>
                <td>${coupon.usedCount || 0}/${coupon.usageLimit || '∞'}</td>
                <td>
                    <div class="table-actions">
                        <button class="btn-icon" onclick="marketingManager.viewCoupon('${coupon._id}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="marketingManager.editCoupon('${coupon._id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn-icon" onclick="marketingManager.toggleCouponStatus('${coupon._id}')" title="切换状态">
                            <i class="bi bi-${coupon.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadCampaigns() {
        try {
            const search = document.getElementById('campaignSearch')?.value || '';
            const status = document.getElementById('campaignStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/marketing/campaigns?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderCampaignsTable(result.data);
                this.renderPagination(result.pagination, 'campaignsPagination');
            }
        } catch (error) {
            console.error('加载推广活动失败:', error);
        }
    }
    
    renderCampaignsTable(campaigns) {
        const tbody = document.querySelector('#campaigns table tbody');
        if (!tbody) return;
        
        tbody.innerHTML = campaigns.map(campaign => `
            <tr>
                <td>${campaign.name}</td>
                <td>
                    <span class="badge bg-${this.getCampaignTypeColor(campaign.type)}">
                        ${this.getCampaignTypeText(campaign.type)}
                    </span>
                </td>
                <td>¥${campaign.budget || 0}</td>
                <td>
                    ${new Date(campaign.startDate).toLocaleDateString()} 至 
                    ${new Date(campaign.endDate).toLocaleDateString()}
                </td>
                <td>
                    <span class="status ${campaign.status}">${this.getStatusText(campaign.status)}</span>
                </td>
                <td>${campaign.participants || 0}</td>
                <td>¥${campaign.spent || 0}</td>
                <td>
                    <div class="table-actions">
                        <button class="btn-icon" onclick="marketingManager.viewCampaign('${campaign._id}')" title="查看详情">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn-icon" onclick="marketingManager.editCampaign('${campaign._id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn-icon" onclick="marketingManager.toggleCampaignStatus('${campaign._id}')" title="切换状态">
                            <i class="bi bi-${campaign.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadReferralSettings() {
        try {
            const response = await fetch('/api/marketing/referral-settings');
            const result = await response.json();
            
            if (result.success) {
                this.populateReferralForm(result.data);
            }
        } catch (error) {
            console.error('加载邀请返利设置失败:', error);
        }
    }
    
    populateReferralForm(settings) {
        document.getElementById('referralEnabled').checked = settings.enabled || false;
        document.getElementById('inviterReward').value = settings.inviterReward || 0;
        document.getElementById('inviteeReward').value = settings.inviteeReward || 0;
        document.getElementById('maxReferrals').value = settings.maxReferrals || 0;
        document.getElementById('referralExpiry').value = settings.referralExpiry || 30;
    }
    
    async saveReferralSettings() {
        try {
            const formData = {
                enabled: document.getElementById('referralEnabled').checked,
                inviterReward: parseFloat(document.getElementById('inviterReward').value),
                inviteeReward: parseFloat(document.getElementById('inviteeReward').value),
                maxReferrals: parseInt(document.getElementById('maxReferrals').value),
                referralExpiry: parseInt(document.getElementById('referralExpiry').value)
            };
            
            const response = await fetch('/api/marketing/referral-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('邀请返利设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存邀请返利设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadPointsSettings() {
        try {
            const response = await fetch('/api/marketing/points-settings');
            const result = await response.json();
            
            if (result.success) {
                this.populatePointsForm(result.data);
            }
        } catch (error) {
            console.error('加载积分设置失败:', error);
        }
    }
    
    populatePointsForm(settings) {
        document.getElementById('pointsEnabled').checked = settings.enabled || false;
        document.getElementById('signInPoints').value = settings.signInPoints || 0;
        document.getElementById('purchasePointsRate').value = settings.purchasePointsRate || 0;
        document.getElementById('pointsExchangeRate').value = settings.pointsExchangeRate || 0;
        document.getElementById('minRedemption').value = settings.minRedemption || 0;
    }
    
    async savePointsSettings() {
        try {
            const formData = {
                enabled: document.getElementById('pointsEnabled').checked,
                signInPoints: parseInt(document.getElementById('signInPoints').value),
                purchasePointsRate: parseFloat(document.getElementById('purchasePointsRate').value),
                pointsExchangeRate: parseFloat(document.getElementById('pointsExchangeRate').value),
                minRedemption: parseInt(document.getElementById('minRedemption').value)
            };
            
            const response = await fetch('/api/marketing/points-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('积分设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存积分设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    initCharts() {
        // 初始化图表
        this.charts.roi = null;
        this.charts.conversion = null;
    }
    
    getDiscountTypeColor(type) {
        const colors = {
            'percentage': 'success',
            'fixed': 'primary',
            'free_shipping': 'info'
        };
        return colors[type] || 'secondary';
    }
    
    getDiscountTypeText(type) {
        const texts = {
            'percentage': '折扣',
            'fixed': '固定金额',
            'free_shipping': '免运费'
        };
        return texts[type] || type;
    }
    
    getCampaignTypeColor(type) {
        const colors = {
            'promotion': 'primary',
            'discount': 'success',
            'referral': 'warning',
            'points': 'info'
        };
        return colors[type] || 'secondary';
    }
    
    getCampaignTypeText(type) {
        const texts = {
            'promotion': '推广活动',
            'discount': '折扣活动',
            'referral': '邀请活动',
            'points': '积分活动'
        };
        return texts[type] || type;
    }
    
    getStatusText(status) {
        const texts = {
            'active': '进行中',
            'pending': '未开始',
            'expired': '已过期',
            'paused': '已暂停',
            'completed': '已完成'
        };
        return texts[status] || status;
    }
    
    getUsageLimitText(coupon) {
        if (coupon.userLimit && coupon.usageLimit) {
            return `每人${coupon.userLimit}次，总计${coupon.usageLimit}次`;
        } else if (coupon.userLimit) {
            return `每人限用${coupon.userLimit}次`;
        } else if (coupon.usageLimit) {
            return `总计${coupon.usageLimit}次`;
        } else {
            return '无限制';
        }
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination, containerId) {
        const paginationEl = document.getElementById(containerId);
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="marketingManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="marketingManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="marketingManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        if (this.currentTab === 'coupons') {
            this.loadCoupons();
        } else if (this.currentTab === 'campaigns') {
            this.loadCampaigns();
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.marketingManager = new MarketingManager();
});

// 导出
window.MarketingManager = MarketingManager;
