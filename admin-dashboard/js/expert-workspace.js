// 专家工作台JavaScript

let currentExpert = null;
let currentWorkOrder = null;
let workTimer = null;
let totalWorkTime = 0;

document.addEventListener('DOMContentLoaded', function() {
    initExpertWorkspace();
    loadExpertInfo();
    loadWorkOrders();
    startWorkTimer();
});

function initExpertWorkspace() {
    // 侧边栏折叠/展开
    const menuToggle = document.querySelector('.menu-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    
    if (menuToggle && adminLayout) {
        menuToggle.addEventListener('click', () => {
            adminLayout.classList.toggle('sidebar-collapsed');
        });
    }
    
    // 用户下拉菜单
    const userBtn = document.querySelector('.user-btn');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    if (userBtn && dropdownMenu) {
        document.addEventListener('click', (e) => {
            if (!userBtn.contains(e.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
        
        userBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });
    }
}

async function loadExpertInfo() {
    try {
        const response = await fetch('/ad/api/expert/profile');
        const result = await response.json();
        
        if (result.success) {
            currentExpert = result.data;
            updateExpertInfo(currentExpert);
        } else {
            showError('获取专家信息失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取专家信息失败:', error);
        showError('获取专家信息失败，请稍后重试');
    }
}

function updateExpertInfo(expert) {
    document.getElementById('expertName').textContent = expert.realName || '专家';
    document.getElementById('currentOrders').textContent = expert.currentOrders || 0;
    document.getElementById('rating').textContent = (expert.averageRating || 5.0).toFixed(1);
    
    // 更新状态
    const statusElement = document.getElementById('currentStatus');
    statusElement.textContent = getStatusText(expert.status);
    statusElement.className = `badge ${getStatusBadgeClass(expert.status)}`;
}

async function loadWorkOrders() {
    try {
        const response = await fetch('/ad/api/expert/workorders');
        const result = await response.json();
        
        if (result.success) {
            renderWorkOrders(result.data);
        } else {
            showError('获取工单列表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取工单列表失败:', error);
        showError('获取工单列表失败，请稍后重试');
    }
}

function renderWorkOrders(workOrders) {
    const container = document.getElementById('workOrdersList');
    
    if (!workOrders || workOrders.length === 0) {
        container.innerHTML = `
            <div class="text-center py-4">
                <i class="bi bi-clipboard-x fs-1 text-muted"></i>
                <p class="text-muted mt-2">暂无待处理工单</p>
            </div>
        `;
        return;
    }
    
    container.innerHTML = workOrders.map(workOrder => `
        <div class="work-item">
            <div class="row">
                <div class="col-md-8">
                    <h6 class="mb-2">
                        <span class="badge bg-primary me-2">#${workOrder.id}</span>
                        ${workOrder.title}
                    </h6>
                    <p class="text-muted mb-2">${workOrder.description || '无描述'}</p>
                    <div class="d-flex gap-2 mb-2">
                        <span class="badge ${getStatusBadgeClass(workOrder.status)} status-badge">
                            ${getStatusText(workOrder.status)}
                        </span>
                        <span class="badge ${getPriorityBadgeClass(workOrder.priority)} status-badge">
                            ${getPriorityText(workOrder.priority)}
                        </span>
                    </div>
                    <small class="text-muted">
                        <i class="bi bi-person me-1"></i>客户: ${workOrder.user?.username || '未知'}
                        <i class="bi bi-clock ms-3 me-1"></i>截止: ${workOrder.deadline ? formatDateTime(workOrder.deadline) : '未设置'}
                    </small>
                </div>
                <div class="col-md-4 text-end">
                    <div class="mb-2">
                        <strong class="text-success">¥${parseFloat(workOrder.order?.amount || 0).toFixed(2)}</strong>
                    </div>
                    <div class="btn-group-vertical w-100">
                        <button class="btn btn-primary btn-sm" onclick="startWork(${workOrder.id})">
                            <i class="bi bi-play-circle"></i> 开始处理
                        </button>
                        <button class="btn btn-outline-secondary btn-sm" onclick="viewWorkOrderDetails(${workOrder.id})">
                            <i class="bi bi-eye"></i> 查看详情
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `).join('');
}

async function startWork(workOrderId) {
    try {
        // 获取工单详情
        const response = await fetch(`/ad/api/workorders/${workOrderId}`);
        const result = await response.json();
        
        if (result.success) {
            currentWorkOrder = result.data;
            showWorkOrderModal(currentWorkOrder);
            
            // 更新工单状态为进行中
            await updateWorkOrderStatus(workOrderId, 'in_progress');
        } else {
            showError('获取工单详情失败: ' + result.message);
        }
    } catch (error) {
        console.error('开始工作失败:', error);
        showError('开始工作失败，请稍后重试');
    }
}

function showWorkOrderModal(workOrder) {
    // 填充原始内容
    document.getElementById('originalContent').innerHTML = workOrder.originalContent || '无原始内容';
    
    // 清空优化内容
    document.getElementById('optimizedContent').value = workOrder.optimizedContent || '';
    
    // 清空工作日志
    document.getElementById('workLog').value = '';
    document.getElementById('hoursSpent').value = '';
    
    // 显示模态框
    const modal = new bootstrap.Modal(document.getElementById('workOrderModal'));
    modal.show();
}

async function saveProgress() {
    if (!currentWorkOrder) return;
    
    try {
        const data = {
            optimizedContent: document.getElementById('optimizedContent').value,
            workLog: document.getElementById('workLog').value,
            hoursSpent: parseFloat(document.getElementById('hoursSpent').value) || 0
        };
        
        const response = await fetch(`/ad/api/workorders/${currentWorkOrder.id}/progress`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess('进度保存成功');
        } else {
            showError('保存进度失败: ' + result.message);
        }
    } catch (error) {
        console.error('保存进度失败:', error);
        showError('保存进度失败，请稍后重试');
    }
}

async function submitWork() {
    if (!currentWorkOrder) return;
    
    const optimizedContent = document.getElementById('optimizedContent').value.trim();
    if (!optimizedContent) {
        showWarning('请输入优化后的内容');
        return;
    }
    
    try {
        const data = {
            optimizedContent: optimizedContent,
            workLog: document.getElementById('workLog').value,
            hoursSpent: parseFloat(document.getElementById('hoursSpent').value) || 0
        };
        
        const response = await fetch(`/ad/api/workorders/${currentWorkOrder.id}/submit`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess('工单提交成功');
            bootstrap.Modal.getInstance(document.getElementById('workOrderModal')).hide();
            loadWorkOrders(); // 重新加载工单列表
            loadExpertInfo(); // 更新专家信息
        } else {
            showError('提交工单失败: ' + result.message);
        }
    } catch (error) {
        console.error('提交工单失败:', error);
        showError('提交工单失败，请稍后重试');
    }
}

async function updateStatus(status) {
    try {
        const response = await fetch('/ad/api/expert/status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
        });
        
        const result = await response.json();
        
        if (result.success) {
            showSuccess(`状态已更新为: ${getStatusText(status)}`);
            loadExpertInfo(); // 重新加载专家信息
        } else {
            showError('更新状态失败: ' + result.message);
        }
    } catch (error) {
        console.error('更新状态失败:', error);
        showError('更新状态失败，请稍后重试');
    }
}

async function updateWorkOrderStatus(workOrderId, status) {
    try {
        const response = await fetch(`/ad/api/workorders/${workOrderId}/status`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ status })
        });
        
        return await response.json();
    } catch (error) {
        console.error('更新工单状态失败:', error);
        return { success: false };
    }
}

function refreshWorkOrders() {
    loadWorkOrders();
    showInfo('工单列表已刷新');
}

function startWorkTimer() {
    workTimer = setInterval(() => {
        totalWorkTime += 1;
        updateWorkTimeDisplay();
    }, 1000);
}

function updateWorkTimeDisplay() {
    const hours = Math.floor(totalWorkTime / 3600);
    const minutes = Math.floor((totalWorkTime % 3600) / 60);
    const seconds = totalWorkTime % 60;
    
    const timeString = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    document.getElementById('currentTime').textContent = timeString;
}

// 辅助函数
function getStatusText(status) {
    const texts = {
        'pending': '待分配',
        'assigned': '已分配',
        'in_progress': '进行中',
        'review': '审核中',
        'completed': '已完成',
        'cancelled': '已取消',
        'active': '在线',
        'busy': '忙碌',
        'offline': '离线',
        'suspended': '暂停'
    };
    return texts[status] || status;
}

function getStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'assigned': 'bg-info',
        'in_progress': 'bg-primary',
        'review': 'bg-secondary',
        'completed': 'bg-success',
        'cancelled': 'bg-danger',
        'active': 'bg-success',
        'busy': 'bg-warning',
        'offline': 'bg-secondary',
        'suspended': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getPriorityText(priority) {
    const texts = {
        'low': '低',
        'normal': '普通',
        'high': '高',
        'urgent': '紧急'
    };
    return texts[priority] || priority;
}

function getPriorityBadgeClass(priority) {
    const classes = {
        'low': 'bg-light text-dark',
        'normal': 'bg-secondary',
        'high': 'bg-warning',
        'urgent': 'bg-danger'
    };
    return classes[priority] || 'bg-secondary';
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

// 页面导航函数
function showMyWorkOrders() {
    loadWorkOrders();
}

function showWorkHistory() {
    // TODO: 实现工作历史页面
    showInfo('工作历史功能开发中...');
}

function showProfile() {
    // TODO: 实现个人资料页面
    showInfo('个人资料功能开发中...');
}

function showSettings() {
    // TODO: 实现设置页面
    showInfo('设置功能开发中...');
}

function viewWorkOrderDetails(workOrderId) {
    // TODO: 实现工单详情查看
    showInfo('工单详情功能开发中...');
}
