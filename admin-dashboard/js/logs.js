/**
 * 系统日志查看器JavaScript
 * 处理实时日志显示、过滤、搜索等功能
 */

class LogViewer {
    constructor() {
        this.logs = [];
        this.filteredLogs = [];
        this.autoRefresh = true;
        this.autoScroll = true;
        this.refreshInterval = null;
        this.maxLogs = 1000; // 最大日志条数
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.startAutoRefresh();
        this.loadInitialLogs();
    }
    
    bindEvents() {
        // 搜索按钮
        document.getElementById('searchLogsBtn')?.addEventListener('click', () => {
            this.filterLogs();
        });
        
        // 清除过滤器
        document.getElementById('clearFiltersBtn')?.addEventListener('click', () => {
            this.clearFilters();
        });
        
        // 刷新日志
        document.getElementById('refreshLogsBtn')?.addEventListener('click', () => {
            this.refreshLogs();
        });
        
        // 导出日志
        document.getElementById('exportLogsBtn')?.addEventListener('click', () => {
            this.exportLogs();
        });
        
        // 清空日志
        document.getElementById('clearLogsBtn')?.addEventListener('click', () => {
            this.clearLogs();
        });
        
        // 自动刷新开关
        document.getElementById('autoRefreshSwitch')?.addEventListener('change', (e) => {
            this.autoRefresh = e.target.checked;
            if (this.autoRefresh) {
                this.startAutoRefresh();
            } else {
                this.stopAutoRefresh();
            }
        });
        
        // 自动滚动开关
        document.getElementById('scrollToBottomSwitch')?.addEventListener('change', (e) => {
            this.autoScroll = e.target.checked;
        });
        
        // 过滤器变化
        ['logLevelFilter', 'logSourceFilter', 'timeRangeFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.filterLogs();
            });
        });
        
        // 搜索输入框回车
        document.getElementById('logSearchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.filterLogs();
            }
        });
        
        // 复制日志详情
        document.getElementById('copyLogDetailBtn')?.addEventListener('click', () => {
            this.copyLogDetail();
        });
    }
    
    loadInitialLogs() {
        // 生成初始日志数据
        this.generateMockLogs(50);
        this.filterLogs();
    }
    
    generateMockLogs(count) {
        const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
        const sources = ['API', 'AUTH', 'DB', 'SYSTEM'];
        const users = ['admin', 'user123', 'system', 'guest'];
        
        const messages = {
            'ERROR': [
                'Database connection failed',
                'Authentication failed for user',
                'API request timeout',
                'File not found error',
                'Memory allocation failed'
            ],
            'WARN': [
                'High memory usage detected',
                'Slow query detected',
                'Rate limit approaching',
                'Cache miss rate high',
                'Deprecated API usage'
            ],
            'INFO': [
                'User logged in successfully',
                'API request completed',
                'Database backup completed',
                'System maintenance started',
                'Configuration updated'
            ],
            'DEBUG': [
                'Processing user request',
                'Cache hit for key',
                'Database query executed',
                'Function called with parameters',
                'Variable value changed'
            ]
        };
        
        for (let i = 0; i < count; i++) {
            const level = levels[Math.floor(Math.random() * levels.length)];
            const source = sources[Math.floor(Math.random() * sources.length)];
            const user = users[Math.floor(Math.random() * users.length)];
            const messageList = messages[level];
            const message = messageList[Math.floor(Math.random() * messageList.length)];
            
            const log = {
                id: this.generateLogId(),
                timestamp: new Date(Date.now() - Math.random() * 24 * 60 * 60 * 1000),
                level,
                source,
                user,
                message,
                details: this.generateLogDetails(level, source, message),
                stackTrace: level === 'ERROR' ? this.generateStackTrace() : null
            };
            
            this.logs.push(log);
        }
        
        // 按时间排序
        this.logs.sort((a, b) => b.timestamp - a.timestamp);
        
        // 限制日志数量
        if (this.logs.length > this.maxLogs) {
            this.logs = this.logs.slice(0, this.maxLogs);
        }
    }
    
    generateLogDetails(level, source, message) {
        const details = {
            requestId: this.generateRequestId(),
            sessionId: this.generateSessionId(),
            ip: this.generateIP(),
            userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        };
        
        if (source === 'API') {
            details.endpoint = '/api/v1/' + ['users', 'agents', 'finance', 'content'][Math.floor(Math.random() * 4)];
            details.method = ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)];
            details.responseTime = Math.floor(Math.random() * 1000) + 'ms';
        }
        
        if (source === 'DB') {
            details.query = 'SELECT * FROM ' + ['users', 'agents', 'transactions'][Math.floor(Math.random() * 3)];
            details.executionTime = Math.floor(Math.random() * 500) + 'ms';
        }
        
        return details;
    }
    
    generateStackTrace() {
        return `Error: ${Math.random() > 0.5 ? 'NullPointerException' : 'SQLException'}
    at com.writerpro.service.UserService.getUser(UserService.java:${Math.floor(Math.random() * 100) + 50})
    at com.writerpro.controller.UserController.getUserById(UserController.java:${Math.floor(Math.random() * 50) + 20})
    at com.writerpro.web.filter.AuthFilter.doFilter(AuthFilter.java:${Math.floor(Math.random() * 30) + 10})
    at org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:119)`;
    }
    
    filterLogs() {
        const levelFilter = document.getElementById('logLevelFilter').value;
        const sourceFilter = document.getElementById('logSourceFilter').value;
        const timeRangeFilter = document.getElementById('timeRangeFilter').value;
        const searchText = document.getElementById('logSearchInput').value.toLowerCase();
        
        // 计算时间范围
        const now = new Date();
        let timeThreshold;
        switch (timeRangeFilter) {
            case '1h':
                timeThreshold = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '6h':
                timeThreshold = new Date(now.getTime() - 6 * 60 * 60 * 1000);
                break;
            case '24h':
                timeThreshold = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                timeThreshold = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            default:
                timeThreshold = new Date(0);
        }
        
        this.filteredLogs = this.logs.filter(log => {
            // 级别过滤
            if (levelFilter && log.level !== levelFilter) return false;
            
            // 来源过滤
            if (sourceFilter && log.source !== sourceFilter) return false;
            
            // 时间过滤
            if (log.timestamp < timeThreshold) return false;
            
            // 搜索过滤
            if (searchText && !log.message.toLowerCase().includes(searchText)) return false;
            
            return true;
        });
        
        this.renderLogs();
        this.updateLogStats();
    }
    
    renderLogs() {
        const viewer = document.getElementById('logViewer');
        if (!viewer) return;
        
        viewer.innerHTML = this.filteredLogs.map(log => `
            <div class="log-entry ${log.level.toLowerCase()}" onclick="logViewer.showLogDetail('${log.id}')">
                <div class="d-flex justify-content-between align-items-start">
                    <div class="flex-grow-1">
                        <span class="log-timestamp">${log.timestamp.toLocaleString()}</span>
                        <span class="log-level ${log.level}">${log.level}</span>
                        <span class="badge bg-secondary ms-2">${log.source}</span>
                        <span class="text-muted ms-2">[${log.user}]</span>
                    </div>
                    <button class="btn btn-sm btn-outline-secondary" onclick="event.stopPropagation(); logViewer.showLogDetail('${log.id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </div>
                <div class="mt-1">
                    ${log.message}
                </div>
                ${log.details.endpoint ? `<small class="text-muted">${log.details.method} ${log.details.endpoint}</small>` : ''}
            </div>
        `).join('');
        
        // 更新总计数
        document.getElementById('totalLogCount').textContent = this.filteredLogs.length;
        
        // 自动滚动到底部
        if (this.autoScroll) {
            viewer.scrollTop = viewer.scrollHeight;
        }
    }
    
    updateLogStats() {
        const stats = {
            ERROR: 0,
            WARN: 0,
            INFO: 0,
            DEBUG: 0
        };
        
        this.filteredLogs.forEach(log => {
            stats[log.level]++;
        });
        
        document.getElementById('errorCount').textContent = stats.ERROR;
        document.getElementById('warningCount').textContent = stats.WARN;
        document.getElementById('infoCount').textContent = stats.INFO;
        document.getElementById('debugCount').textContent = stats.DEBUG;
    }
    
    showLogDetail(logId) {
        const log = this.logs.find(l => l.id === logId);
        if (!log) return;
        
        document.getElementById('logDetailTime').textContent = log.timestamp.toLocaleString();
        document.getElementById('logDetailLevel').innerHTML = `<span class="log-level ${log.level}">${log.level}</span>`;
        document.getElementById('logDetailSource').textContent = log.source;
        document.getElementById('logDetailUser').textContent = log.user;
        document.getElementById('logDetailMessage').textContent = log.message;
        
        // 显示堆栈跟踪（如果有）
        const stackTraceDiv = document.getElementById('logDetailStackTrace');
        if (log.stackTrace) {
            document.getElementById('logDetailStack').textContent = log.stackTrace;
            stackTraceDiv.style.display = 'block';
        } else {
            stackTraceDiv.style.display = 'none';
        }
        
        // 存储当前日志用于复制
        this.currentLogDetail = log;
        
        const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
        modal.show();
    }
    
    copyLogDetail() {
        if (!this.currentLogDetail) return;
        
        const log = this.currentLogDetail;
        const text = `时间: ${log.timestamp.toLocaleString()}
级别: ${log.level}
来源: ${log.source}
用户: ${log.user}
消息: ${log.message}
${log.stackTrace ? `\n堆栈跟踪:\n${log.stackTrace}` : ''}`;
        
        navigator.clipboard.writeText(text).then(() => {
            this.showNotification('日志详情已复制到剪贴板', 'success');
        }).catch(() => {
            this.showNotification('复制失败', 'error');
        });
    }
    
    clearFilters() {
        document.getElementById('logLevelFilter').value = '';
        document.getElementById('logSourceFilter').value = '';
        document.getElementById('timeRangeFilter').value = '24h';
        document.getElementById('logSearchInput').value = '';
        this.filterLogs();
    }
    
    refreshLogs() {
        // 生成新的日志条目
        this.generateMockLogs(10);
        this.filterLogs();
        this.showNotification('日志已刷新', 'success');
    }
    
    clearLogs() {
        if (!confirm('确定要清空所有日志吗？此操作不可恢复。')) return;
        
        this.logs = [];
        this.filteredLogs = [];
        this.renderLogs();
        this.updateLogStats();
        this.showNotification('日志已清空', 'success');
    }
    
    exportLogs() {
        if (this.filteredLogs.length === 0) {
            this.showNotification('没有可导出的日志', 'warning');
            return;
        }
        
        const csvContent = this.convertLogsToCSV(this.filteredLogs);
        const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `system_logs_${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        
        this.showNotification('日志导出成功', 'success');
    }
    
    convertLogsToCSV(logs) {
        const headers = ['时间', '级别', '来源', '用户', '消息'];
        let csv = headers.join(',') + '\n';
        
        logs.forEach(log => {
            const row = [
                log.timestamp.toISOString(),
                log.level,
                log.source,
                log.user,
                `"${log.message.replace(/"/g, '""')}"`
            ];
            csv += row.join(',') + '\n';
        });
        
        return csv;
    }
    
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }
        
        this.refreshInterval = setInterval(() => {
            if (this.autoRefresh) {
                // 生成1-3个新日志
                const newLogCount = Math.floor(Math.random() * 3) + 1;
                this.generateMockLogs(newLogCount);
                this.filterLogs();
            }
        }, 5000); // 每5秒刷新一次
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
    
    // 辅助方法
    generateLogId() {
        return 'log-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    generateRequestId() {
        return 'req-' + Math.random().toString(36).substr(2, 12);
    }
    
    generateSessionId() {
        return 'sess-' + Math.random().toString(36).substr(2, 16);
    }
    
    generateIP() {
        return `${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}.${Math.floor(Math.random() * 255)}`;
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.logViewer = new LogViewer();
});

// 导出
window.LogViewer = LogViewer;
