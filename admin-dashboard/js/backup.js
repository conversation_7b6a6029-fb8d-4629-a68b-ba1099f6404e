/**
 * 备份恢复管理JavaScript
 * 处理数据备份、恢复、计划任务等功能
 */

class BackupManager {
    constructor() {
        this.backups = [];
        this.schedules = [];
        this.currentBackup = null;
        this.storageLimit = 100; // GB
        this.storageUsed = 65; // GB
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadBackups();
        this.loadSchedules();
        this.updateStorageInfo();
        this.startStatusUpdates();
    }
    
    bindEvents() {
        // 快速备份按钮
        document.getElementById('fullBackupBtn')?.addEventListener('click', () => {
            this.startQuickBackup('full');
        });
        
        document.getElementById('incrementalBackupBtn')?.addEventListener('click', () => {
            this.startQuickBackup('incremental');
        });
        
        // 恢复按钮
        document.getElementById('restoreBtn')?.addEventListener('click', () => {
            this.showRestoreModal();
        });
        
        // 计划按钮
        document.getElementById('scheduleBtn')?.addEventListener('click', () => {
            this.showScheduleModal();
        });
        
        // 清理按钮
        document.getElementById('cleanupBtn')?.addEventListener('click', () => {
            this.cleanupOldBackups();
        });
        
        // 刷新按钮
        document.getElementById('refreshBackupsBtn')?.addEventListener('click', () => {
            this.loadBackups();
        });
        
        // 添加计划按钮
        document.getElementById('addScheduleBtn')?.addEventListener('click', () => {
            this.showScheduleModal();
        });
        
        // 模态框按钮
        document.getElementById('startBackupBtn')?.addEventListener('click', () => {
            this.startCustomBackup();
        });
        
        document.getElementById('startRestoreBtn')?.addEventListener('click', () => {
            this.startRestore();
        });
        
        document.getElementById('saveScheduleBtn')?.addEventListener('click', () => {
            this.saveSchedule();
        });
        
        // 过滤器
        document.getElementById('backupFilter')?.addEventListener('change', () => {
            this.filterBackups();
        });
    }
    
    loadBackups() {
        // 生成示例备份数据
        this.generateMockBackups();
        this.filterBackups();
        this.updateBackupStats();
    }
    
    generateMockBackups() {
        const types = ['full', 'incremental', 'differential'];
        const statuses = ['success', 'failed', 'running'];
        
        this.backups = [
            {
                id: 'backup-001',
                name: '完整备份-20241210',
                type: 'full',
                status: 'success',
                size: '15.2 GB',
                sizeBytes: 15200000000,
                createdAt: new Date(Date.now() - 2 * 60 * 60 * 1000),
                duration: '45分钟',
                description: '每日自动完整备份',
                includes: ['database', 'files', 'config'],
                compression: 'medium',
                progress: 100,
                location: '/backup/full/backup-20241210.tar.gz'
            },
            {
                id: 'backup-002',
                name: '增量备份-20241210-12',
                type: 'incremental',
                status: 'running',
                size: '2.1 GB',
                sizeBytes: 2100000000,
                createdAt: new Date(Date.now() - 30 * 60 * 1000),
                duration: '进行中',
                description: '12点增量备份',
                includes: ['database', 'files'],
                compression: 'medium',
                progress: 75,
                location: '/backup/incremental/backup-20241210-12.tar.gz'
            },
            {
                id: 'backup-003',
                name: '完整备份-20241209',
                type: 'full',
                status: 'success',
                size: '14.8 GB',
                sizeBytes: 14800000000,
                createdAt: new Date(Date.now() - 26 * 60 * 60 * 1000),
                duration: '42分钟',
                description: '每日自动完整备份',
                includes: ['database', 'files', 'config'],
                compression: 'medium',
                progress: 100,
                location: '/backup/full/backup-20241209.tar.gz'
            },
            {
                id: 'backup-004',
                name: '增量备份-20241209-18',
                type: 'incremental',
                status: 'failed',
                size: '0 GB',
                sizeBytes: 0,
                createdAt: new Date(Date.now() - 42 * 60 * 60 * 1000),
                duration: '失败',
                description: '18点增量备份',
                includes: ['database', 'files'],
                compression: 'medium',
                progress: 0,
                location: '',
                error: '数据库连接超时'
            },
            {
                id: 'backup-005',
                name: '差异备份-20241208',
                type: 'differential',
                status: 'success',
                size: '8.5 GB',
                sizeBytes: 8500000000,
                createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                duration: '28分钟',
                description: '周末差异备份',
                includes: ['database', 'files'],
                compression: 'high',
                progress: 100,
                location: '/backup/differential/backup-20241208.tar.gz'
            }
        ];
    }
    
    loadSchedules() {
        // 生成示例计划数据
        this.schedules = [
            {
                id: 'schedule-001',
                name: '每日完整备份',
                type: 'full',
                frequency: 'daily',
                time: '02:00',
                enabled: true,
                retentionDays: 30,
                lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
                nextRun: new Date(Date.now() + 22 * 60 * 60 * 1000),
                successCount: 29,
                failureCount: 1
            },
            {
                id: 'schedule-002',
                name: '每6小时增量备份',
                type: 'incremental',
                frequency: 'every_6_hours',
                time: '00:00',
                enabled: true,
                retentionDays: 7,
                lastRun: new Date(Date.now() - 30 * 60 * 1000),
                nextRun: new Date(Date.now() + 5.5 * 60 * 60 * 1000),
                successCount: 120,
                failureCount: 3
            },
            {
                id: 'schedule-003',
                name: '周末差异备份',
                type: 'differential',
                frequency: 'weekly',
                time: '01:00',
                enabled: false,
                retentionDays: 60,
                lastRun: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
                nextRun: new Date(Date.now() + 4 * 24 * 60 * 60 * 1000),
                successCount: 12,
                failureCount: 0
            }
        ];
        
        this.renderSchedules();
    }
    
    filterBackups() {
        const filter = document.getElementById('backupFilter').value;
        
        let filteredBackups = this.backups;
        if (filter) {
            filteredBackups = this.backups.filter(backup => backup.type === filter);
        }
        
        this.renderBackups(filteredBackups);
    }
    
    renderBackups(backups) {
        const container = document.getElementById('backupsList');
        if (!container) return;
        
        if (backups.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-archive display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">没有找到备份</h5>
                    <p class="text-muted">尝试调整筛选条件或创建新备份</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = backups.map(backup => `
            <div class="backup-card ${backup.status}">
                <div class="d-flex align-items-start">
                    <div class="backup-type-icon ${backup.type}">
                        <i class="bi bi-${this.getBackupIcon(backup.type)}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-2">
                            <div>
                                <h6 class="mb-1">${backup.name}</h6>
                                <p class="text-muted mb-1">${backup.description}</p>
                            </div>
                            <div class="text-end">
                                <span class="badge bg-${this.getStatusColor(backup.status)}">${this.getStatusText(backup.status)}</span>
                                <div class="mt-1">
                                    <small class="text-muted">${backup.size}</small>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row mb-2">
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-clock"></i> ${backup.createdAt.toLocaleString()}
                                </small>
                            </div>
                            <div class="col-md-6">
                                <small class="text-muted">
                                    <i class="bi bi-stopwatch"></i> 耗时: ${backup.duration}
                                </small>
                            </div>
                        </div>
                        
                        ${backup.status === 'running' ? `
                            <div class="mb-2">
                                <div class="d-flex justify-content-between align-items-center mb-1">
                                    <small class="text-muted">备份进度</small>
                                    <small class="text-muted">${backup.progress}%</small>
                                </div>
                                <div class="progress backup-progress">
                                    <div class="progress-bar" style="width: ${backup.progress}%"></div>
                                </div>
                            </div>
                        ` : ''}
                        
                        ${backup.error ? `
                            <div class="alert alert-danger alert-sm mb-2">
                                <i class="bi bi-exclamation-triangle"></i> ${backup.error}
                            </div>
                        ` : ''}
                        
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex gap-1">
                                ${backup.includes.map(item => 
                                    `<span class="badge bg-secondary">${this.getIncludeText(item)}</span>`
                                ).join('')}
                            </div>
                            <div class="d-flex gap-1">
                                ${backup.status === 'success' ? `
                                    <button class="btn btn-sm btn-outline-primary" onclick="backupManager.downloadBackup('${backup.id}')" title="下载">
                                        <i class="bi bi-download"></i>
                                    </button>
                                    <button class="btn btn-sm btn-outline-success" onclick="backupManager.restoreFromBackup('${backup.id}')" title="恢复">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                ` : ''}
                                ${backup.status === 'running' ? `
                                    <button class="btn btn-sm btn-outline-warning" onclick="backupManager.cancelBackup('${backup.id}')" title="取消">
                                        <i class="bi bi-x"></i>
                                    </button>
                                ` : ''}
                                <button class="btn btn-sm btn-outline-danger" onclick="backupManager.deleteBackup('${backup.id}')" title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    renderSchedules() {
        const container = document.getElementById('schedulesList');
        if (!container) return;
        
        if (this.schedules.length === 0) {
            container.innerHTML = `
                <div class="text-center py-3">
                    <i class="bi bi-calendar-x text-muted"></i>
                    <p class="text-muted mt-2">暂无备份计划</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.schedules.map(schedule => `
            <div class="schedule-item ${schedule.enabled ? 'active' : ''}">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <h6 class="mb-0">${schedule.name}</h6>
                    <div class="form-check form-switch">
                        <input class="form-check-input" type="checkbox" ${schedule.enabled ? 'checked' : ''} 
                               onchange="backupManager.toggleSchedule('${schedule.id}')">
                    </div>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="bi bi-arrow-repeat"></i> ${this.getFrequencyText(schedule.frequency)} ${schedule.time}
                    </small>
                </div>
                
                <div class="mb-2">
                    <small class="text-muted">
                        <i class="bi bi-clock"></i> 下次执行: ${schedule.nextRun.toLocaleString()}
                    </small>
                </div>
                
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="badge bg-${this.getTypeColor(schedule.type)}">${this.getTypeText(schedule.type)}</span>
                    </div>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-primary" onclick="backupManager.editSchedule('${schedule.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="backupManager.deleteSchedule('${schedule.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
                
                <div class="mt-2">
                    <small class="text-success">成功: ${schedule.successCount}</small>
                    <small class="text-danger ms-2">失败: ${schedule.failureCount}</small>
                </div>
            </div>
        `).join('');
    }
    
    updateBackupStats() {
        const total = this.backups.length;
        const successful = this.backups.filter(b => b.status === 'success').length;
        const totalSizeGB = this.backups.reduce((sum, b) => sum + (b.sizeBytes / 1000000000), 0);
        const lastBackup = this.backups.length > 0 ? this.backups[0].createdAt : null;
        
        document.getElementById('totalBackups').textContent = total;
        document.getElementById('successfulBackups').textContent = successful;
        document.getElementById('totalSize').textContent = totalSizeGB.toFixed(1) + ' GB';
        document.getElementById('lastBackup').textContent = lastBackup ? 
            this.formatRelativeTime(lastBackup) : '无';
    }
    
    updateStorageInfo() {
        const usagePercent = (this.storageUsed / this.storageLimit) * 100;
        
        document.getElementById('storageUsed').textContent = this.storageUsed + ' GB';
        document.getElementById('storageIndicator').style.left = usagePercent + '%';
        
        // 更新颜色
        const indicator = document.getElementById('storageIndicator');
        if (usagePercent > 90) {
            indicator.style.backgroundColor = '#dc3545';
        } else if (usagePercent > 70) {
            indicator.style.backgroundColor = '#ffc107';
        } else {
            indicator.style.backgroundColor = '#198754';
        }
    }
    
    startQuickBackup(type) {
        const backupName = `${this.getTypeText(type)}-${new Date().toISOString().split('T')[0]}`;
        
        const backup = {
            id: this.generateBackupId(),
            name: backupName,
            type: type,
            status: 'running',
            size: '0 GB',
            sizeBytes: 0,
            createdAt: new Date(),
            duration: '进行中',
            description: `快速${this.getTypeText(type)}`,
            includes: ['database', 'files'],
            compression: 'medium',
            progress: 0,
            location: ''
        };
        
        this.backups.unshift(backup);
        this.filterBackups();
        this.updateBackupStats();
        
        this.showNotification(`${this.getTypeText(type)}已开始`, 'info');
        
        // 模拟备份进度
        this.simulateBackupProgress(backup.id);
    }
    
    simulateBackupProgress(backupId) {
        const backup = this.backups.find(b => b.id === backupId);
        if (!backup) return;
        
        const progressInterval = setInterval(() => {
            backup.progress += Math.random() * 15;
            backup.sizeBytes += Math.random() * 500000000; // 增加大小
            backup.size = (backup.sizeBytes / 1000000000).toFixed(1) + ' GB';
            
            if (backup.progress >= 100) {
                backup.progress = 100;
                backup.status = Math.random() > 0.1 ? 'success' : 'failed';
                backup.duration = Math.floor(Math.random() * 30 + 15) + '分钟';
                
                if (backup.status === 'failed') {
                    backup.error = '备份过程中发生错误';
                    backup.size = '0 GB';
                    backup.sizeBytes = 0;
                } else {
                    backup.location = `/backup/${backup.type}/backup-${Date.now()}.tar.gz`;
                    this.storageUsed += backup.sizeBytes / 1000000000;
                    this.updateStorageInfo();
                }
                
                clearInterval(progressInterval);
                this.showNotification(`备份${backup.status === 'success' ? '成功' : '失败'}`, 
                                    backup.status === 'success' ? 'success' : 'error');
            }
            
            this.filterBackups();
        }, 1000);
    }
    
    showRestoreModal() {
        // 填充可恢复的备份列表
        const select = document.getElementById('restoreBackup');
        const successfulBackups = this.backups.filter(b => b.status === 'success');
        
        select.innerHTML = '<option value="">请选择要恢复的备份</option>' +
            successfulBackups.map(backup => 
                `<option value="${backup.id}">${backup.name} (${backup.size})</option>`
            ).join('');
        
        const modal = new bootstrap.Modal(document.getElementById('restoreModal'));
        modal.show();
    }
    
    showScheduleModal() {
        document.getElementById('scheduleForm').reset();
        const modal = new bootstrap.Modal(document.getElementById('scheduleModal'));
        modal.show();
    }
    
    startRestore() {
        const backupId = document.getElementById('restoreBackup').value;
        const confirmRestore = document.getElementById('confirmRestore').checked;
        
        if (!backupId || !confirmRestore) {
            this.showNotification('请选择备份并确认恢复操作', 'warning');
            return;
        }
        
        const backup = this.backups.find(b => b.id === backupId);
        if (!backup) return;
        
        // 模拟恢复过程
        this.showNotification('数据恢复已开始，请勿关闭页面', 'info');
        
        setTimeout(() => {
            if (Math.random() > 0.1) {
                this.showNotification('数据恢复成功', 'success');
            } else {
                this.showNotification('数据恢复失败', 'error');
            }
        }, 3000);
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('restoreModal'));
        modal.hide();
    }
    
    saveSchedule() {
        const scheduleData = {
            id: this.generateScheduleId(),
            name: document.getElementById('scheduleName').value,
            type: document.getElementById('scheduleBackupType').value,
            frequency: document.getElementById('scheduleFrequency').value,
            time: document.getElementById('scheduleTime').value,
            enabled: document.getElementById('scheduleEnabled').checked,
            retentionDays: parseInt(document.getElementById('retentionDays').value),
            lastRun: null,
            nextRun: this.calculateNextRun(document.getElementById('scheduleFrequency').value, document.getElementById('scheduleTime').value),
            successCount: 0,
            failureCount: 0
        };
        
        if (!scheduleData.name) {
            this.showNotification('请填写计划名称', 'warning');
            return;
        }
        
        this.schedules.push(scheduleData);
        this.renderSchedules();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleModal'));
        modal.hide();
        
        this.showNotification('备份计划保存成功', 'success');
    }
    
    cleanupOldBackups() {
        if (!confirm('确定要清理旧备份吗？这将删除30天前的备份文件。')) return;
        
        const cutoffDate = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
        const oldBackups = this.backups.filter(b => b.createdAt < cutoffDate);
        
        if (oldBackups.length === 0) {
            this.showNotification('没有需要清理的旧备份', 'info');
            return;
        }
        
        // 计算释放的空间
        const freedSpace = oldBackups.reduce((sum, b) => sum + (b.sizeBytes / 1000000000), 0);
        
        // 删除旧备份
        this.backups = this.backups.filter(b => b.createdAt >= cutoffDate);
        this.storageUsed -= freedSpace;
        
        this.filterBackups();
        this.updateBackupStats();
        this.updateStorageInfo();
        
        this.showNotification(`已清理 ${oldBackups.length} 个旧备份，释放 ${freedSpace.toFixed(1)} GB 空间`, 'success');
    }
    
    deleteBackup(backupId) {
        if (!confirm('确定要删除此备份吗？')) return;
        
        const backup = this.backups.find(b => b.id === backupId);
        if (backup) {
            this.storageUsed -= backup.sizeBytes / 1000000000;
            this.backups = this.backups.filter(b => b.id !== backupId);
            this.filterBackups();
            this.updateBackupStats();
            this.updateStorageInfo();
            this.showNotification('备份删除成功', 'success');
        }
    }
    
    toggleSchedule(scheduleId) {
        const schedule = this.schedules.find(s => s.id === scheduleId);
        if (schedule) {
            schedule.enabled = !schedule.enabled;
            this.renderSchedules();
            this.showNotification(`计划已${schedule.enabled ? '启用' : '禁用'}`, 'success');
        }
    }
    
    startStatusUpdates() {
        // 模拟状态更新
        setInterval(() => {
            const runningBackups = this.backups.filter(b => b.status === 'running');
            runningBackups.forEach(backup => {
                if (Math.random() > 0.95) { // 5%概率更新进度
                    backup.progress = Math.min(100, backup.progress + Math.random() * 10);
                    if (backup.progress >= 100) {
                        backup.status = 'success';
                        backup.duration = Math.floor(Math.random() * 30 + 15) + '分钟';
                    }
                }
            });
            
            if (runningBackups.length > 0) {
                this.filterBackups();
            }
        }, 5000);
    }
    
    // 辅助方法
    generateBackupId() {
        return 'backup-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    generateScheduleId() {
        return 'schedule-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    getBackupIcon(type) {
        const icons = {
            'full': 'archive-fill',
            'incremental': 'plus-circle',
            'differential': 'arrow-up-circle'
        };
        return icons[type] || 'archive';
    }
    
    getStatusText(status) {
        const texts = {
            'success': '成功',
            'failed': '失败',
            'running': '进行中'
        };
        return texts[status] || status;
    }
    
    getStatusColor(status) {
        const colors = {
            'success': 'success',
            'failed': 'danger',
            'running': 'info'
        };
        return colors[status] || 'secondary';
    }
    
    getTypeText(type) {
        const texts = {
            'full': '完整备份',
            'incremental': '增量备份',
            'differential': '差异备份'
        };
        return texts[type] || type;
    }
    
    getTypeColor(type) {
        const colors = {
            'full': 'primary',
            'incremental': 'success',
            'differential': 'warning'
        };
        return colors[type] || 'secondary';
    }
    
    getIncludeText(include) {
        const texts = {
            'database': '数据库',
            'files': '文件',
            'config': '配置',
            'logs': '日志'
        };
        return texts[include] || include;
    }
    
    getFrequencyText(frequency) {
        const texts = {
            'daily': '每日',
            'weekly': '每周',
            'monthly': '每月',
            'every_6_hours': '每6小时'
        };
        return texts[frequency] || frequency;
    }
    
    calculateNextRun(frequency, time) {
        const now = new Date();
        const [hours, minutes] = time.split(':').map(Number);
        
        let nextRun = new Date();
        nextRun.setHours(hours, minutes, 0, 0);
        
        if (nextRun <= now) {
            nextRun.setDate(nextRun.getDate() + 1);
        }
        
        return nextRun;
    }
    
    formatRelativeTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        return Math.floor(diff / 86400000) + '天前';
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.backupManager = new BackupManager();
});

// 导出
window.BackupManager = BackupManager;
