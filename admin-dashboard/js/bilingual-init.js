/**
 * 双语系统初始化脚本
 * 确保页面加载时正确应用语言设置
 */

(function() {
    'use strict';
    
    // 双语系统配置
    const BILINGUAL_CONFIG = {
        defaultLanguage: 'zh',
        supportedLanguages: ['zh', 'en'],
        fallbackLanguage: 'zh',
        storageKey: 'admin_language',
        autoDetect: true,
        loadRemoteTranslations: true
    };
    
    // 语言检测和初始化
    function initBilingualSystem() {
        // 1. 检测并设置初始语言
        const initialLanguage = detectInitialLanguage();
        
        // 2. 设置HTML属性
        setHtmlAttributes(initialLanguage);
        
        // 3. 预加载语言包
        preloadLanguagePacks(initialLanguage);
        
        // 4. 初始化i18n系统
        initializeI18n(initialLanguage);
        
        // 5. 设置语言变化监听器
        setupLanguageChangeListeners();
        
        console.log(`Bilingual system initialized with language: ${initialLanguage}`);
    }
    
    // 检测初始语言
    function detectInitialLanguage() {
        // 1. 从URL参数获取语言
        const urlParams = new URLSearchParams(window.location.search);
        const urlLang = urlParams.get('lang');
        if (urlLang && BILINGUAL_CONFIG.supportedLanguages.includes(urlLang)) {
            localStorage.setItem(BILINGUAL_CONFIG.storageKey, urlLang);
            return urlLang;
        }
        
        // 2. 从localStorage获取保存的语言
        const savedLang = localStorage.getItem(BILINGUAL_CONFIG.storageKey);
        if (savedLang && BILINGUAL_CONFIG.supportedLanguages.includes(savedLang)) {
            return savedLang;
        }
        
        // 3. 自动检测浏览器语言
        if (BILINGUAL_CONFIG.autoDetect) {
            const browserLang = detectBrowserLanguage();
            if (browserLang) {
                localStorage.setItem(BILINGUAL_CONFIG.storageKey, browserLang);
                return browserLang;
            }
        }
        
        // 4. 使用默认语言
        return BILINGUAL_CONFIG.defaultLanguage;
    }
    
    // 检测浏览器语言
    function detectBrowserLanguage() {
        const browserLangs = navigator.languages || [navigator.language || navigator.userLanguage];
        
        for (const browserLang of browserLangs) {
            // 精确匹配
            if (BILINGUAL_CONFIG.supportedLanguages.includes(browserLang)) {
                return browserLang;
            }
            
            // 语言代码匹配（如 zh-CN -> zh）
            const langCode = browserLang.split('-')[0];
            if (BILINGUAL_CONFIG.supportedLanguages.includes(langCode)) {
                return langCode;
            }
        }
        
        return null;
    }
    
    // 设置HTML属性
    function setHtmlAttributes(language) {
        const html = document.documentElement;
        
        // 设置语言属性
        html.setAttribute('lang', language);
        
        // 设置文本方向（如果需要支持RTL语言）
        const rtlLanguages = ['ar', 'he', 'fa', 'ur'];
        if (rtlLanguages.includes(language)) {
            html.setAttribute('dir', 'rtl');
            document.body.classList.add('rtl');
        } else {
            html.setAttribute('dir', 'ltr');
            document.body.classList.remove('rtl');
        }
        
        // 添加语言类名
        document.body.classList.remove(...BILINGUAL_CONFIG.supportedLanguages.map(lang => `lang-${lang}`));
        document.body.classList.add(`lang-${language}`);
    }
    
    // 预加载语言包
    function preloadLanguagePacks(initialLanguage) {
        if (!BILINGUAL_CONFIG.loadRemoteTranslations) {
            return;
        }
        
        // 预加载当前语言包
        loadLanguagePack(initialLanguage);
        
        // 预加载其他语言包（可选）
        BILINGUAL_CONFIG.supportedLanguages.forEach(lang => {
            if (lang !== initialLanguage) {
                setTimeout(() => loadLanguagePack(lang), 1000);
            }
        });
    }
    
    // 加载语言包
    async function loadLanguagePack(language) {
        try {
            const response = await fetch(`locales/${language}.json`);
            if (response.ok) {
                const translations = await response.json();
                
                // 存储到全局变量或缓存中
                window.translations = window.translations || {};
                window.translations[language] = translations;
                
                return translations;
            }
        } catch (error) {
            console.warn(`Failed to load language pack for ${language}:`, error);
        }
        
        return null;
    }
    
    // 初始化i18n系统
    function initializeI18n(language) {
        // 等待i18n脚本加载完成
        if (window.i18n) {
            i18n.initLanguageSettings();
            i18n.changeLanguage(language);
        } else {
            // 如果i18n还未加载，等待加载完成
            document.addEventListener('DOMContentLoaded', () => {
                if (window.i18n) {
                    i18n.initLanguageSettings();
                    i18n.changeLanguage(language);
                }
            });
        }
    }
    
    // 设置语言变化监听器
    function setupLanguageChangeListeners() {
        // 监听语言变化事件
        document.addEventListener('languageChanged', (e) => {
            const newLanguage = e.detail.language;
            setHtmlAttributes(newLanguage);
            updatePageTitle(newLanguage);
            updateMetaTags(newLanguage);
        });
        
        // 监听存储变化（多标签页同步）
        window.addEventListener('storage', (e) => {
            if (e.key === BILINGUAL_CONFIG.storageKey && e.newValue) {
                const newLanguage = e.newValue;
                if (BILINGUAL_CONFIG.supportedLanguages.includes(newLanguage)) {
                    if (window.i18n) {
                        i18n.changeLanguage(newLanguage);
                    }
                }
            }
        });
    }
    
    // 更新页面标题
    function updatePageTitle(language) {
        const titleElement = document.querySelector('title');
        if (titleElement && window.i18n) {
            // 根据当前页面更新标题
            const currentPage = getCurrentPageKey();
            if (currentPage) {
                const newTitle = i18n.t(`${currentPage}.title`);
                if (newTitle && newTitle !== `${currentPage}.title`) {
                    titleElement.textContent = newTitle + ' - WriterPro管理后台';
                }
            }
        }
    }
    
    // 更新meta标签
    function updateMetaTags(language) {
        // 更新description meta标签
        const descriptionMeta = document.querySelector('meta[name="description"]');
        if (descriptionMeta && window.i18n) {
            const currentPage = getCurrentPageKey();
            if (currentPage) {
                const newDescription = i18n.t(`${currentPage}.description`);
                if (newDescription && newDescription !== `${currentPage}.description`) {
                    descriptionMeta.setAttribute('content', newDescription);
                }
            }
        }
    }
    
    // 获取当前页面键值
    function getCurrentPageKey() {
        const path = window.location.pathname;
        const filename = path.split('/').pop().replace('.html', '');
        
        const pageMap = {
            'dashboard': 'dashboard',
            'users': 'users',
            'agents': 'agents',
            'finance': 'finance',
            'settings': 'settings',
            'content': 'content',
            'api': 'api',
            'marketing': 'marketing',
            'security': 'security',
            'login': 'login'
        };
        
        return pageMap[filename] || 'dashboard';
    }
    
    // 提供全局API
    window.BilingualSystem = {
        config: BILINGUAL_CONFIG,
        getCurrentLanguage: () => localStorage.getItem(BILINGUAL_CONFIG.storageKey) || BILINGUAL_CONFIG.defaultLanguage,
        getSupportedLanguages: () => BILINGUAL_CONFIG.supportedLanguages,
        switchLanguage: (language) => {
            if (BILINGUAL_CONFIG.supportedLanguages.includes(language)) {
                localStorage.setItem(BILINGUAL_CONFIG.storageKey, language);
                if (window.i18n) {
                    i18n.changeLanguage(language);
                }
                return true;
            }
            return false;
        },
        isLanguageSupported: (language) => BILINGUAL_CONFIG.supportedLanguages.includes(language),
        detectBrowserLanguage,
        loadLanguagePack
    };
    
    // 立即初始化
    initBilingualSystem();
    
    // 确保在DOM加载完成后再次检查
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', () => {
            // 如果i18n系统还未初始化，再次尝试
            if (!window.i18n) {
                setTimeout(initBilingualSystem, 100);
            }
        });
    }
    
})();
