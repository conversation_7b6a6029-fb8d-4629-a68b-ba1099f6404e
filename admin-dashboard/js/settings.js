/**
 * 系统设置页面JavaScript
 * 处理基本设置、价格设置、支付设置、系统参数、通知设置、操作日志等功能
 */

class SettingsManager {
    constructor() {
        this.currentTab = 'basic-settings';
        this.currentPage = 1;
        this.pageSize = 20;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadBasicSettings();
        this.loadPricingSettings();
        this.loadPaymentSettings();
        this.loadSystemParameters();
        this.loadNotificationSettings();
        this.loadOperationLogs();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#settingsTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 表单提交
        document.getElementById('basicSettingsForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveBasicSettings();
        });
        
        document.getElementById('pricingForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePricingSettings();
        });
        
        document.getElementById('paymentForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePaymentSettings();
        });
        
        document.getElementById('systemParametersForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSystemParameters();
        });
        
        document.getElementById('notificationForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveNotificationSettings();
        });
        
        // 操作日志筛选
        document.getElementById('logLevelFilter')?.addEventListener('change', () => {
            this.loadOperationLogs();
        });
        
        document.getElementById('logModuleFilter')?.addEventListener('change', () => {
            this.loadOperationLogs();
        });
        
        // 添加价格方案
        document.getElementById('addPricingPlanBtn')?.addEventListener('click', () => {
            this.showAddPricingPlanModal();
        });
        
        // 测试通知
        document.getElementById('testEmailBtn')?.addEventListener('click', () => {
            this.testEmailNotification();
        });
        
        document.getElementById('testSmsBtn')?.addEventListener('click', () => {
            this.testSmsNotification();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'basic-settings':
                this.loadBasicSettings();
                break;
            case 'pricing-settings':
                this.loadPricingSettings();
                break;
            case 'payment-settings':
                this.loadPaymentSettings();
                break;
            case 'system-parameters':
                this.loadSystemParameters();
                break;
            case 'notification-settings':
                this.loadNotificationSettings();
                break;
            case 'operation-logs':
                this.loadOperationLogs();
                break;
        }
    }
    
    async loadBasicSettings() {
        try {
            const response = await fetch('/api/settings/basic');
            const result = await response.json();
            
            if (result.success) {
                this.populateBasicSettingsForm(result.data);
            }
        } catch (error) {
            console.error('加载基本设置失败:', error);
        }
    }
    
    populateBasicSettingsForm(settings) {
        document.getElementById('siteName').value = settings.siteName || '';
        document.getElementById('siteDescription').value = settings.siteDescription || '';
        document.getElementById('contactEmail').value = settings.contactEmail || '';
        document.getElementById('contactPhone').value = settings.contactPhone || '';
        document.getElementById('companyAddress').value = settings.companyAddress || '';
        document.getElementById('icp').value = settings.icp || '';
        document.getElementById('copyright').value = settings.copyright || '';
        document.getElementById('timezone').value = settings.timezone || 'Asia/Shanghai';
        document.getElementById('language').value = settings.language || 'zh-CN';
        document.getElementById('maintenanceMode').checked = settings.maintenanceMode || false;
    }
    
    async saveBasicSettings() {
        try {
            const formData = {
                siteName: document.getElementById('siteName').value,
                siteDescription: document.getElementById('siteDescription').value,
                contactEmail: document.getElementById('contactEmail').value,
                contactPhone: document.getElementById('contactPhone').value,
                companyAddress: document.getElementById('companyAddress').value,
                icp: document.getElementById('icp').value,
                copyright: document.getElementById('copyright').value,
                timezone: document.getElementById('timezone').value,
                language: document.getElementById('language').value,
                maintenanceMode: document.getElementById('maintenanceMode').checked
            };
            
            const response = await fetch('/api/settings/basic', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('基本设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存基本设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadPricingSettings() {
        try {
            const response = await fetch('/api/settings/pricing');
            const result = await response.json();
            
            if (result.success) {
                this.renderPricingPlans(result.data);
            }
        } catch (error) {
            console.error('加载价格设置失败:', error);
        }
    }
    
    renderPricingPlans(plans) {
        const container = document.getElementById('pricingPlansContainer');
        if (!container) return;
        
        container.innerHTML = plans.map(plan => `
            <div class="col-md-4 mb-3">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">${plan.productName}</h6>
                        <span class="badge bg-${plan.status === 'active' ? 'success' : 'secondary'}">
                            ${plan.status === 'active' ? '启用' : '禁用'}
                        </span>
                    </div>
                    <div class="card-body">
                        <div class="pricing-info">
                            <div class="price">
                                <span class="current-price">¥${plan.currentPrice}</span>
                                ${plan.originalPrice !== plan.currentPrice ? 
                                    `<span class="original-price">¥${plan.originalPrice}</span>` : ''}
                            </div>
                            <div class="discount">
                                ${plan.discountPercentage > 0 ? 
                                    `<span class="badge bg-danger">${plan.discountPercentage}% OFF</span>` : ''}
                            </div>
                        </div>
                        <ul class="feature-list">
                            ${plan.features.map(feature => `<li>${feature}</li>`).join('')}
                        </ul>
                        <div class="plan-actions">
                            <button class="btn btn-outline-primary btn-sm" onclick="settingsManager.editPricingPlan('${plan._id}')">
                                <i class="bi bi-pencil"></i> 编辑
                            </button>
                            <button class="btn btn-outline-danger btn-sm" onclick="settingsManager.deletePricingPlan('${plan._id}')">
                                <i class="bi bi-trash"></i> 删除
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    async loadSystemParameters() {
        try {
            const response = await fetch('/api/settings/system');
            const result = await response.json();
            
            if (result.success) {
                this.populateSystemParametersForm(result.data);
            }
        } catch (error) {
            console.error('加载系统参数失败:', error);
        }
    }
    
    populateSystemParametersForm(settings) {
        document.getElementById('maxFileSize').value = settings.maxFileSize || 10;
        document.getElementById('sessionTimeout').value = settings.sessionTimeout || 30;
        document.getElementById('maxLoginAttempts').value = settings.maxLoginAttempts || 5;
        document.getElementById('passwordMinLength').value = settings.passwordMinLength || 8;
        document.getElementById('apiRateLimit').value = settings.apiRateLimit || 1000;
        document.getElementById('cacheExpiry').value = settings.cacheExpiry || 3600;
        document.getElementById('logLevel').value = settings.logLevel || 'info';
        document.getElementById('backupFrequency').value = settings.backupFrequency || 'daily';
        
        document.getElementById('enableTwoFactor').checked = settings.enableTwoFactor || false;
        document.getElementById('enableCache').checked = settings.enableCache || true;
        document.getElementById('enableLogging').checked = settings.enableLogging || true;
        document.getElementById('enableMaintenance').checked = settings.enableMaintenance || false;
        
        // 设置允许的文件类型
        if (settings.allowedFileTypes) {
            settings.allowedFileTypes.forEach(type => {
                const checkbox = document.querySelector(`input[name="allowedFileTypes"][value="${type}"]`);
                if (checkbox) checkbox.checked = true;
            });
        }
    }
    
    async saveSystemParameters() {
        try {
            const allowedFileTypes = Array.from(
                document.querySelectorAll('input[name="allowedFileTypes"]:checked')
            ).map(cb => cb.value);
            
            const formData = {
                maxFileSize: parseInt(document.getElementById('maxFileSize').value),
                allowedFileTypes,
                sessionTimeout: parseInt(document.getElementById('sessionTimeout').value),
                maxLoginAttempts: parseInt(document.getElementById('maxLoginAttempts').value),
                passwordMinLength: parseInt(document.getElementById('passwordMinLength').value),
                enableTwoFactor: document.getElementById('enableTwoFactor').checked,
                apiRateLimit: parseInt(document.getElementById('apiRateLimit').value),
                enableCache: document.getElementById('enableCache').checked,
                cacheExpiry: parseInt(document.getElementById('cacheExpiry').value),
                enableLogging: document.getElementById('enableLogging').checked,
                logLevel: document.getElementById('logLevel').value,
                backupFrequency: document.getElementById('backupFrequency').value,
                enableMaintenance: document.getElementById('enableMaintenance').checked
            };
            
            const response = await fetch('/api/settings/system', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('系统参数保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存系统参数失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadOperationLogs() {
        try {
            const level = document.getElementById('logLevelFilter')?.value || '';
            const module = document.getElementById('logModuleFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                level,
                module
            });
            
            const response = await fetch(`/api/settings/operation-logs?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderOperationLogsTable(result.data);
                this.renderPagination(result.pagination);
            }
        } catch (error) {
            console.error('加载操作日志失败:', error);
        }
    }
    
    renderOperationLogsTable(logs) {
        const tbody = document.getElementById('operationLogsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = logs.map(log => `
            <tr>
                <td>${new Date(log.timestamp).toLocaleString()}</td>
                <td>${log.username || log.userId}</td>
                <td>${log.module}</td>
                <td>${log.operation}</td>
                <td>${log.details}</td>
                <td>
                    <span class="badge bg-${this.getLogLevelColor(log.level)}">
                        ${this.getLogLevelText(log.level)}
                    </span>
                </td>
                <td>${log.ip}</td>
            </tr>
        `).join('');
    }
    
    getLogLevelColor(level) {
        const colors = {
            'info': 'info',
            'warning': 'warning',
            'error': 'danger',
            'success': 'success'
        };
        return colors[level] || 'secondary';
    }
    
    getLogLevelText(level) {
        const texts = {
            'info': '信息',
            'warning': '警告',
            'error': '错误',
            'success': '成功'
        };
        return texts[level] || level;
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination) {
        const paginationEl = document.getElementById('logsPagination');
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="settingsManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="settingsManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="settingsManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadOperationLogs();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.settingsManager = new SettingsManager();
});

// 导出
window.SettingsManager = SettingsManager;
