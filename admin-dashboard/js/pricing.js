/**
 * 价格管理JavaScript
 * 处理产品价格设置、折扣配置、会员价格等功能
 */

class PricingManager {
    constructor() {
        this.products = [];
        this.discounts = [];
        this.memberLevels = [];
        this.selectedProducts = new Set();
        this.currentPage = 1;
        this.pageSize = 20;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadPricingStats();
        this.loadProducts();
        this.loadDiscounts();
        this.loadMemberPricing();
        this.loadPriceHistory();
    }

    bindEvents() {
        // 添加产品按钮
        document.getElementById('addProductBtn')?.addEventListener('click', () => {
            this.showProductModal();
        });

        // 保存产品价格
        document.getElementById('saveProductPriceBtn')?.addEventListener('click', () => {
            this.saveProductPrice();
        });

        // 添加折扣按钮
        document.getElementById('addDiscountBtn')?.addEventListener('click', () => {
            this.showDiscountModal();
        });

        // 保存折扣
        document.getElementById('saveDiscountBtn')?.addEventListener('click', () => {
            this.saveDiscount();
        });

        // 全选/取消全选
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });

        // 清除选择
        document.getElementById('clearSelectionBtn')?.addEventListener('click', () => {
            this.clearSelection();
        });

        // 批量调价
        document.getElementById('batchAdjustPriceBtn')?.addEventListener('click', () => {
            this.showBatchPriceModal();
        });

        // 批量调价表单
        document.getElementById('batchPriceForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.executeBatchPriceAdjustment();
        });

        // 搜索
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.searchProducts();
        });

        // 调价方式变化
        document.getElementById('adjustmentType')?.addEventListener('change', (e) => {
            this.updateAdjustmentUnit(e.target.value);
        });

        // 折扣类型变化
        document.getElementById('discountType')?.addEventListener('change', (e) => {
            this.updateDiscountUnit(e.target.value);
        });

        // 适用范围变化
        document.querySelectorAll('input[name="applicableScope"]').forEach(radio => {
            radio.addEventListener('change', (e) => {
                this.toggleProductSelection(e.target.value);
            });
        });

        // 添加会员等级
        document.getElementById('addMemberLevelBtn')?.addEventListener('click', () => {
            this.addMemberLevel();
        });

        // 保存会员价格配置
        document.getElementById('saveMemberPricingBtn')?.addEventListener('click', () => {
            this.saveMemberPricing();
        });
    }

    async loadPricingStats() {
        try {
            // 模拟统计数据
            const stats = {
                totalProducts: 156,
                avgPrice: 299.50,
                activeDiscounts: 8,
                recentChanges: 23
            };

            this.updateStatsDisplay(stats);
        } catch (error) {
            console.error('加载价格统计失败:', error);
        }
    }

    async loadProducts() {
        try {
            // 生成模拟产品数据
            this.products = this.generateMockProducts();
            this.renderProducts();
        } catch (error) {
            console.error('加载产品列表失败:', error);
        }
    }

    async loadDiscounts() {
        try {
            // 生成模拟折扣数据
            this.discounts = this.generateMockDiscounts();
            this.renderDiscounts();
        } catch (error) {
            console.error('加载折扣列表失败:', error);
        }
    }

    async loadMemberPricing() {
        try {
            // 生成模拟会员等级数据
            this.memberLevels = [
                { level: 1, name: '普通会员', discountRate: 0.05, minSpend: 0 },
                { level: 2, name: '银牌会员', discountRate: 0.10, minSpend: 1000 },
                { level: 3, name: '金牌会员', discountRate: 0.15, minSpend: 5000 },
                { level: 4, name: '钻石会员', discountRate: 0.20, minSpend: 20000 }
            ];

            this.renderMemberLevels();
        } catch (error) {
            console.error('加载会员价格配置失败:', error);
        }
    }

    async loadPriceHistory() {
        try {
            // 生成模拟价格变更历史
            const history = this.generateMockPriceHistory();
            this.renderPriceHistory(history);
        } catch (error) {
            console.error('加载价格历史失败:', error);
        }
    }

    generateMockProducts() {
        const products = [];
        const categories = ['basic', 'premium', 'enterprise'];
        const statuses = ['active', 'inactive'];

        for (let i = 1; i <= 50; i++) {
            const basePrice = Math.floor(Math.random() * 1000) + 100;
            const memberPrice = basePrice * 0.9;

            products.push({
                id: `product_${i}`,
                name: `WriterPro ${categories[i % 3] === 'basic' ? '基础版' : categories[i % 3] === 'premium' ? '高级版' : '企业版'} ${i}`,
                sku: `WP-${String(i).padStart(3, '0')}`,
                category: categories[i % 3],
                basePrice: basePrice,
                costPrice: basePrice * 0.6,
                memberPrice: memberPrice,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                description: `产品描述 ${i}`,
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                updatedAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
            });
        }

        return products.sort((a, b) => b.updatedAt - a.updatedAt);
    }

    generateMockDiscounts() {
        const discounts = [];
        const types = ['percentage', 'fixed'];
        const statuses = ['active', 'inactive'];

        for (let i = 1; i <= 10; i++) {
            const type = types[Math.floor(Math.random() * types.length)];
            const value = type === 'percentage' ? Math.floor(Math.random() * 30) + 5 : Math.floor(Math.random() * 100) + 10;

            discounts.push({
                id: `discount_${i}`,
                name: `${type === 'percentage' ? '百分比' : '固定金额'}折扣 ${i}`,
                type: type,
                value: value,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                startDate: new Date(),
                endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                applicableToAll: Math.random() > 0.5,
                description: `折扣描述 ${i}`,
                createdAt: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000)
            });
        }

        return discounts.sort((a, b) => b.createdAt - a.createdAt);
    }

    generateMockPriceHistory() {
        const history = [];
        const reasons = ['市场调整', '成本变化', '促销活动', '竞争对手调价', '季节性调整'];

        for (let i = 1; i <= 20; i++) {
            const oldPrice = Math.floor(Math.random() * 500) + 100;
            const change = (Math.random() - 0.5) * 100;
            const newPrice = Math.max(50, oldPrice + change);

            history.push({
                id: `history_${i}`,
                productId: `product_${Math.floor(Math.random() * 50) + 1}`,
                productName: `产品 ${i}`,
                oldPrice: oldPrice,
                newPrice: newPrice,
                changeAmount: newPrice - oldPrice,
                changePercent: ((newPrice - oldPrice) / oldPrice * 100),
                reason: reasons[Math.floor(Math.random() * reasons.length)],
                changedBy: '管理员',
                createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000)
            });
        }

        return history.sort((a, b) => b.createdAt - a.createdAt);
    }

    updateStatsDisplay(stats) {
        document.getElementById('totalProducts').textContent = stats.totalProducts;
        document.getElementById('avgPrice').textContent = '¥' + this.formatNumber(stats.avgPrice);
        document.getElementById('activeDiscounts').textContent = stats.activeDiscounts;
        document.getElementById('recentChanges').textContent = stats.recentChanges;
    }

    renderProducts() {
        const tbody = document.getElementById('productsTable');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageProducts = this.products.slice(startIndex, endIndex);

        tbody.innerHTML = pageProducts.map(product => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input product-checkbox"
                           value="${product.id}" ${this.selectedProducts.has(product.id) ? 'checked' : ''}>
                </td>
                <td>
                    <div>
                        <strong>${product.name}</strong>
                        <small class="text-muted d-block">${product.description}</small>
                    </div>
                </td>
                <td>${product.sku}</td>
                <td>
                    <span class="badge bg-${this.getCategoryColor(product.category)}">
                        ${this.getCategoryText(product.category)}
                    </span>
                </td>
                <td>
                    <div class="price-display">¥${this.formatNumber(product.basePrice)}</div>
                    <small class="text-muted">成本: ¥${this.formatNumber(product.costPrice)}</small>
                </td>
                <td>
                    <div class="text-success">¥${this.formatNumber(product.memberPrice)}</div>
                    <small class="text-muted">${((1 - product.memberPrice / product.basePrice) * 100).toFixed(1)}% 折扣</small>
                </td>
                <td>
                    <span class="badge bg-${product.status === 'active' ? 'success' : 'secondary'}">
                        ${product.status === 'active' ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${product.updatedAt.toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="pricingManager.editProduct('${product.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="pricingManager.viewPriceHistory('${product.id}')" title="历史">
                            <i class="bi bi-clock-history"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="pricingManager.deleteProduct('${product.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');

        // 绑定复选框事件
        tbody.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleProductSelection(e.target.value, e.target.checked);
            });
        });

        this.renderPagination();
    }

    renderDiscounts() {
        const container = document.getElementById('discountsList');
        if (!container) return;

        container.innerHTML = this.discounts.map(discount => `
            <div class="pricing-card">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <h6 class="mb-1">${discount.name}</h6>
                        <small class="text-muted">${discount.description}</small>
                    </div>
                    <div class="col-md-2">
                        <div class="discount-badge">
                            ${discount.type === 'percentage' ? discount.value + '%' : '¥' + discount.value}
                        </div>
                    </div>
                    <div class="col-md-2">
                        <span class="badge bg-${discount.status === 'active' ? 'success' : 'secondary'}">
                            ${discount.status === 'active' ? '启用' : '禁用'}
                        </span>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            ${discount.startDate.toLocaleDateString()} - ${discount.endDate.toLocaleDateString()}
                        </small>
                    </div>
                    <div class="col-md-2">
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary" onclick="pricingManager.editDiscount('${discount.id}')">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="pricingManager.deleteDiscount('${discount.id}')">
                                <i class="bi bi-trash"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderMemberLevels() {
        const container = document.getElementById('memberLevels');
        if (!container) return;

        container.innerHTML = this.memberLevels.map((level, index) => `
            <div class="member-level mb-3">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <input type="text" class="form-control" value="${level.name}"
                               onchange="pricingManager.updateMemberLevel(${index}, 'name', this.value)">
                    </div>
                    <div class="col-md-2">
                        <div class="input-group">
                            <input type="number" class="form-control" value="${(level.discountRate * 100).toFixed(1)}"
                                   step="0.1" min="0" max="100"
                                   onchange="pricingManager.updateMemberLevel(${index}, 'discountRate', this.value / 100)">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" value="${level.minSpend}"
                                   onchange="pricingManager.updateMemberLevel(${index}, 'minSpend', this.value)">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <small class="text-muted">
                            会员等级 ${level.level}，消费满 ¥${level.minSpend} 享受 ${(level.discountRate * 100).toFixed(1)}% 折扣
                        </small>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-outline-danger btn-sm" onclick="pricingManager.removeMemberLevel(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderPriceHistory(history) {
        const container = document.getElementById('priceHistory');
        if (!container) return;

        container.innerHTML = history.map(item => `
            <div class="price-change ${item.changeAmount > 0 ? 'increase' : 'decrease'}">
                <div class="d-flex justify-content-between align-items-start">
                    <div>
                        <strong>${item.productName}</strong>
                        <div class="mt-1">
                            <span class="price-old">¥${this.formatNumber(item.oldPrice)}</span>
                            <i class="bi bi-arrow-right mx-1"></i>
                            <span class="price-display">¥${this.formatNumber(item.newPrice)}</span>
                        </div>
                        <small class="text-muted">${item.reason}</small>
                    </div>
                    <div class="text-end">
                        <div class="text-${item.changeAmount > 0 ? 'danger' : 'success'}">
                            ${item.changeAmount > 0 ? '+' : ''}${this.formatNumber(item.changeAmount)}
                        </div>
                        <small class="text-muted">${item.changePercent.toFixed(1)}%</small>
                    </div>
                </div>
                <div class="mt-1">
                    <small class="text-muted">
                        ${item.changedBy} · ${item.createdAt.toLocaleString()}
                    </small>
                </div>
            </div>
        `).join('');
    }

    showProductModal(productId = null) {
        const modal = new bootstrap.Modal(document.getElementById('productPriceModal'));
        const form = document.getElementById('productPriceForm');

        if (productId) {
            const product = this.products.find(p => p.id === productId);
            if (product) {
                document.getElementById('productId').value = product.id;
                document.getElementById('productName').value = product.name;
                document.getElementById('productSku').value = product.sku;
                document.getElementById('productCategory').value = product.category;
                document.getElementById('productStatus').value = product.status;
                document.getElementById('basePrice').value = product.basePrice;
                document.getElementById('costPrice').value = product.costPrice;
                document.getElementById('productDescription').value = product.description;
                document.getElementById('productPriceModalTitle').textContent = '编辑产品价格';
            }
        } else {
            form.reset();
            document.getElementById('productId').value = this.generateProductId();
            document.getElementById('productPriceModalTitle').textContent = '添加产品';
        }

        modal.show();
    }

    async saveProductPrice() {
        const productData = {
            id: document.getElementById('productId').value,
            name: document.getElementById('productName').value,
            sku: document.getElementById('productSku').value,
            category: document.getElementById('productCategory').value,
            status: document.getElementById('productStatus').value,
            basePrice: parseFloat(document.getElementById('basePrice').value),
            costPrice: parseFloat(document.getElementById('costPrice').value) || 0,
            description: document.getElementById('productDescription').value,
            changeReason: document.getElementById('changeReason').value
        };

        if (!productData.name || !productData.sku || !productData.basePrice) {
            this.showNotification('请填写必填字段', 'warning');
            return;
        }

        try {
            const existingIndex = this.products.findIndex(p => p.id === productData.id);

            if (existingIndex >= 0) {
                // 更新现有产品
                this.products[existingIndex] = {
                    ...this.products[existingIndex],
                    ...productData,
                    memberPrice: productData.basePrice * 0.9, // 会员价格为基础价格的90%
                    updatedAt: new Date()
                };
            } else {
                // 添加新产品
                this.products.unshift({
                    ...productData,
                    memberPrice: productData.basePrice * 0.9,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }

            this.renderProducts();

            const modal = bootstrap.Modal.getInstance(document.getElementById('productPriceModal'));
            modal.hide();

            this.showNotification('产品价格保存成功', 'success');
        } catch (error) {
            console.error('保存产品价格失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }

    showDiscountModal(discountId = null) {
        const modal = new bootstrap.Modal(document.getElementById('discountModal'));
        const form = document.getElementById('discountForm');

        if (discountId) {
            const discount = this.discounts.find(d => d.id === discountId);
            if (discount) {
                document.getElementById('discountId').value = discount.id;
                document.getElementById('discountName').value = discount.name;
                document.getElementById('discountType').value = discount.type;
                document.getElementById('discountValue').value = discount.value;
                document.getElementById('startDate').value = discount.startDate.toISOString().slice(0, 16);
                document.getElementById('endDate').value = discount.endDate.toISOString().slice(0, 16);
                document.getElementById('discountStatus').value = discount.status;
                document.getElementById('discountDescription').value = discount.description;
                document.getElementById('discountModalTitle').textContent = '编辑折扣';

                if (discount.applicableToAll) {
                    document.getElementById('allProducts').checked = true;
                } else {
                    document.getElementById('specificProducts').checked = true;
                }

                this.updateDiscountUnit(discount.type);
                this.toggleProductSelection(discount.applicableToAll ? 'all' : 'specific');
            }
        } else {
            form.reset();
            document.getElementById('discountId').value = this.generateDiscountId();
            document.getElementById('discountModalTitle').textContent = '创建折扣';
            this.updateDiscountUnit('percentage');
            this.toggleProductSelection('all');
        }

        modal.show();
    }

    async saveDiscount() {
        const discountData = {
            id: document.getElementById('discountId').value,
            name: document.getElementById('discountName').value,
            type: document.getElementById('discountType').value,
            value: parseFloat(document.getElementById('discountValue').value),
            startDate: new Date(document.getElementById('startDate').value),
            endDate: new Date(document.getElementById('endDate').value),
            status: document.getElementById('discountStatus').value,
            description: document.getElementById('discountDescription').value,
            applicableToAll: document.getElementById('allProducts').checked
        };

        if (!discountData.name || !discountData.value) {
            this.showNotification('请填写必填字段', 'warning');
            return;
        }

        if (discountData.startDate >= discountData.endDate) {
            this.showNotification('开始时间必须早于结束时间', 'warning');
            return;
        }

        try {
            const existingIndex = this.discounts.findIndex(d => d.id === discountData.id);

            if (existingIndex >= 0) {
                this.discounts[existingIndex] = {
                    ...this.discounts[existingIndex],
                    ...discountData,
                    updatedAt: new Date()
                };
            } else {
                this.discounts.unshift({
                    ...discountData,
                    createdAt: new Date(),
                    updatedAt: new Date()
                });
            }

            this.renderDiscounts();

            const modal = bootstrap.Modal.getInstance(document.getElementById('discountModal'));
            modal.hide();

            this.showNotification('折扣保存成功', 'success');
        } catch (error) {
            console.error('保存折扣失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }

    async executeBatchPriceAdjustment() {
        const adjustmentType = document.getElementById('adjustmentType').value;
        const priceAction = document.querySelector('input[name="priceAction"]:checked').value;
        const adjustmentValue = parseFloat(document.getElementById('adjustmentValue').value);
        const category = document.getElementById('batchCategory').value;
        const reason = document.getElementById('batchReason').value;

        if (!adjustmentValue || adjustmentValue <= 0) {
            this.showNotification('请输入有效的调价幅度', 'warning');
            return;
        }

        try {
            let filteredProducts = this.products;
            if (category) {
                filteredProducts = this.products.filter(p => p.category === category);
            }

            let updatedCount = 0;

            filteredProducts.forEach(product => {
                let newPrice = product.basePrice;

                if (adjustmentType === 'percentage') {
                    if (priceAction === 'increase') {
                        newPrice = product.basePrice * (1 + adjustmentValue / 100);
                    } else {
                        newPrice = product.basePrice * (1 - adjustmentValue / 100);
                    }
                } else {
                    if (priceAction === 'increase') {
                        newPrice = product.basePrice + adjustmentValue;
                    } else {
                        newPrice = product.basePrice - adjustmentValue;
                    }
                }

                newPrice = Math.max(0, newPrice);

                if (newPrice !== product.basePrice) {
                    product.basePrice = newPrice;
                    product.memberPrice = newPrice * 0.9;
                    product.updatedAt = new Date();
                    updatedCount++;
                }
            });

            this.renderProducts();
            this.showNotification(`批量调价完成，共更新 ${updatedCount} 个产品`, 'success');

            // 重置表单
            document.getElementById('batchPriceForm').reset();
        } catch (error) {
            console.error('批量调价失败:', error);
            this.showNotification('批量调价失败', 'error');
        }
    }

    toggleProductSelection(productId, selected) {
        if (selected) {
            this.selectedProducts.add(productId);
        } else {
            this.selectedProducts.delete(productId);
        }

        this.updateBatchActions();
        this.updateSelectAllState();
    }

    toggleSelectAll(selectAll) {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.toggleProductSelection(checkbox.value, selectAll);
        });
    }

    updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.product-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');

        if (checkboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else {
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;

            if (checkedCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCount === checkboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    }

    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');

        if (this.selectedProducts.size > 0) {
            batchActions.classList.add('show');
            selectedCount.textContent = this.selectedProducts.size;
        } else {
            batchActions.classList.remove('show');
        }
    }

    clearSelection() {
        this.selectedProducts.clear();
        document.querySelectorAll('.product-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateBatchActions();
        this.updateSelectAllState();
    }

    updateAdjustmentUnit(type) {
        const unit = document.getElementById('adjustmentUnit');
        unit.textContent = type === 'percentage' ? '%' : '¥';
    }

    updateDiscountUnit(type) {
        const unit = document.getElementById('discountUnit');
        unit.textContent = type === 'percentage' ? '%' : '¥';
    }

    toggleProductSelection(scope) {
        const productSelectionGroup = document.getElementById('productSelectionGroup');
        if (scope === 'specific') {
            productSelectionGroup.style.display = 'block';
            this.loadProductOptions();
        } else {
            productSelectionGroup.style.display = 'none';
        }
    }

    loadProductOptions() {
        const select = document.getElementById('selectedProducts');
        select.innerHTML = this.products.map(product =>
            `<option value="${product.id}">${product.name} (${product.sku})</option>`
        ).join('');
    }

    addMemberLevel() {
        const newLevel = {
            level: this.memberLevels.length + 1,
            name: `会员等级 ${this.memberLevels.length + 1}`,
            discountRate: 0.05,
            minSpend: 0
        };

        this.memberLevels.push(newLevel);
        this.renderMemberLevels();
    }

    updateMemberLevel(index, field, value) {
        if (this.memberLevels[index]) {
            this.memberLevels[index][field] = field === 'discountRate' ? parseFloat(value) :
                                            field === 'minSpend' ? parseInt(value) : value;
        }
    }

    removeMemberLevel(index) {
        if (confirm('确定要删除此会员等级吗？')) {
            this.memberLevels.splice(index, 1);
            // 重新分配等级编号
            this.memberLevels.forEach((level, i) => {
                level.level = i + 1;
            });
            this.renderMemberLevels();
        }
    }

    async saveMemberPricing() {
        try {
            // 模拟保存会员价格配置
            this.showNotification('会员价格配置保存成功', 'success');
        } catch (error) {
            console.error('保存会员价格配置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }

    editProduct(productId) {
        this.showProductModal(productId);
    }

    editDiscount(discountId) {
        this.showDiscountModal(discountId);
    }

    deleteProduct(productId) {
        if (confirm('确定要删除此产品吗？')) {
            this.products = this.products.filter(p => p.id !== productId);
            this.renderProducts();
            this.showNotification('产品删除成功', 'success');
        }
    }

    deleteDiscount(discountId) {
        if (confirm('确定要删除此折扣吗？')) {
            this.discounts = this.discounts.filter(d => d.id !== discountId);
            this.renderDiscounts();
            this.showNotification('折扣删除成功', 'success');
        }
    }

    renderPagination() {
        const pagination = document.getElementById('productsPagination');
        if (!pagination) return;

        const totalPages = Math.ceil(this.products.length / this.pageSize);

        let paginationHTML = '';

        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="pricingManager.goToPage(${this.currentPage - 1})">上一页</a>
            </li>
        `;

        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="pricingManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }

        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="pricingManager.goToPage(${this.currentPage + 1})">下一页</a>
            </li>
        `;

        pagination.innerHTML = paginationHTML;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.products.length / this.pageSize);
        if (page < 1 || page > totalPages) return;

        this.currentPage = page;
        this.renderProducts();
    }

    // 辅助方法
    generateProductId() {
        return 'product_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    generateDiscountId() {
        return 'discount_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    formatNumber(num) {
        return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }

    getCategoryText(category) {
        const texts = {
            'basic': '基础版',
            'premium': '高级版',
            'enterprise': '企业版'
        };
        return texts[category] || category;
    }

    getCategoryColor(category) {
        const colors = {
            'basic': 'info',
            'premium': 'warning',
            'enterprise': 'success'
        };
        return colors[category] || 'secondary';
    }

    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' :
                          type === 'error' ? 'alert-danger' :
                          type === 'warning' ? 'alert-warning' : 'alert-info';

        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(alert);

        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.pricingManager = new PricingManager();
});

// 导出
window.PricingManager = PricingManager;