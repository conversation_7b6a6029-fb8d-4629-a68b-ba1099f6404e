// Toast通知系统JavaScript

class ToastNotificationSystem {
    constructor() {
        this.notifications = [];
        this.maxNotifications = 5;
        this.init();
    }

    init() {
        this.createNotificationContainer();
        this.setupGlobalFunctions();
        this.startPolling();
    }

    createNotificationContainer() {
        if (document.getElementById('toastContainer')) return;

        const container = document.createElement('div');
        container.id = 'toastContainer';
        container.className = 'toast-container';
        container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
        `;
        document.body.appendChild(container);
    }

    show(message, type = 'info', duration = 5000) {
        const toast = this.createToast(message, type, duration);
        this.addToast(toast);
    }

    createToast(message, type, duration) {
        const id = 'toast_' + Date.now();
        const toast = document.createElement('div');
        toast.id = id;
        toast.className = `alert alert-${this.getBootstrapType(type)} alert-dismissible fade show toast-item`;
        toast.style.cssText = `
            margin-bottom: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            animation: slideInRight 0.3s ease-out;
            border-left: 4px solid ${this.getTypeColor(type)};
        `;

        const icon = this.getIcon(type);
        toast.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="bi ${icon} me-2 fs-5"></i>
                <div class="flex-grow-1">
                    <div class="fw-bold">${this.getTypeTitle(type)}</div>
                    <div>${message}</div>
                </div>
                <button type="button" class="btn-close" onclick="toastSystem.remove('${id}')"></button>
            </div>
        `;

        // 自动移除
        if (duration > 0) {
            setTimeout(() => {
                this.remove(id);
            }, duration);
        }

        return toast;
    }

    addToast(toast) {
        const container = document.getElementById('toastContainer');
        
        // 限制通知数量
        while (this.notifications.length >= this.maxNotifications) {
            const oldestToast = this.notifications.shift();
            if (oldestToast && oldestToast.parentNode) {
                oldestToast.remove();
            }
        }

        container.appendChild(toast);
        this.notifications.push(toast);
    }

    remove(id) {
        const toast = document.getElementById(id);
        if (toast) {
            toast.style.animation = 'slideOutRight 0.3s ease-in';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
                this.notifications = this.notifications.filter(n => n.id !== id);
            }, 300);
        }
    }

    getBootstrapType(type) {
        const typeMap = {
            'success': 'success',
            'error': 'danger',
            'warning': 'warning',
            'info': 'info'
        };
        return typeMap[type] || 'info';
    }

    getIcon(type) {
        const iconMap = {
            'success': 'bi-check-circle-fill',
            'error': 'bi-exclamation-triangle-fill',
            'warning': 'bi-exclamation-circle-fill',
            'info': 'bi-info-circle-fill'
        };
        return iconMap[type] || 'bi-info-circle-fill';
    }

    getTypeTitle(type) {
        const titleMap = {
            'success': '成功',
            'error': '错误',
            'warning': '警告',
            'info': '信息'
        };
        return titleMap[type] || '通知';
    }

    getTypeColor(type) {
        const colorMap = {
            'success': '#198754',
            'error': '#dc3545',
            'warning': '#fd7e14',
            'info': '#0dcaf0'
        };
        return colorMap[type] || '#0dcaf0';
    }

    setupGlobalFunctions() {
        // 重写全局的通知函数
        window.showError = (message) => this.show(message, 'error');
        window.showSuccess = (message) => this.show(message, 'success');
        window.showWarning = (message) => this.show(message, 'warning');
        window.showInfo = (message) => this.show(message, 'info');
    }

    // 轮询获取新通知
    async startPolling() {
        setInterval(async () => {
            try {
                await this.checkForNewNotifications();
            } catch (error) {
                console.error('检查通知失败:', error);
            }
        }, 30000); // 每30秒检查一次
    }

    async checkForNewNotifications() {
        try {
            const response = await fetch('/ad/api/notifications/recent');
            const result = await response.json();
            
            if (result.success && result.data.length > 0) {
                result.data.forEach(notification => {
                    this.show(notification.message, notification.type, 8000);
                });
            }
        } catch (error) {
            // 静默处理错误，避免干扰用户
            console.error('获取通知失败:', error);
        }
    }

    // 业务相关通知方法
    showWorkOrderUpdate(workOrderId, status, expertName) {
        const statusText = {
            'assigned': '已分配',
            'in_progress': '进行中',
            'review': '审核中',
            'completed': '已完成',
            'cancelled': '已取消'
        };

        let message = `工单 #${workOrderId} 状态更新为: ${statusText[status] || status}`;
        if (expertName && status === 'assigned') {
            message += ` (专家: ${expertName})`;
        }

        this.show(message, status === 'completed' ? 'success' : 'info', 8000);
    }

    showExpertUpdate(expertCode, status) {
        const statusText = {
            'active': '上线',
            'busy': '忙碌',
            'offline': '离线',
            'suspended': '暂停'
        };

        const message = `专家 ${expertCode} 状态更新为: ${statusText[status] || status}`;
        this.show(message, 'info', 6000);
    }

    showNewOrder(orderId, serviceType, amount) {
        const serviceText = serviceType === 'expert_optimization' ? '专家优化' : 'AI优化';
        const message = `新订单 #${orderId} (${serviceText}) - ¥${amount}`;
        this.show(message, 'success', 10000);
    }

    showPaymentSuccess(orderId, amount) {
        const message = `订单 #${orderId} 支付成功 - ¥${amount}`;
        this.show(message, 'success', 8000);
    }

    showSystemAlert(message, level = 'warning') {
        this.show(message, level, 15000);
    }
}

// 添加CSS样式
const toastStyles = document.createElement('style');
toastStyles.textContent = `
    @keyframes slideInRight {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOutRight {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .toast-item {
        transition: all 0.3s ease;
        border-radius: 8px;
        backdrop-filter: blur(10px);
    }

    .toast-item:hover {
        transform: translateX(-5px);
        box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2) !important;
    }

    .toast-container {
        pointer-events: none;
    }

    .toast-item {
        pointer-events: all;
    }

    @media (max-width: 768px) {
        .toast-container {
            left: 10px;
            right: 10px;
            max-width: none;
        }
    }
`;
document.head.appendChild(toastStyles);

// 创建全局实例
const toastSystem = new ToastNotificationSystem();

// 导出给其他模块使用
window.toastSystem = toastSystem;
