/**
 * 通知中心JavaScript
 * 处理系统通知、消息推送、通知设置等功能
 */

class NotificationCenter {
    constructor() {
        this.notifications = [];
        this.filteredNotifications = [];
        this.currentNotification = null;
        this.settings = {
            enablePush: true,
            enableEmail: true,
            enableSms: false,
            frequency: 'realtime',
            quietStart: '22:00',
            quietEnd: '08:00'
        };
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadNotifications();
        this.loadSettings();
        this.startRealTimeUpdates();
    }
    
    bindEvents() {
        // 创建通知按钮
        document.getElementById('createNotificationBtn')?.addEventListener('click', () => {
            this.showNotificationModal();
        });
        
        // 发送通知按钮
        document.getElementById('sendNotificationBtn')?.addEventListener('click', () => {
            this.sendNotification();
        });
        
        // 全部已读按钮
        document.getElementById('markAllReadBtn')?.addEventListener('click', () => {
            this.markAllAsRead();
        });
        
        // 清空按钮
        document.getElementById('clearAllBtn')?.addEventListener('click', () => {
            this.clearAllNotifications();
        });
        
        // 搜索按钮
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.filterNotifications();
        });
        
        // 保存设置按钮
        document.getElementById('saveSettingsBtn')?.addEventListener('click', () => {
            this.saveSettings();
        });
        
        // 标记已读按钮
        document.getElementById('markReadBtn')?.addEventListener('click', () => {
            this.markAsRead();
        });
        
        // 过滤器变化
        ['typeFilter', 'statusFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.filterNotifications();
            });
        });
        
        // 搜索输入框回车
        document.getElementById('searchInput')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.filterNotifications();
            }
        });
        
        // 发送时间选择
        document.getElementById('sendTime')?.addEventListener('change', (e) => {
            this.toggleScheduledTime(e.target.value);
        });
        
        // 目标用户选择
        document.getElementById('targetUsers')?.addEventListener('change', (e) => {
            this.toggleCustomUsers(e.target.value);
        });
    }
    
    loadNotifications() {
        // 生成示例通知数据
        this.generateMockNotifications();
        this.filterNotifications();
        this.updateNotificationStats();
    }
    
    generateMockNotifications() {
        const types = ['info', 'success', 'warning', 'error', 'system'];
        const priorities = ['low', 'normal', 'high', 'urgent'];
        
        const sampleNotifications = [
            {
                title: '系统维护通知',
                content: '系统将于今晚23:00-01:00进行维护，期间服务可能中断，请提前做好准备。',
                type: 'warning',
                priority: 'high'
            },
            {
                title: '新用户注册',
                content: '用户 <EMAIL> 刚刚注册了账户，请及时审核。',
                type: 'info',
                priority: 'normal'
            },
            {
                title: '支付成功',
                content: '订单 #12345 支付成功，金额：¥299.00',
                type: 'success',
                priority: 'normal'
            },
            {
                title: '数据库连接异常',
                content: '检测到数据库连接异常，请立即检查数据库服务状态。',
                type: 'error',
                priority: 'urgent'
            },
            {
                title: '备份完成',
                content: '今日数据备份已完成，备份文件大小：2.3GB',
                type: 'success',
                priority: 'low'
            },
            {
                title: 'API调用异常',
                content: '第三方API调用失败率超过阈值，请检查API配置。',
                type: 'warning',
                priority: 'high'
            },
            {
                title: '系统更新',
                content: '系统已更新到版本 v2.1.0，新增了多项功能和安全改进。',
                type: 'system',
                priority: 'normal'
            }
        ];
        
        this.notifications = sampleNotifications.map((notif, index) => ({
            id: `notif-${Date.now()}-${index}`,
            ...notif,
            isRead: Math.random() > 0.6,
            createdAt: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000),
            targetUsers: 'all',
            sendMethods: ['push'],
            status: 'sent'
        }));
        
        // 按时间排序
        this.notifications.sort((a, b) => b.createdAt - a.createdAt);
    }
    
    filterNotifications() {
        const typeFilter = document.getElementById('typeFilter').value;
        const statusFilter = document.getElementById('statusFilter').value;
        const searchText = document.getElementById('searchInput').value.toLowerCase();
        
        this.filteredNotifications = this.notifications.filter(notification => {
            // 类型过滤
            if (typeFilter && notification.type !== typeFilter) return false;
            
            // 状态过滤
            if (statusFilter === 'read' && !notification.isRead) return false;
            if (statusFilter === 'unread' && notification.isRead) return false;
            
            // 搜索过滤
            if (searchText && !notification.title.toLowerCase().includes(searchText) && 
                !notification.content.toLowerCase().includes(searchText)) return false;
            
            return true;
        });
        
        this.renderNotifications();
    }
    
    renderNotifications() {
        const container = document.getElementById('notificationsList');
        if (!container) return;
        
        if (this.filteredNotifications.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-bell-slash display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">没有找到通知</h5>
                    <p class="text-muted">尝试调整筛选条件或创建新通知</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.filteredNotifications.map(notification => `
            <div class="notification-item ${notification.isRead ? 'read' : 'unread'}" 
                 onclick="notificationCenter.showNotificationDetail('${notification.id}')">
                <div class="d-flex align-items-start">
                    <div class="notification-icon ${notification.type} me-3">
                        <i class="bi bi-${this.getNotificationIcon(notification.type)}"></i>
                    </div>
                    <div class="flex-grow-1">
                        <div class="d-flex justify-content-between align-items-start mb-1">
                            <h6 class="mb-0">${notification.title}</h6>
                            <div class="d-flex align-items-center gap-2">
                                <span class="badge bg-${this.getPriorityColor(notification.priority)}">${this.getPriorityText(notification.priority)}</span>
                                <span class="notification-time">${this.formatTime(notification.createdAt)}</span>
                            </div>
                        </div>
                        <p class="mb-1 text-muted">${this.truncateText(notification.content, 100)}</p>
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex gap-1">
                                ${notification.sendMethods.map(method => 
                                    `<span class="badge bg-secondary">${this.getSendMethodText(method)}</span>`
                                ).join('')}
                            </div>
                            <div class="d-flex gap-1">
                                ${!notification.isRead ? `
                                    <button class="btn btn-sm btn-outline-primary" 
                                            onclick="event.stopPropagation(); notificationCenter.markNotificationAsRead('${notification.id}')" 
                                            title="标记为已读">
                                        <i class="bi bi-check"></i>
                                    </button>
                                ` : ''}
                                <button class="btn btn-sm btn-outline-danger" 
                                        onclick="event.stopPropagation(); notificationCenter.deleteNotification('${notification.id}')" 
                                        title="删除">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    updateNotificationStats() {
        const total = this.notifications.length;
        const unread = this.notifications.filter(n => !n.isRead).length;
        const sentToday = this.notifications.filter(n => 
            new Date(n.createdAt).toDateString() === new Date().toDateString()
        ).length;
        const pending = this.notifications.filter(n => n.status === 'pending').length;
        
        document.getElementById('totalNotifications').textContent = total;
        document.getElementById('unreadNotifications').textContent = unread;
        document.getElementById('sentToday').textContent = sentToday;
        document.getElementById('pendingNotifications').textContent = pending;
        
        // 更新头部通知徽章
        const badge = document.getElementById('notificationBadge');
        if (badge) {
            badge.textContent = unread;
            badge.style.display = unread > 0 ? 'flex' : 'none';
        }
    }
    
    showNotificationModal() {
        const modal = new bootstrap.Modal(document.getElementById('notificationModal'));
        document.getElementById('notificationForm').reset();
        modal.show();
    }
    
    sendNotification() {
        const type = document.getElementById('notificationType').value;
        const priority = document.getElementById('notificationPriority').value;
        const title = document.getElementById('notificationTitle').value;
        const content = document.getElementById('notificationContent').value;
        const targetUsers = document.getElementById('targetUsers').value;
        const sendTime = document.getElementById('sendTime').value;
        
        if (!type || !title || !content) {
            this.showAlert('请填写必填字段', 'warning');
            return;
        }
        
        const sendMethods = [];
        if (document.getElementById('sendPush').checked) sendMethods.push('push');
        if (document.getElementById('sendEmail').checked) sendMethods.push('email');
        if (document.getElementById('sendSms').checked) sendMethods.push('sms');
        
        if (sendMethods.length === 0) {
            this.showAlert('请选择至少一种发送方式', 'warning');
            return;
        }
        
        const notification = {
            id: this.generateNotificationId(),
            type,
            priority,
            title,
            content,
            targetUsers,
            sendMethods,
            isRead: false,
            createdAt: sendTime === 'now' ? new Date() : new Date(document.getElementById('scheduledTime').value),
            status: sendTime === 'now' ? 'sent' : 'pending'
        };
        
        this.notifications.unshift(notification);
        this.filterNotifications();
        this.updateNotificationStats();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('notificationModal'));
        modal.hide();
        
        this.showAlert('通知发送成功', 'success');
        
        // 如果是立即发送，显示浏览器通知
        if (sendTime === 'now' && sendMethods.includes('push')) {
            this.showBrowserNotification(notification);
        }
    }
    
    showNotificationDetail(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (!notification) return;
        
        this.currentNotification = notification;
        
        document.getElementById('detailType').innerHTML = `<span class="badge bg-${this.getTypeColor(notification.type)}">${this.getTypeText(notification.type)}</span>`;
        document.getElementById('detailPriority').innerHTML = `<span class="badge bg-${this.getPriorityColor(notification.priority)}">${this.getPriorityText(notification.priority)}</span>`;
        document.getElementById('detailTime').textContent = notification.createdAt.toLocaleString();
        document.getElementById('detailStatus').innerHTML = `<span class="badge bg-${notification.isRead ? 'success' : 'warning'}">${notification.isRead ? '已读' : '未读'}</span>`;
        document.getElementById('detailTitle').textContent = notification.title;
        document.getElementById('detailContent').textContent = notification.content;
        
        const modal = new bootstrap.Modal(document.getElementById('notificationDetailModal'));
        modal.show();
    }
    
    markAsRead() {
        if (this.currentNotification && !this.currentNotification.isRead) {
            this.currentNotification.isRead = true;
            this.filterNotifications();
            this.updateNotificationStats();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('notificationDetailModal'));
            modal.hide();
            
            this.showAlert('已标记为已读', 'success');
        }
    }
    
    markNotificationAsRead(notificationId) {
        const notification = this.notifications.find(n => n.id === notificationId);
        if (notification && !notification.isRead) {
            notification.isRead = true;
            this.filterNotifications();
            this.updateNotificationStats();
        }
    }
    
    markAllAsRead() {
        if (!confirm('确定要将所有通知标记为已读吗？')) return;
        
        this.notifications.forEach(notification => {
            notification.isRead = true;
        });
        
        this.filterNotifications();
        this.updateNotificationStats();
        this.showAlert('所有通知已标记为已读', 'success');
    }
    
    deleteNotification(notificationId) {
        if (!confirm('确定要删除此通知吗？')) return;
        
        this.notifications = this.notifications.filter(n => n.id !== notificationId);
        this.filterNotifications();
        this.updateNotificationStats();
        this.showAlert('通知删除成功', 'success');
    }
    
    clearAllNotifications() {
        if (!confirm('确定要清空所有通知吗？此操作不可恢复。')) return;
        
        this.notifications = [];
        this.filterNotifications();
        this.updateNotificationStats();
        this.showAlert('所有通知已清空', 'success');
    }
    
    loadSettings() {
        // 从localStorage加载设置
        const savedSettings = localStorage.getItem('notificationSettings');
        if (savedSettings) {
            this.settings = { ...this.settings, ...JSON.parse(savedSettings) };
        }
        
        // 应用设置到界面
        document.getElementById('enablePush').checked = this.settings.enablePush;
        document.getElementById('enableEmail').checked = this.settings.enableEmail;
        document.getElementById('enableSms').checked = this.settings.enableSms;
        document.getElementById('notificationFrequency').value = this.settings.frequency;
        document.getElementById('quietStart').value = this.settings.quietStart;
        document.getElementById('quietEnd').value = this.settings.quietEnd;
    }
    
    saveSettings() {
        this.settings = {
            enablePush: document.getElementById('enablePush').checked,
            enableEmail: document.getElementById('enableEmail').checked,
            enableSms: document.getElementById('enableSms').checked,
            frequency: document.getElementById('notificationFrequency').value,
            quietStart: document.getElementById('quietStart').value,
            quietEnd: document.getElementById('quietEnd').value
        };
        
        localStorage.setItem('notificationSettings', JSON.stringify(this.settings));
        this.showAlert('设置保存成功', 'success');
    }
    
    toggleScheduledTime(sendTime) {
        const section = document.getElementById('scheduledTimeSection');
        if (sendTime === 'scheduled') {
            section.style.display = 'block';
            // 设置默认时间为1小时后
            const defaultTime = new Date(Date.now() + 60 * 60 * 1000);
            document.getElementById('scheduledTime').value = defaultTime.toISOString().slice(0, 16);
        } else {
            section.style.display = 'none';
        }
    }
    
    toggleCustomUsers(targetUsers) {
        const section = document.getElementById('customUsersSection');
        if (targetUsers === 'custom') {
            section.style.display = 'block';
        } else {
            section.style.display = 'none';
        }
    }
    
    showBrowserNotification(notification) {
        if ('Notification' in window && Notification.permission === 'granted') {
            new Notification(notification.title, {
                body: notification.content,
                icon: '/favicon.ico',
                tag: notification.id
            });
        } else if ('Notification' in window && Notification.permission !== 'denied') {
            Notification.requestPermission().then(permission => {
                if (permission === 'granted') {
                    new Notification(notification.title, {
                        body: notification.content,
                        icon: '/favicon.ico',
                        tag: notification.id
                    });
                }
            });
        }
    }
    
    startRealTimeUpdates() {
        // 模拟实时通知更新
        setInterval(() => {
            if (Math.random() > 0.95) { // 5%概率生成新通知
                this.generateRandomNotification();
            }
        }, 10000); // 每10秒检查一次
    }
    
    generateRandomNotification() {
        const types = ['info', 'warning', 'success'];
        const priorities = ['low', 'normal'];
        const titles = [
            '新用户注册',
            '系统状态更新',
            '任务执行完成',
            '数据同步完成',
            '备份任务完成'
        ];
        const contents = [
            '有新用户注册了账户',
            '系统运行状态正常',
            '定时任务执行成功',
            '数据同步已完成',
            '数据备份已完成'
        ];
        
        const notification = {
            id: this.generateNotificationId(),
            type: types[Math.floor(Math.random() * types.length)],
            priority: priorities[Math.floor(Math.random() * priorities.length)],
            title: titles[Math.floor(Math.random() * titles.length)],
            content: contents[Math.floor(Math.random() * contents.length)],
            targetUsers: 'all',
            sendMethods: ['push'],
            isRead: false,
            createdAt: new Date(),
            status: 'sent'
        };
        
        this.notifications.unshift(notification);
        this.filterNotifications();
        this.updateNotificationStats();
        
        // 显示浏览器通知
        if (this.settings.enablePush) {
            this.showBrowserNotification(notification);
        }
    }
    
    // 辅助方法
    generateNotificationId() {
        return 'notif-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    getNotificationIcon(type) {
        const icons = {
            'info': 'info-circle',
            'success': 'check-circle',
            'warning': 'exclamation-triangle',
            'error': 'x-circle',
            'system': 'gear'
        };
        return icons[type] || 'bell';
    }
    
    getTypeText(type) {
        const texts = {
            'info': '信息',
            'success': '成功',
            'warning': '警告',
            'error': '错误',
            'system': '系统'
        };
        return texts[type] || type;
    }
    
    getTypeColor(type) {
        const colors = {
            'info': 'info',
            'success': 'success',
            'warning': 'warning',
            'error': 'danger',
            'system': 'secondary'
        };
        return colors[type] || 'secondary';
    }
    
    getPriorityText(priority) {
        const texts = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        };
        return texts[priority] || priority;
    }
    
    getPriorityColor(priority) {
        const colors = {
            'low': 'secondary',
            'normal': 'primary',
            'high': 'warning',
            'urgent': 'danger'
        };
        return colors[priority] || 'secondary';
    }
    
    getSendMethodText(method) {
        const texts = {
            'push': '推送',
            'email': '邮件',
            'sms': '短信'
        };
        return texts[method] || method;
    }
    
    formatTime(date) {
        const now = new Date();
        const diff = now - date;
        
        if (diff < 60000) return '刚刚';
        if (diff < 3600000) return Math.floor(diff / 60000) + '分钟前';
        if (diff < 86400000) return Math.floor(diff / 3600000) + '小时前';
        if (diff < 604800000) return Math.floor(diff / 86400000) + '天前';
        
        return date.toLocaleDateString();
    }
    
    truncateText(text, maxLength) {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + '...';
    }
    
    showAlert(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.notificationCenter = new NotificationCenter();
});

// 导出
window.NotificationCenter = NotificationCenter;
