/**
 * i18n 国际化配置
 */

// 支持的语言列表
const languages = [
  { code: 'en', name: 'English', flag: '🇺🇸' },
  { code: 'zh', name: '中文', flag: '🇨🇳' }
];

// 中文翻译资源
const translationZH = {
  "common": {
    "dashboard": "仪表盘",
    "users": "用户管理",
    "agents": "代理商管理",
    "roles": "角色管理",
    "security": "安全中心",
    "content": "内容管理",
    "api": "API配置",
    "marketing": "营销工具",
    "finance": "财务管理",
    "monitor": "系统监控",
    "settings": "系统设置",
    "logout": "退出登录",
    "save": "保存",
    "cancel": "取消",
    "delete": "删除",
    "edit": "编辑",
    "add": "添加",
    "search": "搜索",
    "filter": "筛选",
    "reset": "重置",
    "status": "状态",
    "actions": "操作",
    "confirm": "确认",
    "back": "返回",
    "next": "下一步",
    "submit": "提交",
    "loading": "加载中...",
    "noData": "暂无数据",
    "welcome": "欢迎",
    "language": "语言"
  },
  "login": {
    "title": "管理员登录",
    "subtitle": "登录以访问管理功能",
    "username": "用户名",
    "usernamePlaceholder": "请输入管理员用户名",
    "password": "密码",
    "passwordPlaceholder": "请输入密码",
    "remember": "记住我",
    "login": "登录",
    "forgotPassword": "忘记密码?",
    "twoFactorTitle": "双因素认证",
    "twoFactorDesc": "请输入6位验证码",
    "verify": "验证",
    "resend": "重新发送",
    "countdown": "倒计时",
    "seconds": "秒",
    "expired": "已过期",
    "codeValidTime": "验证码有效期",
    "securityTip": "安全提示: 请勿在公共场所登录管理后台",
    "infoDesc": "全面的管理功能，助力业务增长",
    "feature1": "用户管理与数据分析",
    "feature2": "代理招商与佣金设置",
    "feature3": "财务统计与支付管理",
    "feature4": "系统设置与内容管理"
  },
  "notifications": {
    "success": "成功",
    "error": "错误",
    "warning": "警告",
    "info": "提示",
    "loginSuccess": "登录成功，正在跳转...",
    "loginError": "用户名或密码错误",
    "verifySuccess": "验证成功，正在跳转...",
    "verifyError": "验证码错误",
    "saveSuccess": "保存成功",
    "saveError": "保存失败",
    "deleteSuccess": "删除成功",
    "deleteError": "删除失败",
    "operationSuccess": "操作成功",
    "operationError": "操作失败",
    "requiredField": "必填字段不能为空",
    "networkError": "网络错误，请稍后再试",
    "serverError": "服务器错误，请稍后再试",
    "codeSent": "验证码已重新发送",
    "sendCodeError": "重发验证码失败"
  }
};

// 英文翻译资源
const translationEN = {
  "common": {
    "dashboard": "Dashboard",
    "users": "User Management",
    "agents": "Agent Management",
    "roles": "Role Management",
    "security": "Security Center",
    "content": "Content Management",
    "api": "API Configuration",
    "marketing": "Marketing Tools",
    "finance": "Finance Management",
    "monitor": "System Monitor",
    "settings": "System Settings",
    "logout": "Logout",
    "save": "Save",
    "cancel": "Cancel",
    "delete": "Delete",
    "edit": "Edit",
    "add": "Add",
    "search": "Search",
    "filter": "Filter",
    "reset": "Reset",
    "status": "Status",
    "actions": "Actions",
    "confirm": "Confirm",
    "back": "Back",
    "next": "Next",
    "submit": "Submit",
    "loading": "Loading...",
    "noData": "No Data",
    "welcome": "Welcome",
    "language": "Language"
  },
  "login": {
    "title": "Admin Login",
    "subtitle": "Login to access management features",
    "username": "Username",
    "usernamePlaceholder": "Enter admin username",
    "password": "Password",
    "passwordPlaceholder": "Enter password",
    "remember": "Remember me",
    "login": "Login",
    "forgotPassword": "Forgot Password?",
    "twoFactorTitle": "Two-Factor Authentication",
    "twoFactorDesc": "Please enter the 6-digit verification code",
    "verify": "Verify",
    "resend": "Resend",
    "countdown": "Countdown",
    "seconds": "seconds",
    "expired": "Expired",
    "codeValidTime": "Code valid for",
    "securityTip": "Security tip: Do not login to admin panel in public places",
    "infoDesc": "Comprehensive management features to boost your business",
    "feature1": "User management and data analysis",
    "feature2": "Agent recruitment and commission settings",
    "feature3": "Financial statistics and payment management",
    "feature4": "System settings and content management"
  },
  "notifications": {
    "success": "Success",
    "error": "Error",
    "warning": "Warning",
    "info": "Information",
    "loginSuccess": "Login successful, redirecting...",
    "loginError": "Username or password is incorrect",
    "verifySuccess": "Verification successful, redirecting...",
    "verifyError": "Verification code is incorrect",
    "saveSuccess": "Save successful",
    "saveError": "Save failed",
    "deleteSuccess": "Delete successful",
    "deleteError": "Delete failed",
    "operationSuccess": "Operation successful",
    "operationError": "Operation failed",
    "requiredField": "Required fields cannot be empty",
    "networkError": "Network error, please try again later",
    "serverError": "Server error, please try again later",
    "codeSent": "Verification code has been sent",
    "sendCodeError": "Failed to send verification code"
  }
};

// 翻译资源
const resources = {
  en: {
    translation: translationEN
  },
  zh: {
    translation: translationZH
  }
};

// 当前语言
let currentLanguage = localStorage.getItem('language') || 'zh';

// 获取翻译文本
function t(key) {
  const keys = key.split('.');
  let value = resources[currentLanguage].translation;
  
  for (const k of keys) {
    if (value[k] === undefined) {
      return key;
    }
    value = value[k];
  }
  
  return value;
}

// 切换语言
function changeLanguage(lng) {
  currentLanguage = lng;
  localStorage.setItem('language', lng);
  document.documentElement.lang = lng;
  document.documentElement.setAttribute('lang', lng);
  updateContent();
}

// 获取当前语言
function getCurrentLanguage() {
  return currentLanguage;
}

// 更新页面内容
function updateContent() {
  // 更新所有带有data-i18n属性的元素
  document.querySelectorAll('[data-i18n]').forEach(element => {
    const key = element.getAttribute('data-i18n');
    element.textContent = t(key);
  });

  // 更新所有带有data-i18n-placeholder属性的输入元素
  document.querySelectorAll('[data-i18n-placeholder]').forEach(element => {
    const key = element.getAttribute('data-i18n-placeholder');
    element.placeholder = t(key);
  });

  // 更新所有带有data-i18n-title属性的元素
  document.querySelectorAll('[data-i18n-title]').forEach(element => {
    const key = element.getAttribute('data-i18n-title');
    element.title = t(key);
  });

  // 更新所有带有data-i18n-value属性的按钮元素
  document.querySelectorAll('[data-i18n-value]').forEach(element => {
    const key = element.getAttribute('data-i18n-value');
    element.value = t(key);
  });
}

// 创建语言切换器
function createLanguageSwitcher(containerId) {
  const container = document.getElementById(containerId);
  if (!container) return;

  // 创建语言切换下拉菜单
  const currentLangObj = languages.find(lang => lang.code === currentLanguage) || languages[0];

  // 创建下拉菜单容器
  const dropdown = document.createElement('div');
  dropdown.className = 'language-dropdown';
  
  // 创建当前语言显示
  const selected = document.createElement('div');
  selected.className = 'language-selected';
  selected.innerHTML = `
    <span>${currentLangObj.name}</span>
    <i class="bi bi-chevron-down"></i>
  `;
  
  // 创建下拉菜单选项
  const options = document.createElement('div');
  options.className = 'language-options';
  
  // 添加语言选项
  languages.forEach(lang => {
    const option = document.createElement('div');
    option.className = 'language-option';
    if (lang.code === currentLanguage) {
      option.classList.add('active');
    }
    option.setAttribute('data-lang', lang.code);
    option.textContent = lang.name;
    
    // 点击切换语言
    option.addEventListener('click', () => {
      changeLanguage(lang.code);
      selected.querySelector('span').textContent = lang.name;
      
      // 更新活跃状态
      options.querySelectorAll('.language-option').forEach(opt => {
        opt.classList.remove('active');
      });
      option.classList.add('active');
      
      // 关闭下拉菜单
      dropdown.classList.remove('active');
    });
    
    options.appendChild(option);
  });
  
  // 点击显示/隐藏下拉菜单
  selected.addEventListener('click', () => {
    dropdown.classList.toggle('active');
  });
  
  // 点击外部关闭下拉菜单
  document.addEventListener('click', (e) => {
    if (!dropdown.contains(e.target)) {
      dropdown.classList.remove('active');
    }
  });
  
  // 组装组件
  dropdown.appendChild(selected);
  dropdown.appendChild(options);
  container.appendChild(dropdown);
}

// 初始化
function initI18n() {
  // 设置页面语言
  document.documentElement.lang = currentLanguage;
  document.documentElement.setAttribute('lang', currentLanguage);
  
  // 更新页面内容
  updateContent();
}

// 格式化数字
function formatNumber(num, locale = currentLanguage) {
  return new Intl.NumberFormat(locale === 'zh' ? 'zh-CN' : 'en-US').format(num);
}

// 格式化货币
function formatCurrency(amount, currency = 'CNY', locale = currentLanguage) {
  return new Intl.NumberFormat(locale === 'zh' ? 'zh-CN' : 'en-US', {
    style: 'currency',
    currency: currency
  }).format(amount);
}

// 格式化日期
function formatDate(date, options = {}, locale = currentLanguage) {
  const defaultOptions = {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  };

  return new Intl.DateTimeFormat(
    locale === 'zh' ? 'zh-CN' : 'en-US',
    { ...defaultOptions, ...options }
  ).format(new Date(date));
}

// 格式化相对时间
function formatRelativeTime(date, locale = currentLanguage) {
  const rtf = new Intl.RelativeTimeFormat(locale === 'zh' ? 'zh-CN' : 'en-US', {
    numeric: 'auto'
  });

  const now = new Date();
  const target = new Date(date);
  const diffInSeconds = (target - now) / 1000;

  if (Math.abs(diffInSeconds) < 60) {
    return rtf.format(Math.round(diffInSeconds), 'second');
  } else if (Math.abs(diffInSeconds) < 3600) {
    return rtf.format(Math.round(diffInSeconds / 60), 'minute');
  } else if (Math.abs(diffInSeconds) < 86400) {
    return rtf.format(Math.round(diffInSeconds / 3600), 'hour');
  } else {
    return rtf.format(Math.round(diffInSeconds / 86400), 'day');
  }
}

// 复数处理
function pluralize(count, singular, plural, locale = currentLanguage) {
  if (locale === 'zh') {
    // 中文没有复数形式
    return singular;
  } else {
    return count === 1 ? singular : plural;
  }
}

// 动态加载语言包
async function loadLanguagePack(lang) {
  try {
    const response = await fetch(`/locales/${lang}.json`);
    if (response.ok) {
      const translations = await response.json();
      if (lang === 'zh') {
        Object.assign(translationZH, translations);
      } else if (lang === 'en') {
        Object.assign(translationEN, translations);
      }
    }
  } catch (error) {
    console.warn(`Failed to load language pack for ${lang}:`, error);
  }
}

// 检测浏览器语言
function detectBrowserLanguage() {
  const browserLang = navigator.language || navigator.userLanguage;
  if (browserLang.startsWith('zh')) {
    return 'zh';
  } else {
    return 'en';
  }
}

// 初始化语言设置
function initLanguageSettings() {
  // 从localStorage获取保存的语言设置，如果没有则检测浏览器语言
  const savedLanguage = localStorage.getItem('language');
  if (savedLanguage && ['zh', 'en'].includes(savedLanguage)) {
    currentLanguage = savedLanguage;
  } else {
    currentLanguage = detectBrowserLanguage();
    localStorage.setItem('language', currentLanguage);
  }
}

// 导出i18n对象
window.i18n = {
  t,
  changeLanguage,
  getCurrentLanguage,
  updateContent,
  createLanguageSwitcher,
  initI18n,
  formatNumber,
  formatCurrency,
  formatDate,
  formatRelativeTime,
  pluralize,
  loadLanguagePack,
  detectBrowserLanguage,
  initLanguageSettings
};