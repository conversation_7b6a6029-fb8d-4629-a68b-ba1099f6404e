/**
 * 语言切换组件
 * 提供语言切换界面和功能
 */

class LanguageSwitcher {
    constructor() {
        this.currentLanguage = i18n.getCurrentLanguage();
        this.init();
    }
    
    init() {
        this.createLanguageSwitcher();
        this.bindEvents();
        this.updateLanguageDisplay();
    }
    
    createLanguageSwitcher() {
        // 检查是否已存在语言切换器
        if (document.getElementById('languageSwitcher')) {
            return;
        }
        
        // 创建语言切换下拉菜单
        const languageSwitcher = document.createElement('div');
        languageSwitcher.id = 'languageSwitcher';
        languageSwitcher.className = 'language-switcher dropdown';
        languageSwitcher.innerHTML = `
            <button class="btn btn-outline-secondary dropdown-toggle" type="button" 
                    data-bs-toggle="dropdown" aria-expanded="false" id="languageDropdown">
                <i class="bi bi-globe"></i>
                <span class="language-text">${this.getLanguageText(this.currentLanguage)}</span>
            </button>
            <ul class="dropdown-menu" aria-labelledby="languageDropdown">
                <li>
                    <a class="dropdown-item language-option" href="#" data-lang="zh">
                        <i class="bi bi-flag"></i>
                        <span>中文</span>
                        ${this.currentLanguage === 'zh' ? '<i class="bi bi-check float-end"></i>' : ''}
                    </a>
                </li>
                <li>
                    <a class="dropdown-item language-option" href="#" data-lang="en">
                        <i class="bi bi-flag-fill"></i>
                        <span>English</span>
                        ${this.currentLanguage === 'en' ? '<i class="bi bi-check float-end"></i>' : ''}
                    </a>
                </li>
            </ul>
        `;
        
        // 将语言切换器添加到头部操作区域
        const headerActions = document.querySelector('.header-actions');
        if (headerActions) {
            headerActions.insertBefore(languageSwitcher, headerActions.firstChild);
        } else {
            // 如果没有找到头部操作区域，添加到body
            document.body.appendChild(languageSwitcher);
        }
    }
    
    bindEvents() {
        // 语言选择事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.language-option')) {
                e.preventDefault();
                const lang = e.target.closest('.language-option').getAttribute('data-lang');
                this.switchLanguage(lang);
            }
        });
        
        // 监听语言变化事件
        document.addEventListener('languageChanged', (e) => {
            this.currentLanguage = e.detail.language;
            this.updateLanguageDisplay();
        });
    }
    
    switchLanguage(lang) {
        if (lang === this.currentLanguage) {
            return;
        }
        
        // 显示加载状态
        this.showLoadingState();
        
        // 切换语言
        i18n.changeLanguage(lang).then(() => {
            this.currentLanguage = lang;
            this.updateLanguageDisplay();
            this.hideLoadingState();
            
            // 显示切换成功提示
            this.showNotification(
                i18n.t('messages.operationSuccess'),
                'success'
            );
            
            // 触发语言变化事件
            document.dispatchEvent(new CustomEvent('languageChanged', {
                detail: { language: lang }
            }));
            
        }).catch((error) => {
            console.error('Language switch failed:', error);
            this.hideLoadingState();
            this.showNotification(
                i18n.t('messages.operationFailed'),
                'error'
            );
        });
    }
    
    updateLanguageDisplay() {
        // 更新下拉按钮文本
        const languageText = document.querySelector('.language-text');
        if (languageText) {
            languageText.textContent = this.getLanguageText(this.currentLanguage);
        }
        
        // 更新选中状态
        document.querySelectorAll('.language-option').forEach(option => {
            const lang = option.getAttribute('data-lang');
            const checkIcon = option.querySelector('.bi-check');
            
            if (lang === this.currentLanguage) {
                if (!checkIcon) {
                    option.innerHTML += '<i class="bi bi-check float-end"></i>';
                }
            } else {
                if (checkIcon) {
                    checkIcon.remove();
                }
            }
        });
        
        // 更新页面方向（如果需要支持RTL语言）
        document.documentElement.setAttribute('lang', this.currentLanguage);
        if (this.currentLanguage === 'ar' || this.currentLanguage === 'he') {
            document.documentElement.setAttribute('dir', 'rtl');
        } else {
            document.documentElement.setAttribute('dir', 'ltr');
        }
    }
    
    getLanguageText(lang) {
        const languageNames = {
            'zh': '中文',
            'en': 'English',
            'ja': '日本語',
            'ko': '한국어',
            'fr': 'Français',
            'de': 'Deutsch',
            'es': 'Español',
            'pt': 'Português',
            'ru': 'Русский',
            'ar': 'العربية'
        };
        return languageNames[lang] || lang.toUpperCase();
    }
    
    showLoadingState() {
        const button = document.getElementById('languageDropdown');
        if (button) {
            button.disabled = true;
            button.innerHTML = `
                <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                ${i18n.t('common.loading')}
            `;
        }
    }
    
    hideLoadingState() {
        const button = document.getElementById('languageDropdown');
        if (button) {
            button.disabled = false;
            button.innerHTML = `
                <i class="bi bi-globe"></i>
                <span class="language-text">${this.getLanguageText(this.currentLanguage)}</span>
            `;
        }
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
    
    // 获取当前语言
    getCurrentLanguage() {
        return this.currentLanguage;
    }
    
    // 获取支持的语言列表
    getSupportedLanguages() {
        return [
            { code: 'zh', name: '中文', nativeName: '中文' },
            { code: 'en', name: 'English', nativeName: 'English' }
        ];
    }
    
    // 添加新语言支持
    addLanguageSupport(langCode, langName, nativeName) {
        const option = document.createElement('li');
        option.innerHTML = `
            <a class="dropdown-item language-option" href="#" data-lang="${langCode}">
                <i class="bi bi-flag"></i>
                <span>${nativeName}</span>
            </a>
        `;
        
        const dropdown = document.querySelector('.language-switcher .dropdown-menu');
        if (dropdown) {
            dropdown.appendChild(option);
        }
    }
    
    // 移除语言支持
    removeLanguageSupport(langCode) {
        const option = document.querySelector(`[data-lang="${langCode}"]`);
        if (option) {
            option.closest('li').remove();
        }
    }
    
    // 检查是否支持某种语言
    isLanguageSupported(langCode) {
        return document.querySelector(`[data-lang="${langCode}"]`) !== null;
    }
    
    // 获取浏览器首选语言
    getBrowserPreferredLanguage() {
        const browserLang = navigator.language || navigator.userLanguage;
        const supportedLangs = this.getSupportedLanguages().map(lang => lang.code);
        
        // 精确匹配
        if (supportedLangs.includes(browserLang)) {
            return browserLang;
        }
        
        // 语言代码匹配（如 zh-CN -> zh）
        const langCode = browserLang.split('-')[0];
        if (supportedLangs.includes(langCode)) {
            return langCode;
        }
        
        // 默认返回英文
        return 'en';
    }
    
    // 自动检测并设置语言
    autoDetectLanguage() {
        const savedLang = localStorage.getItem('language');
        if (savedLang && this.isLanguageSupported(savedLang)) {
            return savedLang;
        }
        
        const browserLang = this.getBrowserPreferredLanguage();
        if (this.isLanguageSupported(browserLang)) {
            localStorage.setItem('language', browserLang);
            return browserLang;
        }
        
        // 默认语言
        return 'zh';
    }
}

// 初始化语言切换器
document.addEventListener('DOMContentLoaded', () => {
    // 确保i18n已经初始化
    if (window.i18n) {
        window.languageSwitcher = new LanguageSwitcher();
    } else {
        // 等待i18n初始化完成
        document.addEventListener('i18nReady', () => {
            window.languageSwitcher = new LanguageSwitcher();
        });
    }
});

// 导出
window.LanguageSwitcher = LanguageSwitcher;
