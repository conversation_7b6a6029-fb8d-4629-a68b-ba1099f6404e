/**
 * API配置页面JavaScript
 * 处理谷歌API配置、支付接口、API统计、密钥管理、Webhook等功能
 */

class ApiManager {
    constructor() {
        this.currentTab = 'google-api';
        this.currentPage = 1;
        this.pageSize = 20;
        this.charts = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadGoogleApiConfig();
        this.loadPaymentConfig();
        this.loadApiUsageStats();
        this.loadApiKeys();
        this.loadWebhooks();
        this.initCharts();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#apiTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 表单提交
        document.getElementById('googleApiForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveGoogleApiConfig();
        });
        
        document.getElementById('alipayForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePaymentConfig('alipay');
        });
        
        document.getElementById('wechatPayForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.savePaymentConfig('wechat');
        });
        
        // API测试按钮
        document.getElementById('testGoogleApiBtn')?.addEventListener('click', () => {
            this.testApiConnection('google_translation');
        });
        
        document.getElementById('testAlipayBtn')?.addEventListener('click', () => {
            this.testApiConnection('payment_alipay');
        });
        
        document.getElementById('testWechatPayBtn')?.addEventListener('click', () => {
            this.testApiConnection('payment_wechat');
        });
        
        // 添加按钮
        document.getElementById('addApiKeyBtn')?.addEventListener('click', () => {
            this.showAddApiKeyModal();
        });
        
        document.getElementById('addWebhookBtn')?.addEventListener('click', () => {
            this.showAddWebhookModal();
        });
        
        // 模态框表单提交
        document.getElementById('apiKeyForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveApiKey();
        });
        
        document.getElementById('webhookForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveWebhook();
        });
        
        // 刷新统计按钮
        document.getElementById('refreshStatsBtn')?.addEventListener('click', () => {
            this.loadApiUsageStats();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'google-api':
                this.loadGoogleApiConfig();
                break;
            case 'payment-config':
                this.loadPaymentConfig();
                break;
            case 'usage-stats':
                this.loadApiUsageStats();
                break;
            case 'api-keys':
                this.loadApiKeys();
                break;
            case 'webhooks':
                this.loadWebhooks();
                break;
        }
    }
    
    async loadGoogleApiConfig() {
        try {
            const response = await fetch('/api/api-config/google');
            const result = await response.json();
            
            if (result.success) {
                this.populateGoogleApiForm(result.data);
            }
        } catch (error) {
            console.error('加载谷歌API配置失败:', error);
        }
    }
    
    populateGoogleApiForm(config) {
        document.getElementById('translationApiKey').value = config.translationApiKey || '';
        document.getElementById('documentAiApiKey').value = config.documentAiApiKey || '';
        document.getElementById('visionApiKey').value = config.visionApiKey || '';
        document.getElementById('speechApiKey').value = config.speechApiKey || '';
        document.getElementById('googleProjectId').value = config.projectId || '';
        document.getElementById('googleApiEnabled').checked = config.enabled || false;
        
        // 配额限制
        if (config.quotaLimits) {
            document.getElementById('translationQuota').value = config.quotaLimits.translation || 1000000;
            document.getElementById('documentAiQuota').value = config.quotaLimits.documentAi || 100000;
            document.getElementById('visionQuota').value = config.quotaLimits.vision || 50000;
            document.getElementById('speechQuota').value = config.quotaLimits.speech || 30000;
        }
    }
    
    async saveGoogleApiConfig() {
        try {
            const formData = {
                translationApiKey: document.getElementById('translationApiKey').value,
                documentAiApiKey: document.getElementById('documentAiApiKey').value,
                visionApiKey: document.getElementById('visionApiKey').value,
                speechApiKey: document.getElementById('speechApiKey').value,
                projectId: document.getElementById('googleProjectId').value,
                enabled: document.getElementById('googleApiEnabled').checked,
                quotaLimits: {
                    translation: parseInt(document.getElementById('translationQuota').value),
                    documentAi: parseInt(document.getElementById('documentAiQuota').value),
                    vision: parseInt(document.getElementById('visionQuota').value),
                    speech: parseInt(document.getElementById('speechQuota').value)
                }
            };
            
            const response = await fetch('/api/api-config/google', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('谷歌API配置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存谷歌API配置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadPaymentConfig() {
        try {
            const response = await fetch('/api/api-config/payment');
            const result = await response.json();
            
            if (result.success) {
                this.populatePaymentForms(result.data);
            }
        } catch (error) {
            console.error('加载支付配置失败:', error);
        }
    }
    
    populatePaymentForms(config) {
        // 支付宝配置
        if (config.alipay) {
            document.getElementById('alipayAppId').value = config.alipay.appId || '';
            document.getElementById('alipayPrivateKey').value = config.alipay.privateKey || '';
            document.getElementById('alipayPublicKey').value = config.alipay.publicKey || '';
            document.getElementById('alipayEnabled').checked = config.alipay.enabled || false;
            document.getElementById('alipayEnvironment').value = config.alipay.environment || 'sandbox';
        }
        
        // 微信支付配置
        if (config.wechat) {
            document.getElementById('wechatAppId').value = config.wechat.appId || '';
            document.getElementById('wechatMchId').value = config.wechat.mchId || '';
            document.getElementById('wechatApiKey').value = config.wechat.apiKey || '';
            document.getElementById('wechatEnabled').checked = config.wechat.enabled || false;
            document.getElementById('wechatEnvironment').value = config.wechat.environment || 'sandbox';
        }
    }
    
    async savePaymentConfig(provider) {
        try {
            let formData = {};
            
            if (provider === 'alipay') {
                formData = {
                    appId: document.getElementById('alipayAppId').value,
                    privateKey: document.getElementById('alipayPrivateKey').value,
                    publicKey: document.getElementById('alipayPublicKey').value,
                    enabled: document.getElementById('alipayEnabled').checked,
                    environment: document.getElementById('alipayEnvironment').value
                };
            } else if (provider === 'wechat') {
                formData = {
                    appId: document.getElementById('wechatAppId').value,
                    mchId: document.getElementById('wechatMchId').value,
                    apiKey: document.getElementById('wechatApiKey').value,
                    enabled: document.getElementById('wechatEnabled').checked,
                    environment: document.getElementById('wechatEnvironment').value
                };
            }
            
            const response = await fetch(`/api/api-config/payment/${provider}`, {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(`${provider === 'alipay' ? '支付宝' : '微信支付'}配置保存成功`, 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存支付配置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async testApiConnection(type) {
        try {
            let config = {};
            
            // 获取当前配置
            if (type === 'google_translation') {
                config = {
                    apiKey: document.getElementById('translationApiKey').value,
                    projectId: document.getElementById('googleProjectId').value
                };
            } else if (type === 'payment_alipay') {
                config = {
                    appId: document.getElementById('alipayAppId').value,
                    environment: document.getElementById('alipayEnvironment').value
                };
            } else if (type === 'payment_wechat') {
                config = {
                    mchId: document.getElementById('wechatMchId').value,
                    environment: document.getElementById('wechatEnvironment').value
                };
            }
            
            const response = await fetch('/api/api-config/test-connection', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ type, config })
            });
            
            const result = await response.json();
            
            if (result.success && result.data.success) {
                this.showTestResult(result.data, 'success');
            } else {
                this.showTestResult(result.data, 'error');
            }
        } catch (error) {
            console.error('测试API连接失败:', error);
            this.showNotification('测试失败', 'error');
        }
    }
    
    showTestResult(result, type) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'bi-check-circle' : 'bi-x-circle';
        
        let detailsHtml = '';
        if (result.details && Object.keys(result.details).length > 0) {
            detailsHtml = '<ul class="mb-0 mt-2">';
            Object.entries(result.details).forEach(([key, value]) => {
                detailsHtml += `<li>${key}: ${value}</li>`;
            });
            detailsHtml += '</ul>';
        }
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 400px; max-width: 500px;';
        alert.innerHTML = `
            <div class="d-flex align-items-start">
                <i class="bi ${icon} me-2 mt-1"></i>
                <div class="flex-grow-1">
                    <strong>${result.message}</strong>
                    <div class="small">响应时间: ${result.responseTime}ms</div>
                    ${detailsHtml}
                </div>
            </div>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 8000);
    }
    
    async loadApiUsageStats() {
        try {
            const response = await fetch('/api/api-config/usage-stats');
            const result = await response.json();
            
            if (result.success) {
                this.renderUsageStats(result.data);
                this.updateUsageCharts(result.data);
            }
        } catch (error) {
            console.error('加载API使用统计失败:', error);
        }
    }
    
    renderUsageStats(stats) {
        const container = document.getElementById('usageStatsContainer');
        if (!container) return;
        
        container.innerHTML = stats.map(stat => `
            <div class="col-md-3 mb-3">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">${stat.name}</h6>
                        <div class="row">
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-primary">${stat.calls.toLocaleString()}</h4>
                                    <small class="text-muted">调用次数</small>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <h4 class="text-danger">${stat.errors}</h4>
                                    <small class="text-muted">错误次数</small>
                                </div>
                            </div>
                        </div>
                        <div class="mt-2 text-center">
                            <small class="text-info">平均响应: ${stat.avgResponseTime}ms</small>
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    async loadApiKeys() {
        try {
            const response = await fetch('/api/api-config/keys');
            const result = await response.json();
            
            if (result.success) {
                this.renderApiKeysTable(result.data);
            }
        } catch (error) {
            console.error('加载API密钥失败:', error);
        }
    }
    
    renderApiKeysTable(apiKeys) {
        const tbody = document.getElementById('apiKeysTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = apiKeys.map(key => `
            <tr>
                <td>${key.name}</td>
                <td><code>${key.key.substring(0, 20)}...</code></td>
                <td>
                    <span class="badge bg-${key.status === 'active' ? 'success' : 'secondary'}">
                        ${key.status === 'active' ? '活跃' : '禁用'}
                    </span>
                </td>
                <td>${key.lastUsed ? new Date(key.lastUsed).toLocaleDateString() : '从未使用'}</td>
                <td>${new Date(key.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="apiManager.editApiKey('${key._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-warning" onclick="apiManager.rotateApiKey('${key._id}')">
                            <i class="bi bi-arrow-clockwise"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="apiManager.deleteApiKey('${key._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async rotateApiKey(id) {
        if (!confirm('确定要轮换此API密钥吗？旧密钥将立即失效。')) return;
        
        try {
            const response = await fetch(`/api/api-config/keys/${id}/rotate`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('API密钥轮换成功', 'success');
                this.loadApiKeys();
            } else {
                this.showNotification(result.message || '轮换失败', 'error');
            }
        } catch (error) {
            console.error('轮换API密钥失败:', error);
            this.showNotification('轮换失败', 'error');
        }
    }
    
    initCharts() {
        // 初始化图表容器
        this.charts.usage = null;
    }
    
    updateUsageCharts(stats) {
        // 这里可以使用Chart.js或其他图表库来渲染使用统计图表
        console.log('更新使用统计图表:', stats);
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.apiManager = new ApiManager();
});

// 导出
window.ApiManager = ApiManager;
