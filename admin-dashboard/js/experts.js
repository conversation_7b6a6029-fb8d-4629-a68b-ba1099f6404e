// 专家管理页面JavaScript

let currentPage = 1;
let currentFilters = {
    status: '',
    specialty: '',
    search: '',
    sortBy: 'createdAt'
};

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initExpertsPage();
    
    // 绑定事件
    bindEvents();
    
    // 加载专家数据
    loadExperts();
});

function initExpertsPage() {
    // 侧边栏折叠/展开
    const menuToggle = document.querySelector('.menu-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    
    if (menuToggle && adminLayout) {
        menuToggle.addEventListener('click', () => {
            adminLayout.classList.toggle('sidebar-collapsed');
        });
    }
    
    // 用户下拉菜单
    const userBtn = document.querySelector('.user-btn');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    if (userBtn && dropdownMenu) {
        document.addEventListener('click', (e) => {
            if (!userBtn.contains(e.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
        
        userBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });
    }
}

function bindEvents() {
    // 筛选器事件
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentFilters.status = this.value;
        currentPage = 1;
        loadExperts();
    });
    
    document.getElementById('specialtyFilter').addEventListener('change', function() {
        currentFilters.specialty = this.value;
        currentPage = 1;
        loadExperts();
    });
    
    document.getElementById('sortBy').addEventListener('change', function() {
        currentFilters.sortBy = this.value;
        currentPage = 1;
        loadExperts();
    });
    
    // 搜索事件
    document.getElementById('searchBtn').addEventListener('click', function() {
        currentFilters.search = document.getElementById('searchInput').value;
        currentPage = 1;
        loadExperts();
    });
    
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            currentFilters.search = this.value;
            currentPage = 1;
            loadExperts();
        }
    });
}

async function loadExperts() {
    try {
        const params = new URLSearchParams({
            page: currentPage,
            limit: 10,
            ...currentFilters
        });
        
        const response = await fetch(`/ad/api/dashboard/experts?${params}`);
        const result = await response.json();
        
        if (result.success) {
            renderExpertsTable(result.data.experts);
            renderPagination(result.data);
            updateStats(result.data.experts);
        } else {
            showError('加载专家数据失败: ' + result.message);
        }
    } catch (error) {
        console.error('加载专家数据失败:', error);
        showError('加载专家数据失败，请稍后重试');
    }
}

function renderExpertsTable(experts) {
    const tbody = document.getElementById('expertsTable');
    
    if (!experts || experts.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">暂无专家数据</td>
            </tr>
        `;
        return;
    }
    
    tbody.innerHTML = experts.map(expert => `
        <tr>
            <td>
                <strong>${expert.expertCode}</strong>
            </td>
            <td>
                <div>
                    <strong>${expert.realName}</strong>
                    <br>
                    <small class="text-muted">${expert.username || expert.email || ''}</small>
                </div>
            </td>
            <td>${expert.title || '-'}</td>
            <td>
                <div class="d-flex flex-wrap gap-1">
                    ${parseSpecialties(expert.specialties).map(s => 
                        `<span class="badge bg-light text-dark">${s}</span>`
                    ).join('')}
                </div>
            </td>
            <td>
                <span class="badge ${getStatusBadgeClass(expert.status)}">
                    ${getStatusText(expert.status)}
                </span>
            </td>
            <td>
                <span class="badge ${expert.currentOrders >= expert.maxConcurrentOrders ? 'bg-danger' : 'bg-success'}">
                    ${expert.currentOrders}/${expert.maxConcurrentOrders}
                </span>
            </td>
            <td>
                <strong>${expert.completedOrders}</strong>
                <small class="text-muted">/${expert.totalOrders}</small>
            </td>
            <td>
                <div class="d-flex align-items-center">
                    <span class="me-1">${(expert.averageRating || 0).toFixed(1)}</span>
                    <div class="rating-stars">
                        ${renderStars(expert.averageRating || 0)}
                    </div>
                </div>
            </td>
            <td>
                <small>${formatDateTime(expert.createdAt)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showExpertDetails(${expert.id})">
                        <i class="bi bi-eye"></i>
                    </button>
                    <button class="btn btn-outline-warning" onclick="updateExpertStatus(${expert.id}, '${expert.status}')">
                        <i class="bi bi-gear"></i>
                    </button>
                    <button class="btn btn-outline-info" onclick="editExpert(${expert.id})">
                        <i class="bi bi-pencil"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function renderPagination(data) {
    const pagination = document.getElementById('pagination');
    const { page, total, limit } = data;
    const totalPages = Math.ceil(total / limit);
    
    if (totalPages <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (page > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${page - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(totalPages, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    if (page < totalPages) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${page + 1})">下一页</a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHTML;
}

function updateStats(experts) {
    const stats = {
        total: experts.length,
        active: experts.filter(e => e.status === 'active').length,
        busy: experts.filter(e => e.status === 'busy').length,
        averageRating: experts.reduce((sum, e) => sum + (e.averageRating || 0), 0) / experts.length || 0
    };
    
    document.getElementById('totalExperts').textContent = stats.total;
    document.getElementById('activeExperts').textContent = stats.active;
    document.getElementById('busyExperts').textContent = stats.busy;
    document.getElementById('averageRating').textContent = stats.averageRating.toFixed(1);
}

function changePage(page) {
    currentPage = page;
    loadExperts();
}

function showAddExpertModal() {
    const modal = new bootstrap.Modal(document.getElementById('addExpertModal'));
    modal.show();
}

async function addExpert() {
    try {
        const formData = {
            userId: parseInt(document.getElementById('userId').value),
            realName: document.getElementById('realName').value,
            title: document.getElementById('title').value,
            education: document.getElementById('education').value,
            specialties: Array.from(document.getElementById('specialties').selectedOptions).map(o => o.value),
            languages: Array.from(document.getElementById('languages').selectedOptions).map(o => o.value),
            maxConcurrentOrders: parseInt(document.getElementById('maxConcurrentOrders').value)
        };

        const response = await fetch('/ad/api/experts', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(formData)
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('专家添加成功');
            bootstrap.Modal.getInstance(document.getElementById('addExpertModal')).hide();
            document.getElementById('addExpertForm').reset();
            loadExperts();
        } else {
            showError('添加专家失败: ' + result.message);
        }
    } catch (error) {
        console.error('添加专家失败:', error);
        showError('添加专家失败，请稍后重试');
    }
}

function showExpertDetails(expertId) {
    // TODO: 实现专家详情显示
    console.log('显示专家详情:', expertId);
}

function updateExpertStatus(expertId, currentStatus) {
    // TODO: 实现专家状态更新
    console.log('更新专家状态:', expertId, currentStatus);
}

function editExpert(expertId) {
    // TODO: 实现专家信息编辑
    console.log('编辑专家信息:', expertId);
}

// 辅助函数
function parseSpecialties(specialtiesStr) {
    try {
        return JSON.parse(specialtiesStr || '[]');
    } catch {
        return [];
    }
}

function getStatusBadgeClass(status) {
    const classes = {
        'active': 'bg-success',
        'busy': 'bg-warning',
        'offline': 'bg-secondary',
        'suspended': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'active': '在线',
        'busy': '忙碌',
        'offline': '离线',
        'suspended': '暂停'
    };
    return texts[status] || status;
}

function renderStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHTML = '';
    
    // 实心星星
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="bi bi-star-fill text-warning"></i>';
    }
    
    // 半星
    if (hasHalfStar) {
        starsHTML += '<i class="bi bi-star-half text-warning"></i>';
    }
    
    // 空心星星
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="bi bi-star text-muted"></i>';
    }
    
    return starsHTML;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showError(message) {
    // 简单的错误提示
    alert(message);
}

function showSuccess(message) {
    // 简单的成功提示
    alert(message);
}
