// 工单管理页面JavaScript

let currentPage = 1;
let currentFilters = {
    status: '',
    serviceType: '',
    priority: '',
    search: ''
};

document.addEventListener('DOMContentLoaded', function() {
    // 初始化页面
    initWorkOrdersPage();
    
    // 绑定事件
    bindEvents();
    
    // 加载工单数据
    loadWorkOrders();
});

function initWorkOrdersPage() {
    // 侧边栏折叠/展开
    const menuToggle = document.querySelector('.menu-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    
    if (menuToggle && adminLayout) {
        menuToggle.addEventListener('click', () => {
            adminLayout.classList.toggle('sidebar-collapsed');
        });
    }
    
    // 用户下拉菜单
    const userBtn = document.querySelector('.user-btn');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    if (userBtn && dropdownMenu) {
        document.addEventListener('click', (e) => {
            if (!userBtn.contains(e.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
        
        userBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });
    }
}

function bindEvents() {
    // 筛选器事件
    document.getElementById('statusFilter').addEventListener('change', function() {
        currentFilters.status = this.value;
        currentPage = 1;
        loadWorkOrders();
    });
    
    document.getElementById('serviceTypeFilter').addEventListener('change', function() {
        currentFilters.serviceType = this.value;
        currentPage = 1;
        loadWorkOrders();
    });
    
    document.getElementById('priorityFilter').addEventListener('change', function() {
        currentFilters.priority = this.value;
        currentPage = 1;
        loadWorkOrders();
    });
    
    // 搜索事件
    document.getElementById('searchBtn').addEventListener('click', function() {
        currentFilters.search = document.getElementById('searchInput').value;
        currentPage = 1;
        loadWorkOrders();
    });
    
    document.getElementById('searchInput').addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            currentFilters.search = this.value;
            currentPage = 1;
            loadWorkOrders();
        }
    });
}

async function loadWorkOrders() {
    try {
        // 获取认证token
        const token = localStorage.getItem('adminToken') || localStorage.getItem('token');
        if (!token) {
            showError('未找到认证token，请重新登录');
            return;
        }

        const params = new URLSearchParams({
            page: currentPage,
            pageSize: 10,  // 改为pageSize以匹配专家订单API
            ...currentFilters
        });

        // 调用专家订单管理API
        const response = await fetch(`/api/payments/orders/processing/list?${params}`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        const result = await response.json();

        if (result.success) {
            // 适配专家订单数据格式
            renderWorkOrdersTable(result.orders || []);
            renderPagination(result.pagination || {});
            updateStats(result.orders || []);
        } else {
            showError('加载订单数据失败: ' + (result.message || '未知错误'));
        }
    } catch (error) {
        console.error('加载订单数据失败:', error);
        showError('加载订单数据失败，请稍后重试');
    }
}

function renderWorkOrdersTable(orders) {
    const tbody = document.getElementById('workOrdersTable');

    if (!orders || orders.length === 0) {
        tbody.innerHTML = `
            <tr>
                <td colspan="10" class="text-center text-muted">暂无订单数据</td>
            </tr>
        `;
        return;
    }

    tbody.innerHTML = orders.map(order => `
        <tr>
            <td>#${order.id}</td>
            <td>
                <a href="#" onclick="showOrderDetails(${order.id})" class="text-decoration-none">
                    ${order.description || order.originalFileName || `订单 #${order.id}`}
                </a>
            </td>
            <td>
                <div>
                    <strong>${order.tempUserEmail || order.user?.username || '临时用户'}</strong>
                    <br>
                    <small class="text-muted">${order.tempUserEmail || order.user?.email || ''}</small>
                </div>
            </td>
            <td>
                <span class="badge bg-info">
                    专家优化服务
                </span>
            </td>
            <td>
                <span class="badge ${getOrderStatusBadgeClass(order.status)}">
                    ${getOrderStatusText(order.status)}
                </span>
            </td>
            <td>
                <span class="badge bg-warning">
                    正常
                </span>
            </td>
            <td>
                ${order.expertId ?
                    `<div>
                        <strong>专家 #${order.expertId}</strong>
                        <br>
                        <small class="text-muted">已分配</small>
                    </div>` :
                    '<span class="text-muted">未分配</span>'
                }
            </td>
            <td>¥${parseFloat(order.amount || 0).toFixed(2)}</td>
            <td>
                <small>${formatDateTime(order.createdAt)}</small>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="showOrderDetails(${order.id})">
                        <i class="bi bi-eye"></i>
                    </button>
                    ${order.status === 'processing' ?
                        `<button class="btn btn-outline-success" onclick="showUploadResultModal(${order.id})">
                            <i class="bi bi-upload"></i>
                        </button>` : ''
                    }
                    <button class="btn btn-outline-info" onclick="downloadOriginalFile(${order.id})">
                        <i class="bi bi-download"></i>
                    </button>
                </div>
            </td>
        </tr>
    `).join('');
}

function renderPagination(data) {
    const pagination = document.getElementById('pagination');
    // 适配专家订单API的分页格式
    const { currentPage, totalPages, totalCount, pageSize } = data;
    const page = currentPage || 1;
    const total = totalPages || Math.ceil((totalCount || 0) / (pageSize || 10));

    if (total <= 1) {
        pagination.innerHTML = '';
        return;
    }
    
    let paginationHTML = '';
    
    // 上一页
    if (page > 1) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${page - 1})">上一页</a>
            </li>
        `;
    }
    
    // 页码
    const startPage = Math.max(1, page - 2);
    const endPage = Math.min(total, page + 2);
    
    for (let i = startPage; i <= endPage; i++) {
        paginationHTML += `
            <li class="page-item ${i === page ? 'active' : ''}">
                <a class="page-link" href="#" onclick="changePage(${i})">${i}</a>
            </li>
        `;
    }
    
    // 下一页
    if (page < total) {
        paginationHTML += `
            <li class="page-item">
                <a class="page-link" href="#" onclick="changePage(${page + 1})">下一页</a>
            </li>
        `;
    }
    
    pagination.innerHTML = paginationHTML;
}

function updateStats(orders) {
    const stats = {
        total: orders.length,
        pending: orders.filter(o => o.status === 'pending').length,
        processing: orders.filter(o => o.status === 'processing').length,
        completed: orders.filter(o => o.status === 'completed').length
    };

    // 更新统计显示（如果页面有这些元素）
    const totalElement = document.getElementById('totalWorkOrders');
    const pendingElement = document.getElementById('pendingWorkOrders');
    const processingElement = document.getElementById('inProgressWorkOrders');
    const completedElement = document.getElementById('completedWorkOrders');

    if (totalElement) totalElement.textContent = stats.total;
    if (pendingElement) pendingElement.textContent = stats.pending;
    if (processingElement) processingElement.textContent = stats.processing;
    if (completedElement) completedElement.textContent = stats.completed;
}

function changePage(page) {
    currentPage = page;
    loadWorkOrders();
}

async function showWorkOrderDetails(workOrderId) {
    try {
        const response = await fetch(`/ad/api/workorders/${workOrderId}`);
        const result = await response.json();

        if (result.success) {
            renderWorkOrderDetails(result.data);
            const modal = new bootstrap.Modal(document.getElementById('workOrderModal'));
            modal.show();
            // 加载附件列表
            loadAttachments(workOrderId);
        } else {
            showError('获取工单详情失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取工单详情失败:', error);
        showError('获取工单详情失败，请稍后重试');
    }
}

function renderWorkOrderDetails(workOrder) {
    const detailsContainer = document.getElementById('workOrderDetails');

    detailsContainer.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <h6>基本信息</h6>
                <table class="table table-sm">
                    <tr><td>工单ID:</td><td>#${workOrder.id}</td></tr>
                    <tr><td>标题:</td><td>${workOrder.title}</td></tr>
                    <tr><td>服务类型:</td><td>${getServiceTypeText(workOrder.serviceType)}</td></tr>
                    <tr><td>状态:</td><td><span class="badge ${getStatusBadgeClass(workOrder.status)}">${getStatusText(workOrder.status)}</span></td></tr>
                    <tr><td>优先级:</td><td><span class="badge ${getPriorityBadgeClass(workOrder.priority)}">${getPriorityText(workOrder.priority)}</span></td></tr>
                    <tr><td>创建时间:</td><td>${formatDateTime(workOrder.createdAt)}</td></tr>
                    <tr><td>截止时间:</td><td>${workOrder.deadline ? formatDateTime(workOrder.deadline) : '未设置'}</td></tr>
                </table>
            </div>
            <div class="col-md-6">
                <h6>客户信息</h6>
                <table class="table table-sm">
                    <tr><td>客户姓名:</td><td>${workOrder.user?.username || '未知'}</td></tr>
                    <tr><td>邮箱:</td><td>${workOrder.user?.email || '未知'}</td></tr>
                    <tr><td>电话:</td><td>${workOrder.user?.phone || '未提供'}</td></tr>
                </table>

                <h6>专家信息</h6>
                <table class="table table-sm">
                    <tr><td>专家:</td><td>${workOrder.expert ? `${workOrder.expert.realName} (${workOrder.expert.expertCode})` : '未分配'}</td></tr>
                    <tr><td>分配时间:</td><td>${workOrder.assignedAt ? formatDateTime(workOrder.assignedAt) : '未分配'}</td></tr>
                </table>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-12">
                <h6>工单描述</h6>
                <p class="border p-3 bg-light">${workOrder.description || '无描述'}</p>
            </div>
        </div>

        ${workOrder.originalContent ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>原始内容</h6>
                <div class="border p-3 bg-light" style="max-height: 200px; overflow-y: auto;">
                    ${workOrder.originalContent.substring(0, 500)}${workOrder.originalContent.length > 500 ? '...' : ''}
                </div>
            </div>
        </div>
        ` : ''}

        ${workOrder.optimizedContent ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>优化后内容</h6>
                <div class="border p-3 bg-success bg-opacity-10" style="max-height: 200px; overflow-y: auto;">
                    ${workOrder.optimizedContent.substring(0, 500)}${workOrder.optimizedContent.length > 500 ? '...' : ''}
                </div>
            </div>
        </div>
        ` : ''}

        ${workOrder.comments && workOrder.comments.length > 0 ? `
        <div class="row mt-3">
            <div class="col-12">
                <h6>沟通记录</h6>
                <div style="max-height: 200px; overflow-y: auto;">
                    ${workOrder.comments.map(comment => `
                        <div class="border-bottom pb-2 mb-2">
                            <div class="d-flex justify-content-between">
                                <strong>${comment.author?.username || '未知'}</strong>
                                <small class="text-muted">${formatDateTime(comment.createdAt)}</small>
                            </div>
                            <p class="mb-0">${comment.content}</p>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>
        ` : ''}

        <div class="row mt-3">
            <div class="col-12">
                <h6>附件管理</h6>
                <div class="mb-3">
                    <input type="file" class="form-control" id="fileUpload" multiple accept=".pdf,.doc,.docx,.txt,.jpg,.jpeg,.png,.gif">
                    <div class="form-text">支持PDF、Word、文本和图片文件，最大10MB</div>
                </div>
                <button class="btn btn-outline-primary btn-sm" onclick="uploadFiles(${workOrder.id})">
                    <i class="bi bi-upload"></i> 上传文件
                </button>
                <div id="attachmentsList" class="mt-3">
                    <!-- 附件列表将在这里显示 -->
                </div>
            </div>
        </div>
    `;
}

async function assignExpert(workOrderId) {
    try {
        // 获取可用专家列表
        const response = await fetch('/ad/api/dashboard/experts?status=active');
        const result = await response.json();

        if (result.success && result.data.experts.length > 0) {
            showExpertSelectionModal(workOrderId, result.data.experts);
        } else {
            showError('暂无可用专家');
        }
    } catch (error) {
        console.error('获取专家列表失败:', error);
        showError('获取专家列表失败，请稍后重试');
    }
}

function showExpertSelectionModal(workOrderId, experts) {
    const modalHTML = `
        <div class="modal fade" id="expertSelectionModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">选择专家</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="list-group">
                            ${experts.map(expert => `
                                <div class="list-group-item">
                                    <div class="d-flex justify-content-between align-items-center">
                                        <div>
                                            <h6 class="mb-1">${expert.realName} (${expert.expertCode})</h6>
                                            <p class="mb-1">${expert.title || '无职称'}</p>
                                            <small>当前订单: ${expert.currentOrders}/${expert.maxConcurrentOrders}</small>
                                        </div>
                                        <button class="btn btn-primary btn-sm" onclick="confirmAssignExpert(${workOrderId}, ${expert.id})">
                                            选择
                                        </button>
                                    </div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // 移除已存在的模态框
    const existingModal = document.getElementById('expertSelectionModal');
    if (existingModal) {
        existingModal.remove();
    }

    // 添加新模态框
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    const modal = new bootstrap.Modal(document.getElementById('expertSelectionModal'));
    modal.show();
}

async function confirmAssignExpert(workOrderId, expertId) {
    try {
        const response = await fetch(`/ad/api/workorders/${workOrderId}/assign`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ expertId })
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('专家分配成功');
            bootstrap.Modal.getInstance(document.getElementById('expertSelectionModal')).hide();
            loadWorkOrders(); // 重新加载工单列表
        } else {
            showError('分配专家失败: ' + result.message);
        }
    } catch (error) {
        console.error('分配专家失败:', error);
        showError('分配专家失败，请稍后重试');
    }
}

// 辅助函数
function getServiceTypeBadgeClass(serviceType) {
    const classes = {
        'ai_optimization': 'bg-info',
        'expert_optimization': 'bg-warning'
    };
    return classes[serviceType] || 'bg-secondary';
}

function getServiceTypeText(serviceType) {
    const texts = {
        'ai_optimization': 'AI优化',
        'expert_optimization': '专家优化'
    };
    return texts[serviceType] || serviceType;
}

function getStatusBadgeClass(status) {
    const classes = {
        'pending': 'bg-warning',
        'assigned': 'bg-info',
        'in_progress': 'bg-primary',
        'review': 'bg-secondary',
        'completed': 'bg-success',
        'cancelled': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function getStatusText(status) {
    const texts = {
        'pending': '待分配',
        'assigned': '已分配',
        'in_progress': '进行中',
        'review': '审核中',
        'completed': '已完成',
        'cancelled': '已取消'
    };
    return texts[status] || status;
}

function getPriorityBadgeClass(priority) {
    const classes = {
        'low': 'bg-light text-dark',
        'normal': 'bg-secondary',
        'high': 'bg-warning',
        'urgent': 'bg-danger'
    };
    return classes[priority] || 'bg-secondary';
}

function getPriorityText(priority) {
    const texts = {
        'low': '低',
        'normal': '普通',
        'high': '高',
        'urgent': '紧急'
    };
    return texts[priority] || priority;
}

function formatDateTime(dateString) {
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}

function showError(message) {
    // 简单的错误提示
    alert(message);
}

function showSuccess(message) {
    // 简单的成功提示
    alert(message);
}

function showWarning(message) {
    // 简单的警告提示
    alert(message);
}

// 文件上传功能
async function uploadFiles(workOrderId) {
    const fileInput = document.getElementById('fileUpload');
    const files = fileInput.files;

    if (files.length === 0) {
        showWarning('请选择要上传的文件');
        return;
    }

    const formData = new FormData();
    for (let i = 0; i < files.length; i++) {
        formData.append('files', files[i]);
    }

    try {
        const response = await fetch(`/ad/api/workorders/${workOrderId}/attachments/multiple`, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();

        if (result.success) {
            showSuccess(`成功上传 ${result.data.length} 个文件`);
            fileInput.value = ''; // 清空文件选择
            loadAttachments(workOrderId); // 重新加载附件列表
        } else {
            showError('上传文件失败: ' + result.message);
        }
    } catch (error) {
        console.error('上传文件失败:', error);
        showError('上传文件失败，请稍后重试');
    }
}

// 加载附件列表
async function loadAttachments(workOrderId) {
    try {
        const response = await fetch(`/ad/api/workorders/${workOrderId}/attachments`);
        const result = await response.json();

        if (result.success) {
            renderAttachmentsList(result.data);
        } else {
            console.error('获取附件列表失败:', result.message);
        }
    } catch (error) {
        console.error('获取附件列表失败:', error);
    }
}

// 渲染附件列表
function renderAttachmentsList(attachments) {
    const container = document.getElementById('attachmentsList');

    if (!attachments || attachments.length === 0) {
        container.innerHTML = '<p class="text-muted">暂无附件</p>';
        return;
    }

    container.innerHTML = `
        <div class="list-group">
            ${attachments.map(attachment => `
                <div class="list-group-item d-flex justify-content-between align-items-center">
                    <div>
                        <i class="bi ${getFileIcon(attachment.fileType)} me-2"></i>
                        <strong>${attachment.fileName}</strong>
                        <small class="text-muted ms-2">(${formatFileSize(attachment.fileSize)})</small>
                        <br>
                        <small class="text-muted">
                            上传者: ${attachment.uploader?.username || '未知'} |
                            时间: ${formatDateTime(attachment.createdAt)}
                        </small>
                    </div>
                    <div class="btn-group btn-group-sm">
                        <a href="/ad/api/attachments/${attachment.id}/download" class="btn btn-outline-primary" target="_blank">
                            <i class="bi bi-download"></i>
                        </a>
                        <button class="btn btn-outline-danger" onclick="deleteAttachment(${attachment.id}, ${attachment.workOrderId})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            `).join('')}
        </div>
    `;
}

// 删除附件
async function deleteAttachment(attachmentId, workOrderId) {
    if (!confirm('确定要删除这个附件吗？')) {
        return;
    }

    try {
        const response = await fetch(`/ad/api/attachments/${attachmentId}`, {
            method: 'DELETE'
        });

        const result = await response.json();

        if (result.success) {
            showSuccess('附件删除成功');
            loadAttachments(workOrderId); // 重新加载附件列表
        } else {
            showError('删除附件失败: ' + result.message);
        }
    } catch (error) {
        console.error('删除附件失败:', error);
        showError('删除附件失败，请稍后重试');
    }
}

// 获取文件图标
function getFileIcon(fileType) {
    if (fileType.includes('pdf')) return 'bi-file-earmark-pdf';
    if (fileType.includes('word') || fileType.includes('document')) return 'bi-file-earmark-word';
    if (fileType.includes('text')) return 'bi-file-earmark-text';
    if (fileType.includes('image')) return 'bi-file-earmark-image';
    return 'bi-file-earmark';
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 专家订单状态相关函数
function getOrderStatusBadgeClass(status) {
    switch (status) {
        case 'pending': return 'bg-warning';
        case 'processing': return 'bg-primary';
        case 'completed': return 'bg-success';
        case 'cancelled': return 'bg-danger';
        default: return 'bg-secondary';
    }
}

function getOrderStatusText(status) {
    switch (status) {
        case 'pending': return '待处理';
        case 'processing': return '处理中';
        case 'completed': return '已完成';
        case 'cancelled': return '已取消';
        default: return '未知状态';
    }
}

// 专家订单相关操作函数
function showOrderDetails(orderId) {
    // 显示订单详情（暂时使用alert，后续可以实现模态框）
    alert(`查看订单详情: #${orderId}`);
}

function showUploadResultModal(orderId) {
    // 显示上传结果模态框（暂时使用alert，后续可以实现）
    alert(`上传处理结果: #${orderId}`);
}

function downloadOriginalFile(orderId) {
    // 下载原始文件
    window.open(`/api/payments/orders/${orderId}/download`, '_blank');
}
