/**
 * 任务管理JavaScript
 * 处理系统任务和定时任务的管理功能
 */

class TaskManager {
    constructor() {
        this.tasks = [];
        this.filteredTasks = [];
        this.currentTask = null;
        this.refreshInterval = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadTasks();
        this.startAutoRefresh();
    }
    
    bindEvents() {
        // 创建任务按钮
        document.getElementById('createTaskBtn')?.addEventListener('click', () => {
            this.showTaskModal();
        });
        
        // 保存任务按钮
        document.getElementById('saveTaskBtn')?.addEventListener('click', () => {
            this.saveTask();
        });
        
        // 刷新任务按钮
        document.getElementById('refreshTasksBtn')?.addEventListener('click', () => {
            this.loadTasks();
        });
        
        // 搜索任务按钮
        document.getElementById('searchTasksBtn')?.addEventListener('click', () => {
            this.filterTasks();
        });
        
        // 立即执行任务按钮
        document.getElementById('runTaskNowBtn')?.addEventListener('click', () => {
            this.runTaskNow();
        });
        
        // 任务类型变化
        document.getElementById('taskType')?.addEventListener('change', (e) => {
            this.toggleCronSection(e.target.value);
        });
        
        // 过滤器变化
        ['statusFilter', 'typeFilter'].forEach(id => {
            document.getElementById(id)?.addEventListener('change', () => {
                this.filterTasks();
            });
        });
        
        // 搜索输入框回车
        document.getElementById('taskSearch')?.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') {
                this.filterTasks();
            }
        });
    }
    
    loadTasks() {
        // 生成示例任务数据
        this.generateMockTasks();
        this.filterTasks();
        this.updateTaskStats();
    }
    
    generateMockTasks() {
        this.tasks = [
            {
                id: 'task-001',
                name: '数据库备份',
                description: '每日凌晨2点自动备份数据库',
                type: 'scheduled',
                command: '/usr/bin/mysqldump -u root -p database > /backup/db.sql',
                cronExpression: '0 2 * * *',
                timezone: 'Asia/Shanghai',
                status: 'running',
                priority: 'high',
                enabled: true,
                timeout: 3600,
                retries: 3,
                createdAt: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000),
                lastRun: new Date(Date.now() - 2 * 60 * 60 * 1000),
                nextRun: new Date(Date.now() + 22 * 60 * 60 * 1000),
                totalRuns: 30,
                successRuns: 29,
                failedRuns: 1,
                avgDuration: 45000,
                progress: 75,
                logs: [
                    { time: new Date(), level: 'INFO', message: '开始执行数据库备份任务' },
                    { time: new Date(), level: 'INFO', message: '正在备份表: users' },
                    { time: new Date(), level: 'INFO', message: '正在备份表: transactions' }
                ]
            },
            {
                id: 'task-002',
                name: '日志清理',
                description: '清理30天前的系统日志文件',
                type: 'scheduled',
                command: 'find /var/log -name "*.log" -mtime +30 -delete',
                cronExpression: '0 3 * * 0',
                timezone: 'Asia/Shanghai',
                status: 'pending',
                priority: 'normal',
                enabled: true,
                timeout: 1800,
                retries: 2,
                createdAt: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000),
                lastRun: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                nextRun: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
                totalRuns: 2,
                successRuns: 2,
                failedRuns: 0,
                avgDuration: 120000,
                progress: 0,
                logs: []
            },
            {
                id: 'task-003',
                name: '用户数据同步',
                description: '同步用户数据到外部系统',
                type: 'manual',
                command: '/usr/bin/python3 /scripts/sync_users.py',
                cronExpression: '',
                timezone: 'Asia/Shanghai',
                status: 'completed',
                priority: 'normal',
                enabled: true,
                timeout: 600,
                retries: 3,
                createdAt: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000),
                lastRun: new Date(Date.now() - 1 * 60 * 60 * 1000),
                nextRun: null,
                totalRuns: 5,
                successRuns: 4,
                failedRuns: 1,
                avgDuration: 180000,
                progress: 100,
                logs: [
                    { time: new Date(Date.now() - 1 * 60 * 60 * 1000), level: 'INFO', message: '开始同步用户数据' },
                    { time: new Date(Date.now() - 1 * 60 * 60 * 1000), level: 'INFO', message: '已同步 1000 个用户' },
                    { time: new Date(Date.now() - 1 * 60 * 60 * 1000), level: 'SUCCESS', message: '用户数据同步完成' }
                ]
            },
            {
                id: 'task-004',
                name: '系统健康检查',
                description: '检查系统各项指标是否正常',
                type: 'system',
                command: '/usr/bin/python3 /scripts/health_check.py',
                cronExpression: '*/5 * * * *',
                timezone: 'Asia/Shanghai',
                status: 'failed',
                priority: 'urgent',
                enabled: true,
                timeout: 300,
                retries: 3,
                createdAt: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000),
                lastRun: new Date(Date.now() - 5 * 60 * 1000),
                nextRun: new Date(Date.now() + 5 * 60 * 1000),
                totalRuns: 2880,
                successRuns: 2875,
                failedRuns: 5,
                avgDuration: 15000,
                progress: 0,
                logs: [
                    { time: new Date(Date.now() - 5 * 60 * 1000), level: 'INFO', message: '开始系统健康检查' },
                    { time: new Date(Date.now() - 5 * 60 * 1000), level: 'WARN', message: 'CPU使用率过高: 85%' },
                    { time: new Date(Date.now() - 5 * 60 * 1000), level: 'ERROR', message: '数据库连接失败' }
                ]
            },
            {
                id: 'task-005',
                name: '报表生成',
                description: '生成每日运营报表',
                type: 'scheduled',
                command: '/usr/bin/python3 /scripts/generate_report.py',
                cronExpression: '0 8 * * *',
                timezone: 'Asia/Shanghai',
                status: 'pending',
                priority: 'normal',
                enabled: false,
                timeout: 1200,
                retries: 2,
                createdAt: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000),
                lastRun: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
                nextRun: new Date(Date.now() + 16 * 60 * 60 * 1000),
                totalRuns: 7,
                successRuns: 6,
                failedRuns: 1,
                avgDuration: 300000,
                progress: 0,
                logs: []
            }
        ];
    }
    
    filterTasks() {
        const statusFilter = document.getElementById('statusFilter').value;
        const typeFilter = document.getElementById('typeFilter').value;
        const searchText = document.getElementById('taskSearch').value.toLowerCase();
        
        this.filteredTasks = this.tasks.filter(task => {
            // 状态过滤
            if (statusFilter && task.status !== statusFilter) return false;
            
            // 类型过滤
            if (typeFilter && task.type !== typeFilter) return false;
            
            // 搜索过滤
            if (searchText && !task.name.toLowerCase().includes(searchText) && 
                !task.description.toLowerCase().includes(searchText)) return false;
            
            return true;
        });
        
        this.renderTasks();
    }
    
    renderTasks() {
        const container = document.getElementById('tasksList');
        if (!container) return;
        
        if (this.filteredTasks.length === 0) {
            container.innerHTML = `
                <div class="text-center py-5">
                    <i class="bi bi-inbox display-1 text-muted"></i>
                    <h5 class="mt-3 text-muted">没有找到任务</h5>
                    <p class="text-muted">尝试调整筛选条件或创建新任务</p>
                </div>
            `;
            return;
        }
        
        container.innerHTML = this.filteredTasks.map(task => `
            <div class="task-card ${task.status}" onclick="taskManager.showTaskDetail('${task.id}')">
                <div class="d-flex justify-content-between align-items-start mb-2">
                    <div class="flex-grow-1">
                        <h6 class="mb-1">${task.name}</h6>
                        <p class="text-muted mb-2">${task.description}</p>
                    </div>
                    <div class="d-flex gap-1">
                        <span class="badge bg-${this.getStatusColor(task.status)}">${this.getStatusText(task.status)}</span>
                        <span class="badge bg-secondary">${this.getTypeText(task.type)}</span>
                        <span class="badge bg-${this.getPriorityColor(task.priority)}">${this.getPriorityText(task.priority)}</span>
                    </div>
                </div>
                
                <div class="row mb-2">
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="bi bi-clock"></i> 
                            最后执行: ${task.lastRun ? task.lastRun.toLocaleString() : '从未执行'}
                        </small>
                    </div>
                    <div class="col-md-6">
                        <small class="text-muted">
                            <i class="bi bi-arrow-clockwise"></i> 
                            下次执行: ${task.nextRun ? task.nextRun.toLocaleString() : '无计划'}
                        </small>
                    </div>
                </div>
                
                ${task.status === 'running' && task.progress > 0 ? `
                    <div class="mb-2">
                        <div class="d-flex justify-content-between align-items-center mb-1">
                            <small class="text-muted">执行进度</small>
                            <small class="text-muted">${task.progress}%</small>
                        </div>
                        <div class="progress task-progress">
                            <div class="progress-bar" style="width: ${task.progress}%"></div>
                        </div>
                    </div>
                ` : ''}
                
                <div class="d-flex justify-content-between align-items-center">
                    <div class="d-flex gap-3">
                        <small class="text-muted">
                            <i class="bi bi-play-circle"></i> ${task.totalRuns} 次执行
                        </small>
                        <small class="text-muted">
                            <i class="bi bi-check-circle"></i> ${task.successRuns} 次成功
                        </small>
                        <small class="text-muted">
                            <i class="bi bi-x-circle"></i> ${task.failedRuns} 次失败
                        </small>
                    </div>
                    <div class="d-flex gap-1">
                        <button class="btn btn-sm btn-outline-primary" onclick="event.stopPropagation(); taskManager.editTask('${task.id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="event.stopPropagation(); taskManager.runTask('${task.id}')" title="立即执行">
                            <i class="bi bi-play"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-warning" onclick="event.stopPropagation(); taskManager.toggleTask('${task.id}')" title="${task.enabled ? '禁用' : '启用'}">
                            <i class="bi bi-${task.enabled ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="event.stopPropagation(); taskManager.deleteTask('${task.id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    updateTaskStats() {
        const stats = {
            total: this.tasks.length,
            running: this.tasks.filter(t => t.status === 'running').length,
            pending: this.tasks.filter(t => t.status === 'pending').length,
            failed: this.tasks.filter(t => t.status === 'failed').length
        };
        
        document.getElementById('totalTasks').textContent = stats.total;
        document.getElementById('runningTasks').textContent = stats.running;
        document.getElementById('pendingTasks').textContent = stats.pending;
        document.getElementById('failedTasks').textContent = stats.failed;
    }
    
    showTaskModal(task = null) {
        const modal = new bootstrap.Modal(document.getElementById('taskModal'));
        
        if (task) {
            document.getElementById('taskModalTitle').textContent = '编辑任务';
            this.fillTaskForm(task);
        } else {
            document.getElementById('taskModalTitle').textContent = '创建任务';
            document.getElementById('taskForm').reset();
            document.getElementById('taskId').value = '';
        }
        
        modal.show();
    }
    
    fillTaskForm(task) {
        document.getElementById('taskId').value = task.id;
        document.getElementById('taskName').value = task.name;
        document.getElementById('taskDescription').value = task.description;
        document.getElementById('taskType').value = task.type;
        document.getElementById('taskCommand').value = task.command;
        document.getElementById('taskPriority').value = task.priority;
        document.getElementById('cronExpression').value = task.cronExpression;
        document.getElementById('taskTimezone').value = task.timezone;
        document.getElementById('taskTimeout').value = task.timeout;
        document.getElementById('taskRetries').value = task.retries;
        document.getElementById('taskEnabled').checked = task.enabled;
        
        this.toggleCronSection(task.type);
    }
    
    toggleCronSection(type) {
        const cronSection = document.getElementById('cronSection');
        if (type === 'scheduled') {
            cronSection.style.display = 'block';
        } else {
            cronSection.style.display = 'none';
        }
    }
    
    saveTask() {
        const taskId = document.getElementById('taskId').value;
        const taskData = {
            name: document.getElementById('taskName').value,
            description: document.getElementById('taskDescription').value,
            type: document.getElementById('taskType').value,
            command: document.getElementById('taskCommand').value,
            priority: document.getElementById('taskPriority').value,
            cronExpression: document.getElementById('cronExpression').value,
            timezone: document.getElementById('taskTimezone').value,
            timeout: parseInt(document.getElementById('taskTimeout').value),
            retries: parseInt(document.getElementById('taskRetries').value),
            enabled: document.getElementById('taskEnabled').checked
        };
        
        if (!taskData.name || !taskData.type || !taskData.command) {
            this.showNotification('请填写必填字段', 'warning');
            return;
        }
        
        if (taskId) {
            // 编辑任务
            const taskIndex = this.tasks.findIndex(t => t.id === taskId);
            if (taskIndex !== -1) {
                this.tasks[taskIndex] = { ...this.tasks[taskIndex], ...taskData };
                this.showNotification('任务更新成功', 'success');
            }
        } else {
            // 创建新任务
            const newTask = {
                id: this.generateTaskId(),
                ...taskData,
                status: 'pending',
                createdAt: new Date(),
                lastRun: null,
                nextRun: null,
                totalRuns: 0,
                successRuns: 0,
                failedRuns: 0,
                avgDuration: 0,
                progress: 0,
                logs: []
            };
            
            this.tasks.push(newTask);
            this.showNotification('任务创建成功', 'success');
        }
        
        this.filterTasks();
        this.updateTaskStats();
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('taskModal'));
        modal.hide();
    }
    
    editTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (task) {
            this.showTaskModal(task);
        }
    }
    
    runTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        if (task.status === 'running') {
            this.showNotification('任务正在运行中', 'warning');
            return;
        }
        
        // 模拟任务执行
        task.status = 'running';
        task.progress = 0;
        task.lastRun = new Date();
        task.totalRuns++;
        
        this.showNotification(`任务 "${task.name}" 开始执行`, 'info');
        
        // 模拟进度更新
        const progressInterval = setInterval(() => {
            task.progress += Math.random() * 20;
            if (task.progress >= 100) {
                task.progress = 100;
                task.status = Math.random() > 0.1 ? 'completed' : 'failed';
                if (task.status === 'completed') {
                    task.successRuns++;
                } else {
                    task.failedRuns++;
                }
                clearInterval(progressInterval);
                this.showNotification(`任务 "${task.name}" 执行${task.status === 'completed' ? '成功' : '失败'}`, 
                                    task.status === 'completed' ? 'success' : 'error');
            }
            this.renderTasks();
        }, 1000);
        
        this.renderTasks();
        this.updateTaskStats();
    }
    
    toggleTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        task.enabled = !task.enabled;
        this.showNotification(`任务 "${task.name}" 已${task.enabled ? '启用' : '禁用'}`, 'success');
        this.renderTasks();
    }
    
    deleteTask(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        if (!confirm(`确定要删除任务 "${task.name}" 吗？`)) return;
        
        this.tasks = this.tasks.filter(t => t.id !== taskId);
        this.filterTasks();
        this.updateTaskStats();
        this.showNotification('任务删除成功', 'success');
    }
    
    showTaskDetail(taskId) {
        const task = this.tasks.find(t => t.id === taskId);
        if (!task) return;
        
        this.currentTask = task;
        
        // 填充任务详情
        document.getElementById('detailTaskName').textContent = task.name;
        document.getElementById('detailTaskType').textContent = this.getTypeText(task.type);
        document.getElementById('detailTaskStatus').innerHTML = `<span class="badge bg-${this.getStatusColor(task.status)}">${this.getStatusText(task.status)}</span>`;
        document.getElementById('detailTaskPriority').innerHTML = `<span class="badge bg-${this.getPriorityColor(task.priority)}">${this.getPriorityText(task.priority)}</span>`;
        document.getElementById('detailTaskCreated').textContent = task.createdAt.toLocaleString();
        document.getElementById('detailTaskLastRun').textContent = task.lastRun ? task.lastRun.toLocaleString() : '从未执行';
        document.getElementById('detailTaskCommand').textContent = task.command;
        document.getElementById('detailTaskCron').textContent = task.cronExpression || '无';
        document.getElementById('detailTaskRuns').textContent = task.totalRuns;
        document.getElementById('detailTaskSuccess').textContent = task.successRuns;
        document.getElementById('detailTaskFailed').textContent = task.failedRuns;
        document.getElementById('detailTaskAvgTime').textContent = this.formatDuration(task.avgDuration);
        document.getElementById('detailTaskDescription').textContent = task.description;
        
        // 显示日志
        const logContainer = document.getElementById('detailTaskLog');
        if (task.logs.length > 0) {
            logContainer.innerHTML = task.logs.map(log => 
                `<div class="mb-1">
                    <span class="text-muted">[${log.time.toLocaleTimeString()}]</span>
                    <span class="badge bg-${this.getLogLevelColor(log.level)}">${log.level}</span>
                    ${log.message}
                </div>`
            ).join('');
        } else {
            logContainer.innerHTML = '<div class="text-muted">暂无日志</div>';
        }
        
        const modal = new bootstrap.Modal(document.getElementById('taskDetailModal'));
        modal.show();
    }
    
    runTaskNow() {
        if (this.currentTask) {
            this.runTask(this.currentTask.id);
            const modal = bootstrap.Modal.getInstance(document.getElementById('taskDetailModal'));
            modal.hide();
        }
    }
    
    startAutoRefresh() {
        this.refreshInterval = setInterval(() => {
            // 模拟任务状态更新
            this.tasks.forEach(task => {
                if (task.status === 'running' && Math.random() > 0.9) {
                    task.progress += Math.random() * 10;
                    if (task.progress >= 100) {
                        task.progress = 100;
                        task.status = 'completed';
                        task.successRuns++;
                    }
                }
            });
            
            this.renderTasks();
            this.updateTaskStats();
        }, 5000);
    }
    
    // 辅助方法
    generateTaskId() {
        return 'task-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
    }
    
    getStatusText(status) {
        const texts = {
            'running': '运行中',
            'pending': '等待中',
            'completed': '已完成',
            'failed': '失败'
        };
        return texts[status] || status;
    }
    
    getStatusColor(status) {
        const colors = {
            'running': 'success',
            'pending': 'warning',
            'completed': 'secondary',
            'failed': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    getTypeText(type) {
        const texts = {
            'scheduled': '定时任务',
            'manual': '手动任务',
            'system': '系统任务'
        };
        return texts[type] || type;
    }
    
    getPriorityText(priority) {
        const texts = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        };
        return texts[priority] || priority;
    }
    
    getPriorityColor(priority) {
        const colors = {
            'low': 'secondary',
            'normal': 'primary',
            'high': 'warning',
            'urgent': 'danger'
        };
        return colors[priority] || 'secondary';
    }
    
    getLogLevelColor(level) {
        const colors = {
            'INFO': 'info',
            'WARN': 'warning',
            'ERROR': 'danger',
            'SUCCESS': 'success'
        };
        return colors[level] || 'secondary';
    }
    
    formatDuration(ms) {
        if (ms < 1000) return ms + 'ms';
        if (ms < 60000) return Math.round(ms / 1000) + 's';
        return Math.round(ms / 60000) + 'm';
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.taskManager = new TaskManager();
});

// 导出
window.TaskManager = TaskManager;
