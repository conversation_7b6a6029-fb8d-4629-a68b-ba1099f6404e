/**
 * 角色管理页面JavaScript
 * 处理角色CRUD操作、权限分配、角色层级管理等功能
 */

class RoleManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.selectedRoles = new Set();
        this.permissionTree = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadRoles();
        this.loadPermissionTree();
    }
    
    bindEvents() {
        // 搜索和筛选
        document.getElementById('roleSearchBtn')?.addEventListener('click', () => {
            this.loadRoles();
        });
        
        document.getElementById('roleStatusFilter')?.addEventListener('change', () => {
            this.loadRoles();
        });
        
        // 添加角色按钮
        document.getElementById('addRoleBtn')?.addEventListener('click', () => {
            this.showAddRoleModal();
        });
        
        // 批量操作按钮
        document.getElementById('batchActiveBtn')?.addEventListener('click', () => {
            this.batchUpdateStatus('active');
        });
        
        document.getElementById('batchInactiveBtn')?.addEventListener('click', () => {
            this.batchUpdateStatus('inactive');
        });
        
        // 表单提交
        document.getElementById('roleForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRole();
        });
        
        // 全选功能
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('select-all-roles')) {
                const checkboxes = document.querySelectorAll('.role-checkbox');
                checkboxes.forEach(cb => {
                    cb.checked = e.target.checked;
                    this.updateSelectedRoles(cb.value, cb.checked);
                });
                this.updateBatchActions();
            }
            
            if (e.target.classList.contains('role-checkbox')) {
                this.updateSelectedRoles(e.target.value, e.target.checked);
                this.updateBatchActions();
            }
        });
        
        // 权限树展开/折叠
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('permission-toggle')) {
                e.preventDefault();
                const target = e.target.getAttribute('data-target');
                const element = document.getElementById(target);
                if (element) {
                    element.classList.toggle('show');
                    e.target.classList.toggle('collapsed');
                }
            }
        });
    }
    
    async loadRoles() {
        try {
            const search = document.getElementById('roleSearch')?.value || '';
            const status = document.getElementById('roleStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/roles?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderRolesTable(result.data);
                this.renderPagination(result.pagination);
            }
        } catch (error) {
            console.error('加载角色列表失败:', error);
        }
    }
    
    renderRolesTable(roles) {
        const tbody = document.getElementById('rolesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = roles.map(role => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input role-checkbox" value="${role._id}">
                </td>
                <td>
                    <div class="d-flex align-items-center">
                        <div class="role-icon me-2">
                            <i class="bi bi-shield-${this.getRoleIcon(role.level)}"></i>
                        </div>
                        <div>
                            <div class="fw-bold">${role.name}</div>
                            <small class="text-muted">等级 ${role.level}</small>
                        </div>
                    </div>
                </td>
                <td>
                    <span class="text-muted">${role.description}</span>
                </td>
                <td>
                    <span class="badge bg-info">${role.permissions.length} 个权限</span>
                </td>
                <td>
                    <span class="badge bg-secondary">${role.userCount} 个用户</span>
                </td>
                <td>
                    <span class="badge bg-${role.status === 'active' ? 'success' : 'secondary'}">
                        ${role.status === 'active' ? '启用' : '禁用'}
                    </span>
                </td>
                <td>${new Date(role.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="roleManager.editRole('${role._id}')" title="编辑">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="roleManager.viewPermissions('${role._id}')" title="查看权限">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-success" onclick="roleManager.copyRole('${role._id}')" title="复制">
                            <i class="bi bi-files"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="roleManager.deleteRole('${role._id}')" title="删除">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadPermissionTree() {
        try {
            const response = await fetch('/api/permissions/tree');
            const result = await response.json();
            
            if (result.success) {
                this.permissionTree = result.data;
            }
        } catch (error) {
            console.error('加载权限树失败:', error);
        }
    }
    
    showAddRoleModal() {
        this.resetRoleForm();
        document.getElementById('roleModalTitle').textContent = '添加角色';
        document.getElementById('roleId').value = '';
        this.renderPermissionTree();
        
        const modal = new bootstrap.Modal(document.getElementById('roleModal'));
        modal.show();
    }
    
    async editRole(id) {
        try {
            const response = await fetch(`/api/roles/${id}`);
            const result = await response.json();
            
            if (result.success) {
                const role = result.data;
                
                document.getElementById('roleModalTitle').textContent = '编辑角色';
                document.getElementById('roleId').value = role._id;
                document.getElementById('roleName').value = role.name;
                document.getElementById('roleDescription').value = role.description;
                document.getElementById('roleLevel').value = role.level;
                document.getElementById('roleStatus').value = role.status;
                
                this.renderPermissionTree(role.permissions);
                
                const modal = new bootstrap.Modal(document.getElementById('roleModal'));
                modal.show();
            }
        } catch (error) {
            console.error('获取角色详情失败:', error);
        }
    }
    
    async saveRole() {
        try {
            const roleId = document.getElementById('roleId').value;
            const formData = {
                name: document.getElementById('roleName').value,
                description: document.getElementById('roleDescription').value,
                level: parseInt(document.getElementById('roleLevel').value),
                status: document.getElementById('roleStatus').value,
                permissions: this.getSelectedPermissions()
            };
            
            const url = roleId ? `/api/roles/${roleId}` : '/api/roles';
            const method = roleId ? 'PUT' : 'POST';
            
            const response = await fetch(url, {
                method,
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message || '操作成功', 'success');
                this.loadRoles();
                
                const modal = bootstrap.Modal.getInstance(document.getElementById('roleModal'));
                modal.hide();
            } else {
                this.showNotification(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('保存角色失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async deleteRole(id) {
        if (!confirm('确定要删除此角色吗？删除后无法恢复。')) return;
        
        try {
            const response = await fetch(`/api/roles/${id}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('角色删除成功', 'success');
                this.loadRoles();
            } else {
                this.showNotification(result.message || '删除失败', 'error');
            }
        } catch (error) {
            console.error('删除角色失败:', error);
            this.showNotification('删除失败', 'error');
        }
    }
    
    async copyRole(id) {
        const newName = prompt('请输入新角色名称：');
        if (!newName) return;
        
        try {
            const response = await fetch(`/api/roles/${id}/copy`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name: newName })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('角色复制成功', 'success');
                this.loadRoles();
            } else {
                this.showNotification(result.message || '复制失败', 'error');
            }
        } catch (error) {
            console.error('复制角色失败:', error);
            this.showNotification('复制失败', 'error');
        }
    }
    
    async viewPermissions(id) {
        try {
            const response = await fetch(`/api/roles/${id}`);
            const result = await response.json();
            
            if (result.success) {
                const role = result.data;
                this.showPermissionsModal(role);
            }
        } catch (error) {
            console.error('获取角色权限失败:', error);
        }
    }
    
    showPermissionsModal(role) {
        const modalHtml = `
            <div class="modal fade" id="permissionsModal" tabindex="-1">
                <div class="modal-dialog modal-lg">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">${role.name} - 权限详情</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <div class="row">
                                <div class="col-md-6">
                                    <h6>基本信息</h6>
                                    <p><strong>角色名称：</strong>${role.name}</p>
                                    <p><strong>描述：</strong>${role.description}</p>
                                    <p><strong>等级：</strong>${role.level}</p>
                                    <p><strong>状态：</strong>
                                        <span class="badge bg-${role.status === 'active' ? 'success' : 'secondary'}">
                                            ${role.status === 'active' ? '启用' : '禁用'}
                                        </span>
                                    </p>
                                </div>
                                <div class="col-md-6">
                                    <h6>权限列表</h6>
                                    <div class="permissions-list">
                                        ${this.renderPermissionsList(role.permissions)}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('permissionsModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('permissionsModal'));
        modal.show();
    }
    
    renderPermissionsList(permissions) {
        const permissionLabels = {};
        
        // 构建权限标签映射
        Object.values(this.permissionTree).forEach(category => {
            Object.assign(permissionLabels, category.permissions);
        });
        
        return permissions.map(permission => {
            const label = permissionLabels[permission] || permission;
            return `<span class="badge bg-primary me-1 mb-1">${label}</span>`;
        }).join('');
    }
    
    renderPermissionTree(selectedPermissions = []) {
        const container = document.getElementById('permissionsContainer');
        if (!container) return;
        
        let html = '';
        
        Object.entries(this.permissionTree).forEach(([categoryKey, category]) => {
            html += `
                <div class="permission-category mb-3">
                    <h6 class="permission-category-title">
                        <input type="checkbox" class="form-check-input category-checkbox me-2" 
                               data-category="${categoryKey}">
                        ${category.label}
                    </h6>
                    <div class="permission-items ms-4">
            `;
            
            Object.entries(category.permissions).forEach(([permKey, permLabel]) => {
                const isChecked = selectedPermissions.includes(permKey);
                html += `
                    <div class="form-check">
                        <input class="form-check-input permission-checkbox" type="checkbox" 
                               value="${permKey}" ${isChecked ? 'checked' : ''} 
                               data-category="${categoryKey}">
                        <label class="form-check-label">${permLabel}</label>
                    </div>
                `;
            });
            
            html += `
                    </div>
                </div>
            `;
        });
        
        container.innerHTML = html;
        
        // 绑定分类复选框事件
        container.querySelectorAll('.category-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const category = e.target.getAttribute('data-category');
                const checked = e.target.checked;
                
                container.querySelectorAll(`[data-category="${category}"].permission-checkbox`).forEach(cb => {
                    cb.checked = checked;
                });
            });
        });
        
        // 更新分类复选框状态
        this.updateCategoryCheckboxes();
    }
    
    updateCategoryCheckboxes() {
        const container = document.getElementById('permissionsContainer');
        if (!container) return;
        
        container.querySelectorAll('.category-checkbox').forEach(categoryCheckbox => {
            const category = categoryCheckbox.getAttribute('data-category');
            const permissionCheckboxes = container.querySelectorAll(`[data-category="${category}"].permission-checkbox`);
            const checkedCount = container.querySelectorAll(`[data-category="${category}"].permission-checkbox:checked`).length;
            
            if (checkedCount === 0) {
                categoryCheckbox.checked = false;
                categoryCheckbox.indeterminate = false;
            } else if (checkedCount === permissionCheckboxes.length) {
                categoryCheckbox.checked = true;
                categoryCheckbox.indeterminate = false;
            } else {
                categoryCheckbox.checked = false;
                categoryCheckbox.indeterminate = true;
            }
        });
    }
    
    getSelectedPermissions() {
        const checkboxes = document.querySelectorAll('.permission-checkbox:checked');
        return Array.from(checkboxes).map(cb => cb.value);
    }
    
    resetRoleForm() {
        document.getElementById('roleForm').reset();
        document.getElementById('roleId').value = '';
    }
    
    updateSelectedRoles(roleId, selected) {
        if (selected) {
            this.selectedRoles.add(roleId);
        } else {
            this.selectedRoles.delete(roleId);
        }
    }
    
    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = this.selectedRoles.size;
        
        if (batchActions) {
            if (selectedCount > 0) {
                batchActions.style.display = 'block';
                batchActions.querySelector('.selected-count').textContent = selectedCount;
            } else {
                batchActions.style.display = 'none';
            }
        }
    }
    
    async batchUpdateStatus(status) {
        if (this.selectedRoles.size === 0) {
            this.showNotification('请先选择要操作的角色', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/api/roles/batch-status', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    roleIds: Array.from(this.selectedRoles),
                    status
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification(result.message, 'success');
                this.loadRoles();
                this.selectedRoles.clear();
                this.updateBatchActions();
            } else {
                this.showNotification(result.message || '批量操作失败', 'error');
            }
        } catch (error) {
            console.error('批量更新状态失败:', error);
            this.showNotification('批量操作失败', 'error');
        }
    }
    
    getRoleIcon(level) {
        if (level >= 9) return 'check';
        if (level >= 7) return 'plus';
        if (level >= 5) return 'fill';
        if (level >= 3) return 'exclamation';
        return '';
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination) {
        const paginationEl = document.getElementById('rolesPagination');
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="roleManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="roleManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="roleManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadRoles();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.roleManager = new RoleManager();
});

// 导出
window.RoleManager = RoleManager;
