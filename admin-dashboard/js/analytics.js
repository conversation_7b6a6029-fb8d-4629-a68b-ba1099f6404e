/**
 * 数据分析页面JavaScript
 * 处理高级数据分析、图表渲染、预测分析等功能
 */

class AnalyticsManager {
    constructor() {
        this.charts = {};
        this.currentData = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadInitialAnalytics();
    }
    
    bindEvents() {
        // 分析按钮
        document.getElementById('analyzeBtn')?.addEventListener('click', () => {
            this.performAnalysis();
        });
        
        // 导出按钮
        document.getElementById('exportBtn')?.addEventListener('click', () => {
            this.exportData();
        });
        
        // 时间范围选择
        document.getElementById('periodSelect')?.addEventListener('change', (e) => {
            if (e.target.value === 'custom') {
                this.showCustomDateModal();
            }
        });
        
        // 分析类型选择
        document.getElementById('analysisType')?.addEventListener('change', () => {
            this.performAnalysis();
        });
        
        // 自定义日期应用
        document.getElementById('applyCustomDate')?.addEventListener('click', () => {
            this.applyCustomDateRange();
        });
    }
    
    async loadInitialAnalytics() {
        await this.performAnalysis();
    }
    
    async performAnalysis() {
        try {
            this.showLoading();
            
            const period = document.getElementById('periodSelect').value;
            const analysisType = document.getElementById('analysisType').value;
            const granularity = document.getElementById('granularity').value;
            
            // 模拟API调用，生成示例数据
            const data = this.generateMockData(analysisType, period);
            
            this.currentData = data;
            this.renderAnalytics(data, analysisType);
            
        } catch (error) {
            console.error('执行分析失败:', error);
            this.showError('分析失败，请稍后重试');
        } finally {
            this.hideLoading();
        }
    }
    
    generateMockData(analysisType, period) {
        // 生成模拟数据
        const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
        
        const userGrowth = [];
        const revenueGrowth = [];
        const apiUsage = [];
        
        for (let i = 0; i < days; i++) {
            const date = new Date();
            date.setDate(date.getDate() - (days - i));
            
            userGrowth.push({
                date: date.toISOString().split('T')[0],
                value: Math.floor(Math.random() * 100) + 50
            });
            
            revenueGrowth.push({
                date: date.toISOString().split('T')[0],
                value: Math.floor(Math.random() * 5000) + 2000
            });
            
            apiUsage.push({
                date: date.toISOString().split('T')[0],
                value: Math.floor(Math.random() * 10000) + 5000
            });
        }
        
        return {
            overview: {
                totalUsers: 15420,
                activeUsers: 12350,
                newUsers: 1250,
                totalRevenue: 2450000,
                monthlyRevenue: 185000,
                totalAgents: 245,
                activeAgents: 198,
                apiCalls: 1250000,
                errorRate: 2.3
            },
            trends: {
                userGrowth,
                revenueGrowth,
                apiUsage
            },
            segments: {
                userSegments: {
                    byRole: {
                        '普通用户': 12500,
                        'VIP用户': 2800,
                        '企业用户': 120
                    }
                },
                revenueSegments: {
                    byPaymentMethod: {
                        '支付宝': 1200000,
                        '微信支付': 980000,
                        '银行卡': 270000
                    }
                }
            },
            predictions: {
                userGrowthPrediction: {
                    nextMonth: 1850,
                    confidence: 0.78
                },
                revenuePrediction: {
                    nextMonth: 195000,
                    confidence: 0.72
                },
                churnPrediction: {
                    predictedChurn: '8.5',
                    riskLevel: 'medium'
                }
            }
        };
    }
    
    renderAnalytics(data, analysisType) {
        // 更新关键指标
        this.updateMetricsCards(data, analysisType);
        
        // 渲染图表
        this.renderCharts(data, analysisType);
        
        // 更新预测分析
        this.updatePredictions(data);
        
        // 更新详细数据表格
        this.updateAnalyticsTable(data, analysisType);
    }
    
    updateMetricsCards(data, analysisType) {
        if (analysisType === 'comprehensive' && data.overview) {
            document.getElementById('totalUsers').textContent = this.formatNumber(data.overview.totalUsers);
            document.getElementById('totalRevenue').textContent = '¥' + this.formatNumber(data.overview.totalRevenue);
            document.getElementById('totalAgents').textContent = this.formatNumber(data.overview.totalAgents);
            document.getElementById('conversionRate').textContent = (data.overview.errorRate || 2.5) + '%';
            
            // 更新增长率
            document.getElementById('userGrowth').textContent = '+12.5%';
            document.getElementById('revenueGrowth').textContent = '****%';
            document.getElementById('agentGrowth').textContent = '+15.2%';
            document.getElementById('conversionGrowth').textContent = '-0.8%';
        }
    }
    
    renderCharts(data, analysisType) {
        // 销毁现有图表
        Object.values(this.charts).forEach(chart => {
            if (chart) chart.destroy();
        });
        
        // 渲染趋势图表
        this.renderTrendChart(data, analysisType);
        
        // 渲染分布图表
        this.renderDistributionChart(data, analysisType);
    }
    
    renderTrendChart(data, analysisType) {
        const ctx = document.getElementById('trendChart');
        if (!ctx) return;
        
        let chartData = {
            labels: [],
            datasets: []
        };
        
        if (analysisType === 'comprehensive' && data.trends) {
            // 综合分析的趋势图
            const userGrowth = data.trends.userGrowth || [];
            const revenueGrowth = data.trends.revenueGrowth || [];
            
            chartData = {
                labels: userGrowth.map(item => item.date),
                datasets: [
                    {
                        label: '用户增长',
                        data: userGrowth.map(item => item.value),
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    },
                    {
                        label: '收入增长',
                        data: revenueGrowth.map(item => item.value),
                        borderColor: 'rgb(255, 99, 132)',
                        backgroundColor: 'rgba(255, 99, 132, 0.2)',
                        tension: 0.1,
                        yAxisID: 'y1'
                    }
                ]
            };
        }
        
        this.charts.trend = new Chart(ctx, {
            type: 'line',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                interaction: {
                    mode: 'index',
                    intersect: false,
                },
                scales: {
                    x: {
                        display: true,
                        title: {
                            display: true,
                            text: '时间'
                        }
                    },
                    y: {
                        type: 'linear',
                        display: true,
                        position: 'left',
                        title: {
                            display: true,
                            text: '用户数'
                        }
                    },
                    y1: {
                        type: 'linear',
                        display: true,
                        position: 'right',
                        title: {
                            display: true,
                            text: '收入 (¥)'
                        },
                        grid: {
                            drawOnChartArea: false,
                        },
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                    },
                    title: {
                        display: true,
                        text: '趋势分析图表'
                    }
                }
            }
        });
    }
    
    renderDistributionChart(data, analysisType) {
        const ctx = document.getElementById('distributionChart');
        if (!ctx) return;
        
        let chartData = {
            labels: [],
            datasets: []
        };
        
        if (analysisType === 'comprehensive' && data.segments) {
            // 用户角色分布
            const userSegments = data.segments.userSegments || {};
            const roleData = userSegments.byRole || {};
            
            chartData = {
                labels: Object.keys(roleData),
                datasets: [{
                    label: '用户分布',
                    data: Object.values(roleData),
                    backgroundColor: [
                        'rgba(255, 99, 132, 0.8)',
                        'rgba(54, 162, 235, 0.8)',
                        'rgba(255, 205, 86, 0.8)',
                        'rgba(75, 192, 192, 0.8)',
                        'rgba(153, 102, 255, 0.8)',
                        'rgba(255, 159, 64, 0.8)'
                    ],
                    borderWidth: 1
                }]
            };
        }
        
        this.charts.distribution = new Chart(ctx, {
            type: 'doughnut',
            data: chartData,
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                    },
                    title: {
                        display: true,
                        text: '分布分析图表'
                    }
                }
            }
        });
    }
    
    updatePredictions(data) {
        if (data.predictions) {
            const predictions = data.predictions;
            
            // 用户增长预测
            if (predictions.userGrowthPrediction) {
                document.getElementById('userPrediction').textContent = 
                    '+' + this.formatNumber(predictions.userGrowthPrediction.nextMonth);
                document.getElementById('userConfidence').textContent = 
                    Math.round(predictions.userGrowthPrediction.confidence * 100) + '%';
            }
            
            // 收入预测
            if (predictions.revenuePrediction) {
                document.getElementById('revenuePrediction').textContent = 
                    '¥' + this.formatNumber(predictions.revenuePrediction.nextMonth);
                document.getElementById('revenueConfidence').textContent = 
                    Math.round(predictions.revenuePrediction.confidence * 100) + '%';
            }
            
            // 流失率预测
            if (predictions.churnPrediction) {
                document.getElementById('churnPrediction').textContent = 
                    predictions.churnPrediction.predictedChurn + '%';
                
                const riskBadge = document.getElementById('churnRisk');
                const riskLevel = predictions.churnPrediction.riskLevel;
                riskBadge.textContent = this.getRiskLevelText(riskLevel);
                riskBadge.className = `badge bg-${this.getRiskLevelColor(riskLevel)}`;
            }
        }
    }
    
    updateAnalyticsTable(data, analysisType) {
        const header = document.getElementById('analyticsTableHeader');
        const body = document.getElementById('analyticsTableBody');
        
        if (!header || !body) return;
        
        // 根据分析类型生成不同的表格
        let tableData = [];
        let columns = [];
        
        switch (analysisType) {
            case 'comprehensive':
                if (data.trends && data.trends.userGrowth) {
                    columns = ['日期', '新增用户', '收入', 'API调用'];
                    tableData = data.trends.userGrowth.slice(-10).map((item, index) => ({
                        date: item.date,
                        users: item.value,
                        revenue: data.trends.revenueGrowth[data.trends.revenueGrowth.length - 10 + index]?.value || 0,
                        apiCalls: data.trends.apiUsage[data.trends.apiUsage.length - 10 + index]?.value || 0
                    }));
                }
                break;
            default:
                columns = ['指标', '数值', '变化'];
                tableData = [
                    { metric: '总用户数', value: data.overview?.totalUsers || 0, change: '+12.5%' },
                    { metric: '总收入', value: '¥' + this.formatNumber(data.overview?.totalRevenue || 0), change: '****%' },
                    { metric: '活跃用户', value: data.overview?.activeUsers || 0, change: '****%' }
                ];
        }
        
        // 渲染表头
        header.innerHTML = columns.map(col => `<th>${col}</th>`).join('');
        
        // 渲染表格内容
        body.innerHTML = tableData.map(row => {
            const cells = Object.values(row).map(value => `<td>${value}</td>`).join('');
            return `<tr>${cells}</tr>`;
        }).join('');
    }
    
    showCustomDateModal() {
        const modal = new bootstrap.Modal(document.getElementById('customDateModal'));
        modal.show();
    }
    
    applyCustomDateRange() {
        const startDate = document.getElementById('startDate').value;
        const endDate = document.getElementById('endDate').value;
        
        if (!startDate || !endDate) {
            alert('请选择开始和结束日期');
            return;
        }
        
        if (new Date(startDate) >= new Date(endDate)) {
            alert('开始日期必须早于结束日期');
            return;
        }
        
        const modal = bootstrap.Modal.getInstance(document.getElementById('customDateModal'));
        modal.hide();
        
        this.performAnalysis();
    }
    
    async exportData() {
        if (!this.currentData) {
            alert('没有可导出的数据');
            return;
        }
        
        try {
            // 模拟导出功能
            const analysisType = document.getElementById('analysisType').value;
            const csvContent = this.convertToCSV(this.currentData);
            
            const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `analytics_${analysisType}_${new Date().toISOString().split('T')[0]}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
            
        } catch (error) {
            console.error('导出数据失败:', error);
            alert('导出失败，请稍后重试');
        }
    }
    
    convertToCSV(data) {
        let csv = '指标,数值\n';
        
        if (data.overview) {
            csv += `总用户数,${data.overview.totalUsers}\n`;
            csv += `活跃用户,${data.overview.activeUsers}\n`;
            csv += `新用户,${data.overview.newUsers}\n`;
            csv += `总收入,${data.overview.totalRevenue}\n`;
            csv += `月收入,${data.overview.monthlyRevenue}\n`;
            csv += `总代理商,${data.overview.totalAgents}\n`;
            csv += `活跃代理商,${data.overview.activeAgents}\n`;
            csv += `API调用,${data.overview.apiCalls}\n`;
            csv += `错误率,${data.overview.errorRate}%\n`;
        }
        
        return csv;
    }
    
    formatNumber(num) {
        if (num >= 1000000) {
            return (num / 1000000).toFixed(1) + 'M';
        } else if (num >= 1000) {
            return (num / 1000).toFixed(1) + 'K';
        } else {
            return num.toString();
        }
    }
    
    getRiskLevelText(level) {
        const texts = {
            'low': '低风险',
            'medium': '中风险',
            'high': '高风险'
        };
        return texts[level] || level;
    }
    
    getRiskLevelColor(level) {
        const colors = {
            'low': 'success',
            'medium': 'warning',
            'high': 'danger'
        };
        return colors[level] || 'secondary';
    }
    
    showLoading() {
        document.getElementById('analyzeBtn').disabled = true;
        document.getElementById('analyzeBtn').innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>分析中...';
    }
    
    hideLoading() {
        document.getElementById('analyzeBtn').disabled = false;
        document.getElementById('analyzeBtn').innerHTML = '<i class="bi bi-play-fill"></i> 开始分析';
    }
    
    showError(message) {
        const alert = document.createElement('div');
        alert.className = 'alert alert-danger alert-dismissible fade show position-fixed';
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.analyticsManager = new AnalyticsManager();
});

// 导出
window.AnalyticsManager = AnalyticsManager;
