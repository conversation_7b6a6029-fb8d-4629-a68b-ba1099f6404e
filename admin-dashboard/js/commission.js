/**
 * 佣金管理JavaScript
 * 处理代理商佣金计算、结算、提现等功能
 */

class CommissionManager {
    constructor() {
        this.commissionRecords = [];
        this.withdrawRequests = [];
        this.commissionConfig = null;
        this.selectedRecords = new Set();
        this.currentPage = 1;
        this.pageSize = 20;
        this.trendChart = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadCommissionConfig();
        this.loadCommissionStats();
        this.loadCommissionRecords();
        this.loadWithdrawRequests();
        this.initChart();
    }
    
    bindEvents() {
        // 标签页切换
        document.querySelectorAll('#commissionTabs button').forEach(tab => {
            tab.addEventListener('shown.bs.tab', (e) => {
                if (e.target.id === 'overview-tab') {
                    this.refreshChart();
                }
            });
        });
        
        // 批量结算
        document.getElementById('batchSettleBtn')?.addEventListener('click', () => {
            this.batchSettleCommissions();
        });
        
        document.getElementById('batchSettleSelectedBtn')?.addEventListener('click', () => {
            this.batchSettleSelected();
        });
        
        // 全选/取消全选
        document.getElementById('selectAll')?.addEventListener('change', (e) => {
            this.toggleSelectAll(e.target.checked);
        });
        
        // 清除选择
        document.getElementById('clearSelectionBtn')?.addEventListener('click', () => {
            this.clearSelection();
        });
        
        // 搜索
        document.getElementById('searchRecordsBtn')?.addEventListener('click', () => {
            this.searchCommissionRecords();
        });
        
        document.getElementById('searchWithdrawBtn')?.addEventListener('click', () => {
            this.searchWithdrawRequests();
        });
        
        // 计算佣金
        document.getElementById('calculateCommissionBtn')?.addEventListener('click', () => {
            this.showCalculateCommissionModal();
        });
        
        // 添加佣金等级
        document.getElementById('addLevelBtn')?.addEventListener('click', () => {
            this.addCommissionLevel();
        });
        
        // 保存结算配置
        document.getElementById('settlementConfigForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveSettlementConfig();
        });
        
        // 处理提现申请
        document.getElementById('confirmProcessBtn')?.addEventListener('click', () => {
            this.processWithdrawRequest();
        });
        
        // 处理动作变化
        document.getElementById('processAction')?.addEventListener('change', (e) => {
            this.toggleProcessedAmountField(e.target.value);
        });
    }
    
    async loadCommissionConfig() {
        try {
            // 模拟API调用
            this.commissionConfig = {
                levels: [
                    { level: 1, name: '普通代理', rate: 0.10, minSales: 0, maxSales: 50000 },
                    { level: 2, name: '高级代理', rate: 0.15, minSales: 50000, maxSales: 200000 },
                    { level: 3, name: '区域代理', rate: 0.20, minSales: 200000, maxSales: 1000000 },
                    { level: 4, name: '总代理', rate: 0.25, minSales: 1000000, maxSales: null }
                ],
                settlementCycle: 'monthly',
                settlementDay: 1,
                minWithdraw: 100,
                maxWithdraw: 50000,
                withdrawFee: 0.01,
                autoSettle: true
            };
            
            this.renderCommissionLevels();
            this.fillSettlementConfig();
        } catch (error) {
            console.error('加载佣金配置失败:', error);
        }
    }
    
    async loadCommissionStats() {
        try {
            // 模拟统计数据
            const stats = {
                totalCommission: 125680.50,
                pendingCommission: 23450.80,
                paidCommission: 102229.70,
                avgCommissionRate: 0.165,
                monthlyTrend: this.generateMonthlyTrend(),
                topAgents: this.generateTopAgents()
            };
            
            this.updateStatsDisplay(stats);
            this.updateTrendChart(stats.monthlyTrend);
            this.renderTopAgents(stats.topAgents);
        } catch (error) {
            console.error('加载佣金统计失败:', error);
        }
    }
    
    async loadCommissionRecords() {
        try {
            // 生成模拟佣金记录
            this.commissionRecords = this.generateMockCommissionRecords();
            this.renderCommissionRecords();
        } catch (error) {
            console.error('加载佣金记录失败:', error);
        }
    }
    
    async loadWithdrawRequests() {
        try {
            // 生成模拟提现申请
            this.withdrawRequests = this.generateMockWithdrawRequests();
            this.renderWithdrawRequests();
        } catch (error) {
            console.error('加载提现申请失败:', error);
        }
    }
    
    generateMockCommissionRecords() {
        const records = [];
        const statuses = ['pending', 'settled', 'paid'];
        const agents = ['张三', '李四', '王五', '赵六', '钱七'];
        
        for (let i = 0; i < 50; i++) {
            const orderAmount = Math.floor(Math.random() * 5000) + 500;
            const rate = 0.1 + Math.random() * 0.15;
            const commissionAmount = orderAmount * rate;
            
            records.push({
                id: `comm_${Date.now()}_${i}`,
                orderId: `ORD${String(i + 1).padStart(6, '0')}`,
                agentId: `agent_${i % 5}`,
                agentName: agents[i % 5],
                orderAmount: orderAmount,
                rate: rate,
                amount: commissionAmount,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                createdAt: new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000),
                settledAt: Math.random() > 0.5 ? new Date() : null
            });
        }
        
        return records.sort((a, b) => b.createdAt - a.createdAt);
    }
    
    generateMockWithdrawRequests() {
        const requests = [];
        const statuses = ['pending', 'approved', 'rejected'];
        const agents = ['张三', '李四', '王五', '赵六', '钱七'];
        
        for (let i = 0; i < 20; i++) {
            const amount = Math.floor(Math.random() * 10000) + 1000;
            
            requests.push({
                id: `withdraw_${Date.now()}_${i}`,
                agentId: `agent_${i % 5}`,
                agentName: agents[i % 5],
                amount: amount,
                status: statuses[Math.floor(Math.random() * statuses.length)],
                bankAccount: `****${String(Math.floor(Math.random() * 10000)).padStart(4, '0')}`,
                createdAt: new Date(Date.now() - Math.random() * 15 * 24 * 60 * 60 * 1000),
                processedAt: Math.random() > 0.6 ? new Date() : null,
                reason: ''
            });
        }
        
        return requests.sort((a, b) => b.createdAt - a.createdAt);
    }
    
    generateMonthlyTrend() {
        const months = [];
        for (let i = 11; i >= 0; i--) {
            const date = new Date();
            date.setMonth(date.getMonth() - i);
            months.push({
                month: date.toISOString().substr(0, 7),
                amount: Math.floor(Math.random() * 20000) + 5000,
                count: Math.floor(Math.random() * 100) + 20
            });
        }
        return months;
    }
    
    generateTopAgents() {
        const agents = ['张三', '李四', '王五', '赵六', '钱七'];
        return agents.map((name, index) => ({
            agentId: `agent_${index}`,
            agentName: name,
            totalCommission: Math.floor(Math.random() * 50000) + 10000,
            orderCount: Math.floor(Math.random() * 200) + 50
        })).sort((a, b) => b.totalCommission - a.totalCommission);
    }
    
    updateStatsDisplay(stats) {
        document.getElementById('totalCommission').textContent = '¥' + this.formatNumber(stats.totalCommission);
        document.getElementById('pendingCommission').textContent = '¥' + this.formatNumber(stats.pendingCommission);
        document.getElementById('paidCommission').textContent = '¥' + this.formatNumber(stats.paidCommission);
        document.getElementById('avgCommissionRate').textContent = (stats.avgCommissionRate * 100).toFixed(1) + '%';
    }
    
    initChart() {
        const ctx = document.getElementById('commissionTrendChart');
        if (!ctx) return;
        
        this.trendChart = new Chart(ctx, {
            type: 'line',
            data: {
                labels: [],
                datasets: [{
                    label: '佣金金额',
                    data: [],
                    borderColor: 'rgb(75, 192, 192)',
                    backgroundColor: 'rgba(75, 192, 192, 0.2)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return '¥' + value.toLocaleString();
                            }
                        }
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    }
                }
            }
        });
    }
    
    updateTrendChart(trendData) {
        if (!this.trendChart) return;
        
        this.trendChart.data.labels = trendData.map(item => item.month);
        this.trendChart.data.datasets[0].data = trendData.map(item => item.amount);
        this.trendChart.update();
    }
    
    refreshChart() {
        if (this.trendChart) {
            this.trendChart.resize();
        }
    }
    
    renderTopAgents(topAgents) {
        const container = document.getElementById('topAgentsList');
        if (!container) return;
        
        container.innerHTML = topAgents.map((agent, index) => `
            <div class="d-flex justify-content-between align-items-center mb-2 p-2 ${index < 3 ? 'bg-light rounded' : ''}">
                <div>
                    <strong>${agent.agentName}</strong>
                    <small class="text-muted d-block">${agent.orderCount} 笔订单</small>
                </div>
                <div class="text-end">
                    <strong class="text-success">¥${this.formatNumber(agent.totalCommission)}</strong>
                </div>
            </div>
        `).join('');
    }
    
    renderCommissionRecords() {
        const tbody = document.getElementById('commissionRecordsTable');
        if (!tbody) return;
        
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageRecords = this.commissionRecords.slice(startIndex, endIndex);
        
        tbody.innerHTML = pageRecords.map(record => `
            <tr>
                <td>
                    <input type="checkbox" class="form-check-input record-checkbox" 
                           value="${record.id}" ${this.selectedRecords.has(record.id) ? 'checked' : ''}>
                </td>
                <td>${record.orderId}</td>
                <td>${record.agentName}</td>
                <td>¥${this.formatNumber(record.orderAmount)}</td>
                <td>${(record.rate * 100).toFixed(1)}%</td>
                <td>¥${this.formatNumber(record.amount)}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(record.status)}">
                        ${this.getStatusText(record.status)}
                    </span>
                </td>
                <td>${record.createdAt.toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        ${record.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="commissionManager.settleCommission('${record.id}')" title="结算">
                                <i class="bi bi-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-info" onclick="commissionManager.viewCommissionDetail('${record.id}')" title="详情">
                            <i class="bi bi-eye"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
        
        // 绑定复选框事件
        tbody.querySelectorAll('.record-checkbox').forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                this.toggleRecordSelection(e.target.value, e.target.checked);
            });
        });
        
        this.renderPagination();
    }
    
    renderWithdrawRequests() {
        const container = document.getElementById('withdrawRequestsList');
        if (!container) return;
        
        container.innerHTML = this.withdrawRequests.map(request => `
            <div class="card mb-3">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-3">
                            <h6 class="mb-1">${request.agentName}</h6>
                            <small class="text-muted">代理商ID: ${request.agentId}</small>
                        </div>
                        <div class="col-md-2">
                            <strong class="text-primary">¥${this.formatNumber(request.amount)}</strong>
                        </div>
                        <div class="col-md-2">
                            <span class="withdraw-status ${request.status}">
                                ${this.getWithdrawStatusText(request.status)}
                            </span>
                        </div>
                        <div class="col-md-3">
                            <small class="text-muted">
                                申请时间: ${request.createdAt.toLocaleDateString()}<br>
                                银行账户: ${request.bankAccount}
                            </small>
                        </div>
                        <div class="col-md-2">
                            ${request.status === 'pending' ? `
                                <button class="btn btn-success btn-sm me-1" onclick="commissionManager.processWithdraw('${request.id}', 'approve')">
                                    批准
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="commissionManager.processWithdraw('${request.id}', 'reject')">
                                    拒绝
                                </button>
                            ` : `
                                <button class="btn btn-outline-info btn-sm" onclick="commissionManager.viewWithdrawDetail('${request.id}')">
                                    详情
                                </button>
                            `}
                        </div>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    renderCommissionLevels() {
        const container = document.getElementById('commissionLevels');
        if (!container || !this.commissionConfig) return;
        
        container.innerHTML = this.commissionConfig.levels.map((level, index) => `
            <div class="commission-level mb-3">
                <div class="row align-items-center">
                    <div class="col-md-3">
                        <input type="text" class="form-control" value="${level.name}" 
                               onchange="commissionManager.updateLevelName(${index}, this.value)">
                    </div>
                    <div class="col-md-2">
                        <div class="input-group">
                            <input type="number" class="form-control" value="${(level.rate * 100).toFixed(1)}" 
                                   step="0.1" min="0" max="100"
                                   onchange="commissionManager.updateLevelRate(${index}, this.value)">
                            <span class="input-group-text">%</span>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" value="${level.minSales}" 
                                   onchange="commissionManager.updateLevelMinSales(${index}, this.value)">
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="input-group">
                            <span class="input-group-text">¥</span>
                            <input type="number" class="form-control" value="${level.maxSales || ''}" 
                                   placeholder="无上限"
                                   onchange="commissionManager.updateLevelMaxSales(${index}, this.value)">
                        </div>
                    </div>
                    <div class="col-md-1">
                        <button class="btn btn-outline-danger btn-sm" onclick="commissionManager.removeLevel(${index})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </div>
            </div>
        `).join('');
    }
    
    fillSettlementConfig() {
        if (!this.commissionConfig) return;
        
        document.getElementById('settlementCycle').value = this.commissionConfig.settlementCycle;
        document.getElementById('settlementDay').value = this.commissionConfig.settlementDay;
        document.getElementById('minWithdraw').value = this.commissionConfig.minWithdraw;
        document.getElementById('maxWithdraw').value = this.commissionConfig.maxWithdraw;
        document.getElementById('withdrawFee').value = this.commissionConfig.withdrawFee * 100;
        document.getElementById('autoSettle').checked = this.commissionConfig.autoSettle;
    }
    
    toggleRecordSelection(recordId, selected) {
        if (selected) {
            this.selectedRecords.add(recordId);
        } else {
            this.selectedRecords.delete(recordId);
        }
        
        this.updateBatchActions();
        this.updateSelectAllState();
    }
    
    toggleSelectAll(selectAll) {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.toggleRecordSelection(checkbox.value, selectAll);
        });
    }
    
    updateSelectAllState() {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        const selectAllCheckbox = document.getElementById('selectAll');
        
        if (checkboxes.length === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else {
            const checkedCount = Array.from(checkboxes).filter(cb => cb.checked).length;
            
            if (checkedCount === 0) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = false;
            } else if (checkedCount === checkboxes.length) {
                selectAllCheckbox.indeterminate = false;
                selectAllCheckbox.checked = true;
            } else {
                selectAllCheckbox.indeterminate = true;
                selectAllCheckbox.checked = false;
            }
        }
    }
    
    updateBatchActions() {
        const batchActions = document.getElementById('batchActions');
        const selectedCount = document.getElementById('selectedCount');
        
        if (this.selectedRecords.size > 0) {
            batchActions.classList.add('show');
            selectedCount.textContent = this.selectedRecords.size;
        } else {
            batchActions.classList.remove('show');
        }
    }
    
    clearSelection() {
        this.selectedRecords.clear();
        document.querySelectorAll('.record-checkbox').forEach(checkbox => {
            checkbox.checked = false;
        });
        this.updateBatchActions();
        this.updateSelectAllState();
    }
    
    async batchSettleSelected() {
        if (this.selectedRecords.size === 0) {
            this.showNotification('请选择要结算的记录', 'warning');
            return;
        }
        
        if (!confirm(`确定要结算选中的 ${this.selectedRecords.size} 条记录吗？`)) {
            return;
        }
        
        try {
            // 模拟批量结算
            const selectedIds = Array.from(this.selectedRecords);
            let successCount = 0;
            
            selectedIds.forEach(id => {
                const record = this.commissionRecords.find(r => r.id === id);
                if (record && record.status === 'pending') {
                    record.status = 'settled';
                    record.settledAt = new Date();
                    successCount++;
                }
            });
            
            this.clearSelection();
            this.renderCommissionRecords();
            this.showNotification(`成功结算 ${successCount} 条记录`, 'success');
        } catch (error) {
            console.error('批量结算失败:', error);
            this.showNotification('批量结算失败', 'error');
        }
    }
    
    processWithdraw(requestId, action) {
        const request = this.withdrawRequests.find(r => r.id === requestId);
        if (!request) return;
        
        // 填充模态框信息
        document.getElementById('processRequestId').value = requestId;
        document.getElementById('processAction').value = action;
        document.getElementById('processedAmount').value = request.amount;
        
        const requestInfo = document.getElementById('withdrawRequestInfo');
        requestInfo.innerHTML = `
            <div class="alert alert-info">
                <h6>提现申请信息</h6>
                <p><strong>代理商:</strong> ${request.agentName}</p>
                <p><strong>申请金额:</strong> ¥${this.formatNumber(request.amount)}</p>
                <p><strong>银行账户:</strong> ${request.bankAccount}</p>
                <p><strong>申请时间:</strong> ${request.createdAt.toLocaleString()}</p>
            </div>
        `;
        
        this.toggleProcessedAmountField(action);
        
        const modal = new bootstrap.Modal(document.getElementById('processWithdrawModal'));
        modal.show();
    }
    
    toggleProcessedAmountField(action) {
        const amountGroup = document.getElementById('processedAmountGroup');
        if (action === 'approve') {
            amountGroup.style.display = 'block';
        } else {
            amountGroup.style.display = 'none';
        }
    }
    
    async processWithdrawRequest() {
        const requestId = document.getElementById('processRequestId').value;
        const action = document.getElementById('processAction').value;
        const reason = document.getElementById('processReason').value;
        const processedAmount = document.getElementById('processedAmount').value;
        
        if (!action) {
            this.showNotification('请选择处理结果', 'warning');
            return;
        }
        
        try {
            const request = this.withdrawRequests.find(r => r.id === requestId);
            if (request) {
                request.status = action === 'approve' ? 'approved' : 'rejected';
                request.processedAt = new Date();
                request.reason = reason;
                if (action === 'approve' && processedAmount) {
                    request.processedAmount = parseFloat(processedAmount);
                }
            }
            
            this.renderWithdrawRequests();
            
            const modal = bootstrap.Modal.getInstance(document.getElementById('processWithdrawModal'));
            modal.hide();
            
            this.showNotification(`提现申请${action === 'approve' ? '批准' : '拒绝'}成功`, 'success');
        } catch (error) {
            console.error('处理提现申请失败:', error);
            this.showNotification('处理失败', 'error');
        }
    }
    
    renderPagination() {
        const pagination = document.getElementById('recordsPagination');
        if (!pagination) return;
        
        const totalPages = Math.ceil(this.commissionRecords.length / this.pageSize);
        
        let paginationHTML = '';
        
        // 上一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="commissionManager.goToPage(${this.currentPage - 1})">上一页</a>
            </li>
        `;
        
        // 页码
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHTML += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="commissionManager.goToPage(${i})">${i}</a>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHTML += '<li class="page-item disabled"><span class="page-link">...</span></li>';
            }
        }
        
        // 下一页
        paginationHTML += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <a class="page-link" href="#" onclick="commissionManager.goToPage(${this.currentPage + 1})">下一页</a>
            </li>
        `;
        
        pagination.innerHTML = paginationHTML;
    }
    
    goToPage(page) {
        const totalPages = Math.ceil(this.commissionRecords.length / this.pageSize);
        if (page < 1 || page > totalPages) return;
        
        this.currentPage = page;
        this.renderCommissionRecords();
    }
    
    // 辅助方法
    formatNumber(num) {
        return num.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
    }
    
    getStatusText(status) {
        const texts = {
            'pending': '待结算',
            'settled': '已结算',
            'paid': '已支付'
        };
        return texts[status] || status;
    }
    
    getStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'settled': 'info',
            'paid': 'success'
        };
        return colors[status] || 'secondary';
    }
    
    getWithdrawStatusText(status) {
        const texts = {
            'pending': '待审核',
            'approved': '已批准',
            'rejected': '已拒绝'
        };
        return texts[status] || status;
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.commissionManager = new CommissionManager();
});

// 导出
window.CommissionManager = CommissionManager;
