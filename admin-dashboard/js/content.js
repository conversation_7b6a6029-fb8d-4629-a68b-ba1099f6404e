/**
 * 内容管理页面JavaScript
 * 处理协议管理、公告管理、客服二维码、页面内容编辑等功能
 */

class ContentManager {
    constructor() {
        this.currentTab = 'agreements';
        this.currentPage = 1;
        this.pageSize = 20;
        this.editor = null;
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadAgreements();
        this.loadAnnouncements();
        this.loadQRCodeSettings();
        this.loadPages();
        this.initEditor();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#contentTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 搜索和筛选
        document.getElementById('agreementSearchBtn')?.addEventListener('click', () => {
            this.loadAgreements();
        });
        
        document.getElementById('announcementSearchBtn')?.addEventListener('click', () => {
            this.loadAnnouncements();
        });
        
        document.getElementById('agreementStatusFilter')?.addEventListener('change', () => {
            this.loadAgreements();
        });
        
        document.getElementById('announcementStatusFilter')?.addEventListener('change', () => {
            this.loadAnnouncements();
        });
        
        // 表单提交
        document.getElementById('qrcodeForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveQRCodeSettings();
        });
        
        // 添加按钮
        document.getElementById('addAgreementBtn')?.addEventListener('click', () => {
            this.showAddAgreementModal();
        });
        
        document.getElementById('addAnnouncementBtn')?.addEventListener('click', () => {
            this.showAddAnnouncementModal();
        });
        
        // 模态框表单提交
        document.getElementById('agreementForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAgreement();
        });
        
        document.getElementById('announcementForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveAnnouncement();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'agreements':
                this.loadAgreements();
                break;
            case 'announcements':
                this.loadAnnouncements();
                break;
            case 'qrcode':
                this.loadQRCodeSettings();
                break;
            case 'pages':
                this.loadPages();
                break;
        }
    }
    
    async loadAgreements() {
        try {
            const search = document.getElementById('agreementSearch')?.value || '';
            const status = document.getElementById('agreementStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/content/agreements?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderAgreementsTable(result.data);
                this.renderPagination(result.pagination, 'agreementsPagination');
            }
        } catch (error) {
            console.error('加载协议列表失败:', error);
        }
    }
    
    renderAgreementsTable(agreements) {
        const tbody = document.getElementById('agreementsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = agreements.map(agreement => `
            <tr>
                <td>${agreement.title}</td>
                <td>${agreement.type}</td>
                <td>${agreement.version}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(agreement.status)}">
                        ${this.getStatusText(agreement.status)}
                    </span>
                </td>
                <td>${new Date(agreement.createdAt).toLocaleDateString()}</td>
                <td>${agreement.updatedAt ? new Date(agreement.updatedAt).toLocaleDateString() : '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="contentManager.editAgreement('${agreement._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="contentManager.viewAgreement('${agreement._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${agreement.status === 'draft' ? `
                            <button class="btn btn-outline-success" onclick="contentManager.publishAgreement('${agreement._id}')">
                                <i class="bi bi-check"></i>
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="contentManager.deleteAgreement('${agreement._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadAnnouncements() {
        try {
            const search = document.getElementById('announcementSearch')?.value || '';
            const status = document.getElementById('announcementStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/content/announcements?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderAnnouncementsTable(result.data);
                this.renderPagination(result.pagination, 'announcementsPagination');
            }
        } catch (error) {
            console.error('加载公告列表失败:', error);
        }
    }
    
    renderAnnouncementsTable(announcements) {
        const tbody = document.getElementById('announcementsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = announcements.map(announcement => `
            <tr>
                <td>${announcement.title}</td>
                <td>
                    <span class="badge bg-${this.getPriorityColor(announcement.priority)}">
                        ${this.getPriorityText(announcement.priority)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getStatusColor(announcement.status)}">
                        ${this.getStatusText(announcement.status)}
                    </span>
                </td>
                <td>${announcement.publishedAt ? new Date(announcement.publishedAt).toLocaleDateString() : '-'}</td>
                <td>${new Date(announcement.createdAt).toLocaleDateString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="contentManager.editAnnouncement('${announcement._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="contentManager.viewAnnouncement('${announcement._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${announcement.status === 'draft' ? `
                            <button class="btn btn-outline-success" onclick="contentManager.publishAnnouncement('${announcement._id}')">
                                <i class="bi bi-check"></i> 发布
                            </button>
                        ` : announcement.status === 'published' ? `
                            <button class="btn btn-outline-warning" onclick="contentManager.unpublishAnnouncement('${announcement._id}')">
                                <i class="bi bi-x"></i> 下架
                            </button>
                        ` : ''}
                        <button class="btn btn-outline-danger" onclick="contentManager.deleteAnnouncement('${announcement._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadQRCodeSettings() {
        try {
            const response = await fetch('/api/content/qrcode-settings');
            const result = await response.json();
            
            if (result.success) {
                this.populateQRCodeForm(result.data);
            }
        } catch (error) {
            console.error('加载二维码设置失败:', error);
        }
    }
    
    populateQRCodeForm(settings) {
        document.getElementById('customerServiceQR').value = settings.customerServiceQR || '';
        document.getElementById('wechatGroupQR').value = settings.wechatGroupQR || '';
        document.getElementById('officialAccountQR').value = settings.officialAccountQR || '';
        document.getElementById('appDownloadQR').value = settings.appDownloadQR || '';
    }
    
    async saveQRCodeSettings() {
        try {
            const formData = {
                customerServiceQR: document.getElementById('customerServiceQR').value,
                wechatGroupQR: document.getElementById('wechatGroupQR').value,
                officialAccountQR: document.getElementById('officialAccountQR').value,
                appDownloadQR: document.getElementById('appDownloadQR').value
            };
            
            const response = await fetch('/api/content/qrcode-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('二维码设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存二维码设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadPages() {
        try {
            const response = await fetch('/api/content/pages');
            const result = await response.json();
            
            if (result.success) {
                this.renderPagesTable(result.data);
            }
        } catch (error) {
            console.error('加载页面列表失败:', error);
        }
    }
    
    renderPagesTable(pages) {
        const tbody = document.getElementById('pagesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = pages.map(page => `
            <tr>
                <td>${page.title}</td>
                <td>${page.slug}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(page.status)}">
                        ${this.getStatusText(page.status)}
                    </span>
                </td>
                <td>${page.updatedAt ? new Date(page.updatedAt).toLocaleDateString() : '-'}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="contentManager.editPage('${page._id}')">
                            <i class="bi bi-pencil"></i> 编辑
                        </button>
                        <button class="btn btn-outline-info" onclick="contentManager.previewPage('${page._id}')">
                            <i class="bi bi-eye"></i> 预览
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async publishAgreement(id) {
        try {
            const response = await fetch(`/api/content/agreements/${id}/publish`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('协议发布成功', 'success');
                this.loadAgreements();
            } else {
                this.showNotification(result.message || '发布失败', 'error');
            }
        } catch (error) {
            console.error('发布协议失败:', error);
            this.showNotification('发布失败', 'error');
        }
    }
    
    async publishAnnouncement(id) {
        try {
            const response = await fetch(`/api/content/announcements/${id}/publish`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('公告发布成功', 'success');
                this.loadAnnouncements();
            } else {
                this.showNotification(result.message || '发布失败', 'error');
            }
        } catch (error) {
            console.error('发布公告失败:', error);
            this.showNotification('发布失败', 'error');
        }
    }
    
    async unpublishAnnouncement(id) {
        try {
            const response = await fetch(`/api/content/announcements/${id}/unpublish`, {
                method: 'POST'
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('公告下架成功', 'success');
                this.loadAnnouncements();
            } else {
                this.showNotification(result.message || '下架失败', 'error');
            }
        } catch (error) {
            console.error('下架公告失败:', error);
            this.showNotification('下架失败', 'error');
        }
    }
    
    initEditor() {
        // 初始化富文本编辑器（这里使用简单的textarea，实际项目中可以集成CKEditor、TinyMCE等）
        this.editor = {
            setContent: (content) => {
                const textarea = document.getElementById('editorContent');
                if (textarea) textarea.value = content;
            },
            getContent: () => {
                const textarea = document.getElementById('editorContent');
                return textarea ? textarea.value : '';
            }
        };
    }
    
    getStatusColor(status) {
        const colors = {
            'draft': 'secondary',
            'published': 'success',
            'unpublished': 'warning',
            'archived': 'dark'
        };
        return colors[status] || 'secondary';
    }
    
    getStatusText(status) {
        const texts = {
            'draft': '草稿',
            'published': '已发布',
            'unpublished': '已下架',
            'archived': '已归档'
        };
        return texts[status] || status;
    }
    
    getPriorityColor(priority) {
        const colors = {
            'low': 'info',
            'normal': 'secondary',
            'high': 'warning',
            'urgent': 'danger'
        };
        return colors[priority] || 'secondary';
    }
    
    getPriorityText(priority) {
        const texts = {
            'low': '低',
            'normal': '普通',
            'high': '高',
            'urgent': '紧急'
        };
        return texts[priority] || priority;
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination, containerId) {
        const paginationEl = document.getElementById(containerId);
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="contentManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="contentManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="contentManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        if (this.currentTab === 'agreements') {
            this.loadAgreements();
        } else if (this.currentTab === 'announcements') {
            this.loadAnnouncements();
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.contentManager = new ContentManager();
});

// 导出
window.ContentManager = ContentManager;
