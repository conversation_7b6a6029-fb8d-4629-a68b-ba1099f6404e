/**
 * 代理商管理页面JavaScript
 * 处理代理商列表、级别管理、佣金设置、招商加盟等功能
 */

class AgentManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentTab = 'agent-list';
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadAgentOverview();
        this.loadAgentList();
        this.loadAgentLevels();
        this.loadCommissionSettings();
        this.loadRecruitmentSettings();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#agentTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 搜索和筛选
        document.getElementById('searchBtn')?.addEventListener('click', () => {
            this.loadAgentList();
        });
        
        document.getElementById('statusFilter')?.addEventListener('change', () => {
            this.loadAgentList();
        });
        
        // 表单提交
        document.getElementById('commissionRateForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCommissionSettings();
        });
        
        document.getElementById('settlementForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveCommissionSettings();
        });
        
        document.getElementById('recruitmentForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRecruitmentSettings();
        });
        
        // 添加代理级别
        document.querySelector('[data-bs-target="#addLevelModal"]')?.addEventListener('click', () => {
            this.showAddLevelModal();
        });
        
        // 查看申请列表
        document.getElementById('viewApplicationsBtn')?.addEventListener('click', () => {
            this.showApplicationsList();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'agent-list':
                this.loadAgentList();
                break;
            case 'agent-levels':
                this.loadAgentLevels();
                break;
            case 'commission-settings':
                this.loadCommissionSettings();
                break;
            case 'recruitment':
                this.loadRecruitmentSettings();
                break;
            case 'agent-analytics':
                this.loadAgentAnalytics();
                break;
        }
    }
    
    async loadAgentOverview() {
        try {
            const response = await fetch('/api/agents/overview');
            const result = await response.json();
            
            if (result.success) {
                this.updateOverviewCards(result.data);
            }
        } catch (error) {
            console.error('加载代理概览失败:', error);
        }
    }
    
    updateOverviewCards(data) {
        document.getElementById('totalAgentsCount').textContent = data.totalAgentsCount || 0;
        document.getElementById('activeAgentsCount').textContent = data.activeAgentsCount || 0;
        document.getElementById('totalCommission').textContent = `¥${data.totalCommission || 0}`;
        document.getElementById('agentGrowth').textContent = `+${data.agentGrowth || 0}%`;
        document.getElementById('pendingApplications').textContent = data.pendingApplications || 0;
        document.getElementById('approvedThisMonth').textContent = data.approvedThisMonth || 0;
        document.getElementById('rejectedThisMonth').textContent = data.rejectedThisMonth || 0;
    }
    
    async loadAgentList() {
        try {
            const search = document.getElementById('searchInput')?.value || '';
            const status = document.getElementById('statusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/agents?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderAgentTable(result.data);
                this.renderPagination(result.pagination);
            }
        } catch (error) {
            console.error('加载代理列表失败:', error);
        }
    }
    
    renderAgentTable(agents) {
        const tbody = document.getElementById('agentTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = agents.map(agent => `
            <tr>
                <td>${agent.agentId || agent._id}</td>
                <td>${agent.name}</td>
                <td>${agent.contactPerson || agent.contactName}</td>
                <td>${agent.phone}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(agent.status)}">
                        ${this.getStatusText(agent.status)}
                    </span>
                </td>
                <td>${agent.subAgentCount || 0}</td>
                <td>${agent.userCount || 0}</td>
                <td>${agent.commissionRate || 0}%</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="agentManager.editAgent('${agent._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="agentManager.viewAgentDetail('${agent._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="agentManager.deleteAgent('${agent._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadAgentLevels() {
        try {
            const response = await fetch('/api/agents/levels');
            const result = await response.json();
            
            if (result.success) {
                this.renderAgentLevelsTable(result.data);
            }
        } catch (error) {
            console.error('加载代理级别失败:', error);
        }
    }
    
    renderAgentLevelsTable(levels) {
        const tbody = document.getElementById('agentLevelsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = levels.map(level => `
            <tr>
                <td>${level.name}</td>
                <td>${level.code}</td>
                <td>¥${level.minPerformance}</td>
                <td>${level.commissionRate}%</td>
                <td>${level.benefits}</td>
                <td>
                    <span class="badge bg-${level.status === 'active' ? 'success' : 'secondary'}">
                        ${level.status === 'active' ? '启用' : '禁用'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="agentManager.editLevel('${level._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="agentManager.deleteLevel('${level._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadCommissionSettings() {
        try {
            const response = await fetch('/api/agents/commission-settings');
            const result = await response.json();
            
            if (result.success) {
                this.populateCommissionForm(result.data);
            }
        } catch (error) {
            console.error('加载佣金设置失败:', error);
        }
    }
    
    populateCommissionForm(settings) {
        document.getElementById('level1Commission').value = settings.level1Commission || 10;
        document.getElementById('level2Commission').value = settings.level2Commission || 5;
        document.getElementById('level3Commission').value = settings.level3Commission || 2;
        document.getElementById('settlementPeriod').value = settings.settlementPeriod || 'monthly';
        document.getElementById('minWithdraw').value = settings.minWithdraw || 100;
        document.getElementById('withdrawFee').value = settings.withdrawFee || 0.5;
    }
    
    async saveCommissionSettings() {
        try {
            const formData = {
                level1Commission: parseFloat(document.getElementById('level1Commission').value),
                level2Commission: parseFloat(document.getElementById('level2Commission').value),
                level3Commission: parseFloat(document.getElementById('level3Commission').value),
                settlementPeriod: document.getElementById('settlementPeriod').value,
                minWithdraw: parseFloat(document.getElementById('minWithdraw').value),
                withdrawFee: parseFloat(document.getElementById('withdrawFee').value)
            };
            
            const response = await fetch('/api/agents/commission-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('佣金设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存佣金设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    async loadRecruitmentSettings() {
        try {
            const response = await fetch('/api/agents/recruitment-settings');
            const result = await response.json();
            
            if (result.success) {
                this.populateRecruitmentForm(result.data);
            }
        } catch (error) {
            console.error('加载招商设置失败:', error);
        }
    }
    
    populateRecruitmentForm(settings) {
        const approvalProcess = settings.approvalProcess || 'manual';
        document.querySelector(`input[name="approvalProcess"][value="${approvalProcess}"]`).checked = true;
        document.getElementById('applicationRequirements').value = settings.applicationRequirements || '';
        document.getElementById('contractTemplate').value = settings.contractTemplate || '';
    }
    
    async saveRecruitmentSettings() {
        try {
            const formData = {
                approvalProcess: document.querySelector('input[name="approvalProcess"]:checked').value,
                applicationRequirements: document.getElementById('applicationRequirements').value,
                contractTemplate: document.getElementById('contractTemplate').value
            };
            
            const response = await fetch('/api/agents/recruitment-settings', {
                method: 'PUT',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(formData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('招商设置保存成功', 'success');
            } else {
                this.showNotification(result.message || '保存失败', 'error');
            }
        } catch (error) {
            console.error('保存招商设置失败:', error);
            this.showNotification('保存失败', 'error');
        }
    }
    
    getStatusColor(status) {
        const colors = {
            'active': 'success',
            'pending': 'warning',
            'suspended': 'danger',
            'inactive': 'secondary'
        };
        return colors[status] || 'secondary';
    }
    
    getStatusText(status) {
        const texts = {
            'active': '活跃',
            'pending': '待审核',
            'suspended': '已暂停',
            'inactive': '非活跃'
        };
        return texts[status] || status;
    }
    
    showNotification(message, type = 'info') {
        // 简单的通知实现
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination) {
        const paginationEl = document.getElementById('pagination');
        if (!paginationEl) return;
        
        let html = '';
        
        // 上一页
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="agentManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        // 页码
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="agentManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        // 下一页
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="agentManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.loadAgentList();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.agentManager = new AgentManager();
});

// 导出
window.AgentManager = AgentManager;
