/**
 * 移动端导航组件
 * 处理移动设备上的侧边栏显示和隐藏
 */

class MobileNavigation {
    constructor() {
        this.sidebar = null;
        this.overlay = null;
        this.menuToggle = null;
        this.isOpen = false;
        this.init();
    }
    
    init() {
        this.createElements();
        this.bindEvents();
        this.handleResize();
    }
    
    createElements() {
        // 获取侧边栏
        this.sidebar = document.querySelector('.sidebar');
        if (!this.sidebar) return;
        
        // 创建遮罩层
        this.overlay = document.createElement('div');
        this.overlay.className = 'sidebar-overlay';
        document.body.appendChild(this.overlay);
        
        // 获取或创建菜单切换按钮
        this.menuToggle = document.querySelector('.menu-toggle');
        if (!this.menuToggle) {
            this.createMenuToggle();
        }
        
        // 创建移动端搜索按钮
        this.createMobileSearchToggle();
    }
    
    createMenuToggle() {
        const header = document.querySelector('.header');
        if (!header) return;
        
        const toggle = document.createElement('button');
        toggle.className = 'menu-toggle';
        toggle.innerHTML = '<i class="bi bi-list"></i>';
        toggle.setAttribute('aria-label', '切换菜单');
        
        const headerLeft = header.querySelector('.header-left');
        if (headerLeft) {
            headerLeft.insertBefore(toggle, headerLeft.firstChild);
        } else {
            header.insertBefore(toggle, header.firstChild);
        }
        
        this.menuToggle = toggle;
    }
    
    createMobileSearchToggle() {
        const header = document.querySelector('.header');
        const searchBar = document.querySelector('.search-bar');
        if (!header || !searchBar) return;
        
        const searchToggle = document.createElement('button');
        searchToggle.className = 'search-toggle d-md-none';
        searchToggle.innerHTML = '<i class="bi bi-search"></i>';
        searchToggle.setAttribute('aria-label', '搜索');
        
        const headerRight = header.querySelector('.header-right');
        if (headerRight) {
            headerRight.appendChild(searchToggle);
        }
        
        // 搜索切换事件
        searchToggle.addEventListener('click', () => {
            searchBar.classList.toggle('mobile-show');
            const input = searchBar.querySelector('input');
            if (input && searchBar.classList.contains('mobile-show')) {
                setTimeout(() => input.focus(), 100);
            }
        });
    }
    
    bindEvents() {
        // 菜单切换按钮点击
        if (this.menuToggle) {
            this.menuToggle.addEventListener('click', (e) => {
                e.preventDefault();
                this.toggle();
            });
        }
        
        // 遮罩层点击
        if (this.overlay) {
            this.overlay.addEventListener('click', () => {
                this.close();
            });
        }
        
        // ESC键关闭
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape' && this.isOpen) {
                this.close();
            }
        });
        
        // 窗口大小变化
        window.addEventListener('resize', () => {
            this.handleResize();
        });
        
        // 侧边栏链接点击（移动端自动关闭）
        if (this.sidebar) {
            this.sidebar.addEventListener('click', (e) => {
                if (e.target.matches('a') && window.innerWidth < 768) {
                    this.close();
                }
            });
        }
        
        // 触摸滑动支持
        this.bindTouchEvents();
    }
    
    bindTouchEvents() {
        let startX = 0;
        let currentX = 0;
        let isDragging = false;
        
        // 触摸开始
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            
            // 从左边缘开始滑动
            if (startX < 20 && !this.isOpen) {
                isDragging = true;
            }
            // 在侧边栏上滑动
            else if (this.isOpen && this.sidebar && this.sidebar.contains(e.target)) {
                isDragging = true;
            }
        }, { passive: true });
        
        // 触摸移动
        document.addEventListener('touchmove', (e) => {
            if (!isDragging) return;
            
            currentX = e.touches[0].clientX;
            const deltaX = currentX - startX;
            
            if (!this.isOpen && deltaX > 50) {
                // 向右滑动打开
                this.open();
                isDragging = false;
            } else if (this.isOpen && deltaX < -50) {
                // 向左滑动关闭
                this.close();
                isDragging = false;
            }
        }, { passive: true });
        
        // 触摸结束
        document.addEventListener('touchend', () => {
            isDragging = false;
        }, { passive: true });
    }
    
    toggle() {
        if (this.isOpen) {
            this.close();
        } else {
            this.open();
        }
    }
    
    open() {
        if (!this.sidebar || !this.overlay) return;
        
        this.sidebar.classList.add('show');
        this.overlay.classList.add('show');
        this.isOpen = true;
        
        // 防止背景滚动
        document.body.style.overflow = 'hidden';
        
        // 更新按钮图标
        if (this.menuToggle) {
            this.menuToggle.innerHTML = '<i class="bi bi-x"></i>';
            this.menuToggle.setAttribute('aria-expanded', 'true');
        }
        
        // 触发事件
        document.dispatchEvent(new CustomEvent('sidebarOpened'));
    }
    
    close() {
        if (!this.sidebar || !this.overlay) return;
        
        this.sidebar.classList.remove('show');
        this.overlay.classList.remove('show');
        this.isOpen = false;
        
        // 恢复背景滚动
        document.body.style.overflow = '';
        
        // 更新按钮图标
        if (this.menuToggle) {
            this.menuToggle.innerHTML = '<i class="bi bi-list"></i>';
            this.menuToggle.setAttribute('aria-expanded', 'false');
        }
        
        // 触发事件
        document.dispatchEvent(new CustomEvent('sidebarClosed'));
    }
    
    handleResize() {
        // 桌面端自动关闭移动菜单
        if (window.innerWidth >= 768 && this.isOpen) {
            this.close();
        }
        
        // 隐藏移动搜索
        const searchBar = document.querySelector('.search-bar');
        if (searchBar && window.innerWidth >= 768) {
            searchBar.classList.remove('mobile-show');
        }
    }
    
    // 设置活跃菜单项
    setActiveMenuItem(path) {
        if (!this.sidebar) return;
        
        const links = this.sidebar.querySelectorAll('a');
        links.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === path) {
                link.classList.add('active');
            }
        });
    }
    
    // 添加菜单项徽章
    addBadge(selector, count, type = 'danger') {
        const menuItem = this.sidebar?.querySelector(selector);
        if (!menuItem) return;
        
        // 移除现有徽章
        const existingBadge = menuItem.querySelector('.badge');
        if (existingBadge) {
            existingBadge.remove();
        }
        
        // 添加新徽章
        if (count > 0) {
            const badge = document.createElement('span');
            badge.className = `badge badge-${type}`;
            badge.textContent = count > 99 ? '99+' : count;
            menuItem.appendChild(badge);
        }
    }
    
    // 更新菜单项计数
    updateMenuCounts(counts) {
        Object.entries(counts).forEach(([selector, count]) => {
            this.addBadge(selector, count);
        });
    }
}

// 响应式表格处理
class ResponsiveTable {
    constructor() {
        this.init();
    }
    
    init() {
        this.handleTables();
        window.addEventListener('resize', () => {
            this.handleTables();
        });
    }
    
    handleTables() {
        const tables = document.querySelectorAll('.table-responsive table');
        
        tables.forEach(table => {
            if (window.innerWidth < 768) {
                this.makeTableMobile(table);
            } else {
                this.restoreTable(table);
            }
        });
    }
    
    makeTableMobile(table) {
        // 添加移动端样式类
        table.classList.add('table-mobile');
        
        // 隐藏不重要的列
        const headers = table.querySelectorAll('th');
        const rows = table.querySelectorAll('tbody tr');
        
        headers.forEach((header, index) => {
            if (header.classList.contains('d-none-mobile')) {
                header.style.display = 'none';
                rows.forEach(row => {
                    const cell = row.cells[index];
                    if (cell) cell.style.display = 'none';
                });
            }
        });
    }
    
    restoreTable(table) {
        table.classList.remove('table-mobile');
        
        // 显示所有列
        const cells = table.querySelectorAll('th, td');
        cells.forEach(cell => {
            cell.style.display = '';
        });
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.mobileNav = new MobileNavigation();
    window.responsiveTable = new ResponsiveTable();
    
    // 设置当前页面的活跃菜单项
    const currentPath = window.location.pathname.split('/').pop();
    if (currentPath) {
        window.mobileNav.setActiveMenuItem(currentPath);
    }
});

// 导出
window.MobileNavigation = MobileNavigation;
window.ResponsiveTable = ResponsiveTable;
