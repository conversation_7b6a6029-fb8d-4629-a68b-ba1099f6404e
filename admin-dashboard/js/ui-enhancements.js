/**
 * UI增强和用户体验优化
 * 提供响应式布局、数据导出、批量操作、实时验证等功能
 */

class UIEnhancements {
    constructor() {
        this.selectedItems = new Set();
        this.validationRules = {};
        this.exportFormats = ['excel', 'csv', 'pdf'];
        this.init();
    }
    
    init() {
        this.initResponsiveLayout();
        this.initBatchOperations();
        this.initDataExport();
        this.initRealTimeValidation();
        this.initTooltips();
        this.initLoadingStates();
        this.initKeyboardShortcuts();
        this.initThemeToggle();
        this.initSearchEnhancements();
        this.initTableEnhancements();
    }
    
    // 响应式布局优化
    initResponsiveLayout() {
        // 侧边栏折叠功能
        const sidebarToggle = document.querySelector('.menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        
        if (sidebarToggle && sidebar) {
            sidebarToggle.addEventListener('click', () => {
                sidebar.classList.toggle('collapsed');
                localStorage.setItem('sidebarCollapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            if (localStorage.getItem('sidebarCollapsed') === 'true') {
                sidebar.classList.add('collapsed');
            }
        }
        
        // 响应式表格
        this.makeTablesResponsive();
        
        // 移动端优化
        this.optimizeForMobile();
    }
    
    // 批量操作功能
    initBatchOperations() {
        // 全选功能
        document.addEventListener('change', (e) => {
            if (e.target.classList.contains('select-all')) {
                const checkboxes = document.querySelectorAll('.item-checkbox');
                checkboxes.forEach(cb => {
                    cb.checked = e.target.checked;
                    this.updateSelectedItems(cb.value, cb.checked);
                });
                this.updateBatchActions();
            }
            
            if (e.target.classList.contains('item-checkbox')) {
                this.updateSelectedItems(e.target.value, e.target.checked);
                this.updateBatchActions();
            }
        });
        
        // 批量操作按钮
        this.createBatchActionBar();
    }
    
    // 数据导出功能
    initDataExport() {
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('export-btn')) {
                e.preventDefault();
                this.showExportModal();
            }
        });
    }
    
    // 实时验证
    initRealTimeValidation() {
        document.addEventListener('input', (e) => {
            if (e.target.hasAttribute('data-validate')) {
                this.validateField(e.target);
            }
        });
        
        document.addEventListener('blur', (e) => {
            if (e.target.hasAttribute('data-validate')) {
                this.validateField(e.target);
            }
        });
    }
    
    // 工具提示
    initTooltips() {
        // 初始化Bootstrap工具提示
        const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    }
    
    // 加载状态
    initLoadingStates() {
        // 为所有表单添加加载状态
        document.addEventListener('submit', (e) => {
            const form = e.target;
            const submitBtn = form.querySelector('button[type="submit"]');
            if (submitBtn) {
                this.setLoadingState(submitBtn, true);
                
                // 模拟加载完成（实际项目中应该在请求完成后调用）
                setTimeout(() => {
                    this.setLoadingState(submitBtn, false);
                }, 2000);
            }
        });
    }
    
    // 键盘快捷键
    initKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S 保存
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                const activeForm = document.querySelector('form:focus-within');
                if (activeForm) {
                    activeForm.dispatchEvent(new Event('submit'));
                }
            }
            
            // Ctrl+A 全选
            if (e.ctrlKey && e.key === 'a' && e.target.tagName !== 'INPUT' && e.target.tagName !== 'TEXTAREA') {
                e.preventDefault();
                const selectAllCheckbox = document.querySelector('.select-all');
                if (selectAllCheckbox) {
                    selectAllCheckbox.checked = true;
                    selectAllCheckbox.dispatchEvent(new Event('change'));
                }
            }
            
            // ESC 关闭模态框
            if (e.key === 'Escape') {
                const openModal = document.querySelector('.modal.show');
                if (openModal) {
                    const modal = bootstrap.Modal.getInstance(openModal);
                    if (modal) modal.hide();
                }
            }
        });
    }
    
    // 主题切换
    initThemeToggle() {
        const themeToggle = document.getElementById('themeToggle');
        if (themeToggle) {
            themeToggle.addEventListener('click', () => {
                this.toggleTheme();
            });
            
            // 恢复主题设置
            const savedTheme = localStorage.getItem('theme') || 'light';
            this.setTheme(savedTheme);
        }
    }
    
    // 搜索增强
    initSearchEnhancements() {
        const searchInputs = document.querySelectorAll('.search-input');
        searchInputs.forEach(input => {
            // 添加搜索建议
            this.addSearchSuggestions(input);
            
            // 添加清除按钮
            this.addClearButton(input);
        });
    }
    
    // 表格增强
    initTableEnhancements() {
        const tables = document.querySelectorAll('.data-table');
        tables.forEach(table => {
            // 添加排序功能
            this.addTableSorting(table);
            
            // 添加列筛选
            this.addColumnFilters(table);
            
            // 添加行高亮
            this.addRowHighlight(table);
        });
    }
    
    // 更新选中项目
    updateSelectedItems(itemId, selected) {
        if (selected) {
            this.selectedItems.add(itemId);
        } else {
            this.selectedItems.delete(itemId);
        }
    }
    
    // 更新批量操作
    updateBatchActions() {
        const batchActionBar = document.getElementById('batchActionBar');
        const selectedCount = this.selectedItems.size;
        
        if (batchActionBar) {
            if (selectedCount > 0) {
                batchActionBar.style.display = 'flex';
                batchActionBar.querySelector('.selected-count').textContent = selectedCount;
            } else {
                batchActionBar.style.display = 'none';
            }
        }
    }
    
    // 创建批量操作栏
    createBatchActionBar() {
        if (document.getElementById('batchActionBar')) return;
        
        const batchActionBar = document.createElement('div');
        batchActionBar.id = 'batchActionBar';
        batchActionBar.className = 'batch-action-bar';
        batchActionBar.style.display = 'none';
        batchActionBar.innerHTML = `
            <div class="batch-info">
                已选择 <span class="selected-count">0</span> 项
            </div>
            <div class="batch-actions">
                <button class="btn btn-outline-primary btn-sm" onclick="uiEnhancements.batchEdit()">
                    <i class="bi bi-pencil"></i> 批量编辑
                </button>
                <button class="btn btn-outline-danger btn-sm" onclick="uiEnhancements.batchDelete()">
                    <i class="bi bi-trash"></i> 批量删除
                </button>
                <button class="btn btn-outline-secondary btn-sm" onclick="uiEnhancements.clearSelection()">
                    <i class="bi bi-x"></i> 取消选择
                </button>
            </div>
        `;
        
        document.body.appendChild(batchActionBar);
    }
    
    // 显示导出模态框
    showExportModal() {
        const modalHtml = `
            <div class="modal fade" id="exportModal" tabindex="-1">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">导出数据</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                        </div>
                        <div class="modal-body">
                            <form id="exportForm">
                                <div class="mb-3">
                                    <label class="form-label">导出格式</label>
                                    <select class="form-select" id="exportFormat">
                                        <option value="excel">Excel (.xlsx)</option>
                                        <option value="csv">CSV (.csv)</option>
                                        <option value="pdf">PDF (.pdf)</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">导出范围</label>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportRange" value="all" checked>
                                        <label class="form-check-label">全部数据</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportRange" value="selected">
                                        <label class="form-check-label">选中数据 (${this.selectedItems.size} 项)</label>
                                    </div>
                                    <div class="form-check">
                                        <input class="form-check-input" type="radio" name="exportRange" value="filtered">
                                        <label class="form-check-label">当前筛选结果</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">包含字段</label>
                                    <div class="export-fields">
                                        <!-- 字段选择将动态生成 -->
                                    </div>
                                </div>
                            </form>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                            <button type="button" class="btn btn-primary" onclick="uiEnhancements.executeExport()">导出</button>
                        </div>
                    </div>
                </div>
            </div>
        `;
        
        // 移除已存在的模态框
        const existingModal = document.getElementById('exportModal');
        if (existingModal) {
            existingModal.remove();
        }
        
        document.body.insertAdjacentHTML('beforeend', modalHtml);
        const modal = new bootstrap.Modal(document.getElementById('exportModal'));
        modal.show();
    }
    
    // 字段验证
    validateField(field) {
        const value = field.value.trim();
        const rules = field.getAttribute('data-validate').split('|');
        let isValid = true;
        let errorMessage = '';
        
        for (const rule of rules) {
            if (rule === 'required' && !value) {
                isValid = false;
                errorMessage = '此字段为必填项';
                break;
            }
            
            if (rule.startsWith('min:')) {
                const min = parseInt(rule.split(':')[1]);
                if (value.length < min) {
                    isValid = false;
                    errorMessage = `最少需要${min}个字符`;
                    break;
                }
            }
            
            if (rule.startsWith('max:')) {
                const max = parseInt(rule.split(':')[1]);
                if (value.length > max) {
                    isValid = false;
                    errorMessage = `最多允许${max}个字符`;
                    break;
                }
            }
            
            if (rule === 'email' && value && !this.isValidEmail(value)) {
                isValid = false;
                errorMessage = '请输入有效的邮箱地址';
                break;
            }
            
            if (rule === 'phone' && value && !this.isValidPhone(value)) {
                isValid = false;
                errorMessage = '请输入有效的手机号码';
                break;
            }
        }
        
        this.showFieldValidation(field, isValid, errorMessage);
        return isValid;
    }
    
    // 显示字段验证结果
    showFieldValidation(field, isValid, message) {
        // 移除之前的验证状态
        field.classList.remove('is-valid', 'is-invalid');
        
        const feedback = field.parentNode.querySelector('.invalid-feedback');
        if (feedback) {
            feedback.remove();
        }
        
        if (!isValid && message) {
            field.classList.add('is-invalid');
            const feedbackEl = document.createElement('div');
            feedbackEl.className = 'invalid-feedback';
            feedbackEl.textContent = message;
            field.parentNode.appendChild(feedbackEl);
        } else if (isValid && field.value.trim()) {
            field.classList.add('is-valid');
        }
    }
    
    // 设置加载状态
    setLoadingState(button, loading) {
        if (loading) {
            button.disabled = true;
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>处理中...';
        } else {
            button.disabled = false;
            button.innerHTML = button.getAttribute('data-original-text') || '提交';
        }
    }
    
    // 主题切换
    toggleTheme() {
        const currentTheme = document.documentElement.getAttribute('data-theme') || 'light';
        const newTheme = currentTheme === 'light' ? 'dark' : 'light';
        this.setTheme(newTheme);
    }
    
    setTheme(theme) {
        document.documentElement.setAttribute('data-theme', theme);
        localStorage.setItem('theme', theme);
        
        const themeIcon = document.querySelector('#themeToggle i');
        if (themeIcon) {
            themeIcon.className = theme === 'light' ? 'bi bi-moon' : 'bi bi-sun';
        }
    }
    
    // 响应式表格
    makeTablesResponsive() {
        const tables = document.querySelectorAll('table');
        tables.forEach(table => {
            if (!table.parentNode.classList.contains('table-responsive')) {
                const wrapper = document.createElement('div');
                wrapper.className = 'table-responsive';
                table.parentNode.insertBefore(wrapper, table);
                wrapper.appendChild(table);
            }
        });
    }
    
    // 移动端优化
    optimizeForMobile() {
        if (window.innerWidth <= 768) {
            // 移动端特定优化
            document.body.classList.add('mobile-optimized');
            
            // 简化表格显示
            const tables = document.querySelectorAll('.data-table');
            tables.forEach(table => {
                table.classList.add('mobile-table');
            });
        }
    }
    
    // 邮箱验证
    isValidEmail(email) {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
    }
    
    // 手机号验证
    isValidPhone(phone) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        return phoneRegex.test(phone);
    }
    
    // 批量操作方法
    batchEdit() {
        if (this.selectedItems.size === 0) {
            this.showNotification('请先选择要编辑的项目', 'warning');
            return;
        }
        
        // 实现批量编辑逻辑
        console.log('批量编辑:', Array.from(this.selectedItems));
        this.showNotification(`正在批量编辑 ${this.selectedItems.size} 个项目`, 'info');
    }
    
    batchDelete() {
        if (this.selectedItems.size === 0) {
            this.showNotification('请先选择要删除的项目', 'warning');
            return;
        }
        
        if (confirm(`确定要删除选中的 ${this.selectedItems.size} 个项目吗？`)) {
            // 实现批量删除逻辑
            console.log('批量删除:', Array.from(this.selectedItems));
            this.showNotification(`已删除 ${this.selectedItems.size} 个项目`, 'success');
            this.clearSelection();
        }
    }
    
    clearSelection() {
        this.selectedItems.clear();
        document.querySelectorAll('.item-checkbox, .select-all').forEach(cb => {
            cb.checked = false;
        });
        this.updateBatchActions();
    }
    
    executeExport() {
        const format = document.getElementById('exportFormat').value;
        const range = document.querySelector('input[name="exportRange"]:checked').value;
        
        // 实现导出逻辑
        console.log('导出数据:', { format, range, selectedItems: Array.from(this.selectedItems) });
        
        this.showNotification(`正在导出${format.toUpperCase()}格式的数据...`, 'info');
        
        // 关闭模态框
        const modal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
        modal.hide();
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
}

// 初始化UI增强功能
document.addEventListener('DOMContentLoaded', () => {
    window.uiEnhancements = new UIEnhancements();
});

// 导出
window.UIEnhancements = UIEnhancements;
