// 数据报表页面JavaScript

let currentReportType = 'business';
let charts = {};

document.addEventListener('DOMContentLoaded', function() {
    initReportsPage();
    setDefaultDates();
    loadReports();
});

function initReportsPage() {
    // 侧边栏折叠/展开
    const menuToggle = document.querySelector('.menu-toggle');
    const adminLayout = document.querySelector('.admin-layout');
    
    if (menuToggle && adminLayout) {
        menuToggle.addEventListener('click', () => {
            adminLayout.classList.toggle('sidebar-collapsed');
        });
    }
    
    // 用户下拉菜单
    const userBtn = document.querySelector('.user-btn');
    const dropdownMenu = document.querySelector('.dropdown-menu');
    
    if (userBtn && dropdownMenu) {
        document.addEventListener('click', (e) => {
            if (!userBtn.contains(e.target)) {
                dropdownMenu.style.display = 'none';
            }
        });
        
        userBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            dropdownMenu.style.display = dropdownMenu.style.display === 'block' ? 'none' : 'block';
        });
    }

    // 报表类型切换
    document.getElementById('reportType').addEventListener('change', function() {
        currentReportType = this.value;
        showReportSection(currentReportType);
    });
}

function setDefaultDates() {
    const endDate = new Date();
    const startDate = new Date();
    startDate.setDate(startDate.getDate() - 30); // 默认30天

    document.getElementById('startDate').value = startDate.toISOString().split('T')[0];
    document.getElementById('endDate').value = endDate.toISOString().split('T')[0];
}

function showReportSection(reportType) {
    // 隐藏所有报表区域
    document.querySelectorAll('.report-section').forEach(section => {
        section.style.display = 'none';
    });

    // 显示选中的报表区域
    const targetSection = document.getElementById(reportType + 'Report');
    if (targetSection) {
        targetSection.style.display = 'block';
    }
}

async function loadReports() {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const reportType = document.getElementById('reportType').value;

    showReportSection(reportType);

    try {
        switch (reportType) {
            case 'business':
                await loadBusinessOverview(startDate, endDate);
                break;
            case 'financial':
                await loadFinancialReport(startDate, endDate);
                break;
            case 'user':
                await loadUserBehaviorReport(startDate, endDate);
                break;
            case 'expert':
                await loadExpertPerformanceReport(startDate, endDate);
                break;
        }
    } catch (error) {
        console.error('加载报表失败:', error);
        showError('加载报表失败，请稍后重试');
    }
}

async function loadBusinessOverview(startDate, endDate) {
    try {
        const params = new URLSearchParams({ startDate, endDate });
        const response = await fetch(`/ad/api/reports/business?${params}`);
        const result = await response.json();

        if (result.success) {
            renderBusinessOverview(result.data);
        } else {
            showError('获取业务概览失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取业务概览失败:', error);
        showError('获取业务概览失败，请稍后重试');
    }
}

function renderBusinessOverview(data) {
    // 更新关键指标
    const overview = data.overview;
    document.getElementById('totalRevenue').textContent = `¥${parseFloat(overview.totalRevenue || 0).toFixed(2)}`;
    document.getElementById('totalOrders').textContent = overview.totalOrders || 0;
    document.getElementById('totalUsers').textContent = overview.totalUsers || 0;
    document.getElementById('totalExperts').textContent = overview.totalExperts || 0;

    // 渲染收入趋势图
    renderRevenueChart(data.dailyRevenueTrend);

    // 渲染服务类型分布图
    renderServiceTypeChart(data.serviceTypeDistribution);

    // 渲染专家效率表格
    renderExpertEfficiencyTable(data.expertEfficiency);
}

function renderRevenueChart(data) {
    const ctx = document.getElementById('revenueChart').getContext('2d');
    
    // 销毁现有图表
    if (charts.revenueChart) {
        charts.revenueChart.destroy();
    }

    const labels = data.map(item => item.date);
    const revenues = data.map(item => parseFloat(item.revenue || 0));
    const orderCounts = data.map(item => parseInt(item.orderCount || 0));

    charts.revenueChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '收入 (¥)',
                data: revenues,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                yAxisID: 'y'
            }, {
                label: '订单数',
                data: orderCounts,
                borderColor: 'rgb(255, 99, 132)',
                backgroundColor: 'rgba(255, 99, 132, 0.2)',
                yAxisID: 'y1'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            }
        }
    });
}

function renderServiceTypeChart(data) {
    const ctx = document.getElementById('serviceTypeChart').getContext('2d');
    
    // 销毁现有图表
    if (charts.serviceTypeChart) {
        charts.serviceTypeChart.destroy();
    }

    const labels = data.map(item => getServiceTypeText(item.serviceType));
    const revenues = data.map(item => parseFloat(item.revenue || 0));

    charts.serviceTypeChart = new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: labels,
            datasets: [{
                data: revenues,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

function renderExpertEfficiencyTable(data) {
    const tbody = document.getElementById('expertEfficiencyTable');
    
    if (!data || data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = data.map(expert => `
        <tr>
            <td>${expert.expertCode}</td>
            <td>${expert.realName}</td>
            <td><span class="badge bg-success">${expert.completedWorkOrders}</span></td>
            <td>${(expert.avgHours || 0).toFixed(1)}</td>
            <td>
                <div class="d-flex align-items-center">
                    <span class="me-1">${(expert.avgRating || 0).toFixed(1)}</span>
                    <div class="rating-stars">
                        ${renderStars(expert.avgRating || 0)}
                    </div>
                </div>
            </td>
        </tr>
    `).join('');
}

async function loadFinancialReport(startDate, endDate) {
    try {
        const params = new URLSearchParams({ startDate, endDate });
        const response = await fetch(`/ad/api/reports/financial?${params}`);
        const result = await response.json();

        if (result.success) {
            renderFinancialReport(result.data);
        } else {
            showError('获取财务报表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取财务报表失败:', error);
        showError('获取财务报表失败，请稍后重试');
    }
}

function renderFinancialReport(data) {
    // 渲染财务趋势图
    renderFinancialTrendChart(data.revenueStats);
    
    // 渲染支付方式分布图
    renderPaymentMethodChart(data.paymentMethodStats);
}

function renderFinancialTrendChart(data) {
    const ctx = document.getElementById('financialTrendChart').getContext('2d');
    
    if (charts.financialTrendChart) {
        charts.financialTrendChart.destroy();
    }

    const labels = data.map(item => item.period);
    const totalRevenue = data.map(item => parseFloat(item.totalRevenue || 0));
    const aiRevenue = data.map(item => parseFloat(item.aiRevenue || 0));
    const expertRevenue = data.map(item => parseFloat(item.expertRevenue || 0));

    charts.financialTrendChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: 'AI优化收入',
                data: aiRevenue,
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }, {
                label: '专家优化收入',
                data: expertRevenue,
                backgroundColor: 'rgba(255, 99, 132, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                x: {
                    stacked: true
                },
                y: {
                    stacked: true
                }
            }
        }
    });
}

function renderPaymentMethodChart(data) {
    const ctx = document.getElementById('paymentMethodChart').getContext('2d');
    
    if (charts.paymentMethodChart) {
        charts.paymentMethodChart.destroy();
    }

    const labels = data.map(item => item.paymentMethod);
    const amounts = data.map(item => parseFloat(item.totalAmount || 0));

    charts.paymentMethodChart = new Chart(ctx, {
        type: 'pie',
        data: {
            labels: labels,
            datasets: [{
                data: amounts,
                backgroundColor: [
                    'rgba(255, 99, 132, 0.8)',
                    'rgba(54, 162, 235, 0.8)',
                    'rgba(255, 205, 86, 0.8)',
                    'rgba(75, 192, 192, 0.8)'
                ]
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom'
                }
            }
        }
    });
}

async function loadUserBehaviorReport(startDate, endDate) {
    try {
        const params = new URLSearchParams({ startDate, endDate });
        const response = await fetch(`/ad/api/reports/user?${params}`);
        const result = await response.json();

        if (result.success) {
            renderUserBehaviorReport(result.data);
        } else {
            showError('获取用户行为报表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取用户行为报表失败:', error);
        showError('获取用户行为报表失败，请稍后重试');
    }
}

function renderUserBehaviorReport(data) {
    // 渲染用户注册趋势图
    renderUserRegistrationChart(data.registrationTrend);
    
    // 渲染服务偏好图
    renderServicePreferenceChart(data.servicePreference);
    
    // 渲染高价值用户表格
    renderTopConsumersTable(data.topConsumers);
}

function renderUserRegistrationChart(data) {
    const ctx = document.getElementById('userRegistrationChart').getContext('2d');
    
    if (charts.userRegistrationChart) {
        charts.userRegistrationChart.destroy();
    }

    const labels = data.map(item => item.date);
    const newUsers = data.map(item => parseInt(item.newUsers || 0));

    charts.userRegistrationChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: '新用户注册',
                data: newUsers,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function renderServicePreferenceChart(data) {
    const ctx = document.getElementById('servicePreferenceChart').getContext('2d');
    
    if (charts.servicePreferenceChart) {
        charts.servicePreferenceChart.destroy();
    }

    const labels = data.map(item => getServiceTypeText(item.serviceType));
    const orderCounts = data.map(item => parseInt(item.orderCount || 0));

    charts.servicePreferenceChart = new Chart(ctx, {
        type: 'bar',
        data: {
            labels: labels,
            datasets: [{
                label: '订单数量',
                data: orderCounts,
                backgroundColor: 'rgba(54, 162, 235, 0.8)'
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false
        }
    });
}

function renderTopConsumersTable(data) {
    const tbody = document.getElementById('topConsumersTable');
    
    if (!data || data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="5" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = data.map(user => `
        <tr>
            <td>${user.username}</td>
            <td><span class="badge bg-primary">${user.orderCount}</span></td>
            <td><strong class="text-success">¥${parseFloat(user.totalSpent || 0).toFixed(2)}</strong></td>
            <td>¥${parseFloat(user.avgOrderValue || 0).toFixed(2)}</td>
            <td><small>${formatDateTime(user.lastOrderDate)}</small></td>
        </tr>
    `).join('');
}

async function loadExpertPerformanceReport(startDate, endDate) {
    try {
        const params = new URLSearchParams({ startDate, endDate });
        const response = await fetch(`/ad/api/reports/expert?${params}`);
        const result = await response.json();

        if (result.success) {
            renderExpertPerformanceReport(result.data);
        } else {
            showError('获取专家绩效报表失败: ' + result.message);
        }
    } catch (error) {
        console.error('获取专家绩效报表失败:', error);
        showError('获取专家绩效报表失败，请稍后重试');
    }
}

function renderExpertPerformanceReport(data) {
    // 渲染专家工作量表格
    renderExpertWorkloadTable(data.expertWorkload);
}

function renderExpertWorkloadTable(data) {
    const tbody = document.getElementById('expertWorkloadTable');
    
    if (!data || data.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" class="text-center text-muted">暂无数据</td></tr>';
        return;
    }

    tbody.innerHTML = data.map(expert => `
        <tr>
            <td>${expert.expertCode}</td>
            <td>${expert.realName}</td>
            <td><span class="badge ${getStatusBadgeClass(expert.status)}">${getStatusText(expert.status)}</span></td>
            <td>${expert.totalWorkOrders || 0}</td>
            <td><span class="badge bg-success">${expert.completedWorkOrders || 0}</span></td>
            <td><span class="badge bg-warning">${expert.inProgressWorkOrders || 0}</span></td>
            <td>${(expert.totalHours || 0).toFixed(1)}h</td>
            <td>
                <div class="d-flex align-items-center">
                    <span class="me-1">${(expert.avgRating || 0).toFixed(1)}</span>
                    <div class="rating-stars">
                        ${renderStars(expert.avgRating || 0)}
                    </div>
                </div>
            </td>
            <td><strong class="text-success">¥${parseFloat(expert.totalRevenue || 0).toFixed(2)}</strong></td>
        </tr>
    `).join('');
}

async function exportReport(format) {
    const startDate = document.getElementById('startDate').value;
    const endDate = document.getElementById('endDate').value;
    const reportType = document.getElementById('reportType').value;

    try {
        const params = new URLSearchParams({ 
            reportType, 
            format, 
            startDate, 
            endDate 
        });
        
        const response = await fetch(`/ad/api/reports/export?${params}`);
        
        if (format === 'csv') {
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `${reportType}_report_${Date.now()}.csv`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);
            showSuccess('报表导出成功');
        } else {
            const result = await response.json();
            if (result.success) {
                const dataStr = JSON.stringify(result.data, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `${reportType}_report_${Date.now()}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                window.URL.revokeObjectURL(url);
                showSuccess('报表导出成功');
            } else {
                showError('导出报表失败: ' + result.message);
            }
        }
    } catch (error) {
        console.error('导出报表失败:', error);
        showError('导出报表失败，请稍后重试');
    }
}

// 辅助函数
function getServiceTypeText(serviceType) {
    const texts = {
        'ai_optimization': 'AI优化',
        'expert_optimization': '专家优化'
    };
    return texts[serviceType] || serviceType;
}

function getStatusText(status) {
    const texts = {
        'active': '在线',
        'busy': '忙碌',
        'offline': '离线',
        'suspended': '暂停'
    };
    return texts[status] || status;
}

function getStatusBadgeClass(status) {
    const classes = {
        'active': 'bg-success',
        'busy': 'bg-warning',
        'offline': 'bg-secondary',
        'suspended': 'bg-danger'
    };
    return classes[status] || 'bg-secondary';
}

function renderStars(rating) {
    const fullStars = Math.floor(rating);
    const hasHalfStar = rating % 1 >= 0.5;
    const emptyStars = 5 - fullStars - (hasHalfStar ? 1 : 0);
    
    let starsHTML = '';
    
    for (let i = 0; i < fullStars; i++) {
        starsHTML += '<i class="bi bi-star-fill text-warning"></i>';
    }
    
    if (hasHalfStar) {
        starsHTML += '<i class="bi bi-star-half text-warning"></i>';
    }
    
    for (let i = 0; i < emptyStars; i++) {
        starsHTML += '<i class="bi bi-star text-muted"></i>';
    }
    
    return starsHTML;
}

function formatDateTime(dateString) {
    if (!dateString) return '未知';
    const date = new Date(dateString);
    return date.toLocaleString('zh-CN');
}
