/**
 * API文档JavaScript
 * 处理API文档的交互功能、API测试等
 */

class ApiDocsManager {
    constructor() {
        this.baseUrl = '/api';
        this.token = localStorage.getItem('token');
        this.apiSections = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadApiSections();
        this.initScrollSpy();
        this.loadApiStats();
    }
    
    bindEvents() {
        // 导航链接点击
        document.querySelectorAll('.api-nav .nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const target = e.target.getAttribute('href');
                this.scrollToSection(target);
                this.updateActiveNav(e.target);
            });
        });
        
        // 主题切换
        document.getElementById('themeToggle')?.addEventListener('click', () => {
            this.toggleTheme();
        });
    }
    
    loadApiSections() {
        // 加载更多API文档部分
        const moreApiSections = document.getElementById('moreApiSections');
        if (!moreApiSections) return;
        
        const sections = [
            this.createAgentsSection(),
            this.createCommissionSection(),
            this.createPricingSection(),
            this.createFinanceSection(),
            this.createContentSection(),
            this.createMarketingSection(),
            this.createSecuritySection(),
            this.createMonitorSection(),
            this.createAnalyticsSection(),
            this.createErrorCodesSection(),
            this.createSDKSection()
        ];
        
        moreApiSections.innerHTML = sections.join('');
        
        // 重新初始化代码高亮
        if (typeof Prism !== 'undefined') {
            Prism.highlightAll();
        }
    }
    
    createAgentsSection() {
        return `
            <section id="agents" class="api-section">
                <h3>代理商管理</h3>
                <p>代理商管理相关的API接口，包括代理商信息管理、业绩统计等。</p>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method get">GET</span>
                        <span class="api-path">/agents</span>
                        <span class="ms-auto text-muted">获取代理商列表</span>
                    </div>
                    <div class="api-content">
                        <h6>查询参数</h6>
                        <table class="table table-sm param-table">
                            <thead>
                                <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>page</td><td>integer</td><td>否</td><td>页码，默认1</td></tr>
                                <tr><td>limit</td><td>integer</td><td>否</td><td>每页数量，默认20</td></tr>
                                <tr><td>level</td><td>integer</td><td>否</td><td>代理商等级</td></tr>
                                <tr><td>status</td><td>string</td><td>否</td><td>状态：active, inactive, pending</td></tr>
                            </tbody>
                        </table>
                        
                        <div class="api-test-form">
                            <h6>API测试</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="apiDocsManager.testApi('/agents', 'GET')">测试接口</button>
                            <div class="api-test-result mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method post">POST</span>
                        <span class="api-path">/agents</span>
                        <span class="ms-auto text-muted">创建代理商</span>
                    </div>
                    <div class="api-content">
                        <h6>请求参数</h6>
                        <table class="table table-sm param-table">
                            <thead>
                                <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>name</td><td>string</td><td>是</td><td>代理商名称</td></tr>
                                <tr><td>email</td><td>string</td><td>是</td><td>邮箱地址</td></tr>
                                <tr><td>phone</td><td>string</td><td>是</td><td>联系电话</td></tr>
                                <tr><td>level</td><td>integer</td><td>是</td><td>代理商等级</td></tr>
                                <tr><td>region</td><td>string</td><td>否</td><td>负责区域</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        `;
    }
    
    createCommissionSection() {
        return `
            <section id="commission" class="api-section">
                <h3>佣金管理</h3>
                <p>佣金管理相关的API接口，包括佣金计算、结算、提现等功能。</p>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method get">GET</span>
                        <span class="api-path">/commission/records</span>
                        <span class="ms-auto text-muted">获取佣金记录</span>
                    </div>
                    <div class="api-content">
                        <h6>查询参数</h6>
                        <table class="table table-sm param-table">
                            <thead>
                                <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>agentId</td><td>string</td><td>否</td><td>代理商ID</td></tr>
                                <tr><td>status</td><td>string</td><td>否</td><td>状态：pending, settled, paid</td></tr>
                                <tr><td>startDate</td><td>string</td><td>否</td><td>开始日期</td></tr>
                                <tr><td>endDate</td><td>string</td><td>否</td><td>结束日期</td></tr>
                            </tbody>
                        </table>
                        
                        <div class="api-test-form">
                            <h6>API测试</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="apiDocsManager.testApi('/commission/records', 'GET')">测试接口</button>
                            <div class="api-test-result mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method post">POST</span>
                        <span class="api-path">/commission/calculate</span>
                        <span class="ms-auto text-muted">计算订单佣金</span>
                    </div>
                    <div class="api-content">
                        <h6>请求参数</h6>
                        <table class="table table-sm param-table">
                            <thead>
                                <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>orderId</td><td>string</td><td>是</td><td>订单ID</td></tr>
                                <tr><td>agentId</td><td>string</td><td>是</td><td>代理商ID</td></tr>
                                <tr><td>orderAmount</td><td>number</td><td>是</td><td>订单金额</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        `;
    }
    
    createPricingSection() {
        return `
            <section id="pricing" class="api-section">
                <h3>价格管理</h3>
                <p>价格管理相关的API接口，包括产品价格、折扣配置等功能。</p>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method get">GET</span>
                        <span class="api-path">/pricing/products</span>
                        <span class="ms-auto text-muted">获取产品价格列表</span>
                    </div>
                    <div class="api-content">
                        <div class="api-test-form">
                            <h6>API测试</h6>
                            <button type="button" class="btn btn-primary btn-sm" onclick="apiDocsManager.testApi('/pricing/products', 'GET')">测试接口</button>
                            <div class="api-test-result mt-2"></div>
                        </div>
                    </div>
                </div>
                
                <div class="api-endpoint">
                    <div class="api-endpoint-header">
                        <span class="api-method post">POST</span>
                        <span class="api-path">/pricing/calculate</span>
                        <span class="ms-auto text-muted">计算产品价格</span>
                    </div>
                    <div class="api-content">
                        <h6>请求参数</h6>
                        <table class="table table-sm param-table">
                            <thead>
                                <tr><th>参数名</th><th>类型</th><th>必填</th><th>说明</th></tr>
                            </thead>
                            <tbody>
                                <tr><td>productId</td><td>string</td><td>是</td><td>产品ID</td></tr>
                                <tr><td>quantity</td><td>integer</td><td>否</td><td>数量，默认1</td></tr>
                                <tr><td>memberLevel</td><td>integer</td><td>否</td><td>会员等级</td></tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </section>
        `;
    }
    
    createErrorCodesSection() {
        return `
            <section id="errors" class="api-section">
                <h3>错误代码</h3>
                <p>API可能返回的错误代码及其含义。</p>
                
                <div class="table-responsive">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>错误代码</th>
                                <th>HTTP状态码</th>
                                <th>说明</th>
                                <th>解决方案</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td><code>INVALID_TOKEN</code></td>
                                <td>401</td>
                                <td>无效的访问令牌</td>
                                <td>重新登录获取新令牌</td>
                            </tr>
                            <tr>
                                <td><code>INSUFFICIENT_PERMISSIONS</code></td>
                                <td>403</td>
                                <td>权限不足</td>
                                <td>联系管理员分配权限</td>
                            </tr>
                            <tr>
                                <td><code>RESOURCE_NOT_FOUND</code></td>
                                <td>404</td>
                                <td>资源不存在</td>
                                <td>检查资源ID是否正确</td>
                            </tr>
                            <tr>
                                <td><code>VALIDATION_ERROR</code></td>
                                <td>400</td>
                                <td>参数验证失败</td>
                                <td>检查请求参数格式</td>
                            </tr>
                            <tr>
                                <td><code>RATE_LIMIT_EXCEEDED</code></td>
                                <td>429</td>
                                <td>请求频率超限</td>
                                <td>降低请求频率</td>
                            </tr>
                            <tr>
                                <td><code>INTERNAL_SERVER_ERROR</code></td>
                                <td>500</td>
                                <td>服务器内部错误</td>
                                <td>联系技术支持</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </section>
        `;
    }
    
    createSDKSection() {
        return `
            <section id="sdk" class="api-section">
                <h3>SDK下载</h3>
                <p>提供多种编程语言的SDK，简化API集成过程。</p>
                
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-filetype-js"></i> JavaScript SDK
                                </h6>
                                <p class="card-text">适用于Node.js和浏览器环境</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="apiDocsManager.downloadSDK('javascript')">
                                        <i class="bi bi-download"></i> 下载
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="apiDocsManager.viewSDKDocs('javascript')">
                                        文档
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-body">
                                <h6 class="card-title">
                                    <i class="bi bi-filetype-py"></i> Python SDK
                                </h6>
                                <p class="card-text">适用于Python 3.6+</p>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-primary btn-sm" onclick="apiDocsManager.downloadSDK('python')">
                                        <i class="bi bi-download"></i> 下载
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="apiDocsManager.viewSDKDocs('python')">
                                        文档
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mt-4">
                    <h5>快速开始示例</h5>
                    <ul class="nav nav-tabs" id="sdkTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="js-tab" data-bs-toggle="tab" data-bs-target="#js" type="button" role="tab">JavaScript</button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="python-tab" data-bs-toggle="tab" data-bs-target="#python" type="button" role="tab">Python</button>
                        </li>
                    </ul>
                    <div class="tab-content" id="sdkTabContent">
                        <div class="tab-pane fade show active" id="js" role="tabpanel">
                            <div class="code-block">
                                <pre><code class="language-javascript">// 安装SDK
npm install writerpro-admin-sdk

// 使用示例
const WriterProAdmin = require('writerpro-admin-sdk');

const client = new WriterProAdmin({
  baseUrl: 'https://api.writerpro.com/v1',
  token: 'your_access_token'
});

// 获取用户列表
const users = await client.users.list({
  page: 1,
  limit: 20
});

console.log(users);</code></pre>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="python" role="tabpanel">
                            <div class="code-block">
                                <pre><code class="language-python"># 安装SDK
pip install writerpro-admin-sdk

# 使用示例
from writerpro_admin import WriterProAdmin

client = WriterProAdmin(
    base_url='https://api.writerpro.com/v1',
    token='your_access_token'
)

# 获取用户列表
users = client.users.list(page=1, limit=20)
print(users)</code></pre>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        `;
    }
    
    // 其他section创建方法的简化版本
    createFinanceSection() { return '<section id="finance" class="api-section"><h3>财务管理</h3><p>财务管理相关API接口...</p></section>'; }
    createContentSection() { return '<section id="content" class="api-section"><h3>内容管理</h3><p>内容管理相关API接口...</p></section>'; }
    createMarketingSection() { return '<section id="marketing" class="api-section"><h3>营销工具</h3><p>营销工具相关API接口...</p></section>'; }
    createSecuritySection() { return '<section id="security" class="api-section"><h3>安全中心</h3><p>安全中心相关API接口...</p></section>'; }
    createMonitorSection() { return '<section id="monitor" class="api-section"><h3>系统监控</h3><p>系统监控相关API接口...</p></section>'; }
    createAnalyticsSection() { return '<section id="analytics" class="api-section"><h3>数据分析</h3><p>数据分析相关API接口...</p></section>'; }
    
    // API测试功能
    async testApi(endpoint, method = 'GET', data = null) {
        try {
            const options = {
                method: method,
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.token}`
                }
            };
            
            if (data && method !== 'GET') {
                options.body = JSON.stringify(data);
            }
            
            const response = await fetch(this.baseUrl + endpoint, options);
            const result = await response.json();
            
            this.displayTestResult(result, response.status);
        } catch (error) {
            this.displayTestResult({ error: error.message }, 500);
        }
    }
    
    displayTestResult(result, status) {
        const resultContainer = event.target.parentNode.querySelector('.api-test-result');
        const statusClass = status >= 200 && status < 300 ? 'success' : 'danger';
        
        resultContainer.innerHTML = `
            <div class="alert alert-${statusClass}">
                <strong>状态码: ${status}</strong>
                <pre class="mt-2 mb-0"><code>${JSON.stringify(result, null, 2)}</code></pre>
            </div>
        `;
    }
    
    // 登录测试
    async testLogin() {
        const email = document.getElementById('testEmail').value;
        const password = document.getElementById('testPassword').value;
        
        if (!email || !password) {
            this.showNotification('请填写邮箱和密码', 'warning');
            return;
        }
        
        try {
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ email, password })
            });
            
            const result = await response.json();
            
            const resultContainer = document.getElementById('loginTestResult');
            const statusClass = response.ok ? 'success' : 'danger';
            
            resultContainer.innerHTML = `
                <div class="alert alert-${statusClass}">
                    <strong>状态码: ${response.status}</strong>
                    <pre class="mt-2 mb-0"><code>${JSON.stringify(result, null, 2)}</code></pre>
                </div>
            `;
            
            if (response.ok && result.data.token) {
                this.token = result.data.token;
                localStorage.setItem('token', this.token);
                this.showNotification('登录成功，令牌已保存', 'success');
            }
        } catch (error) {
            this.showNotification('登录测试失败: ' + error.message, 'error');
        }
    }
    
    // SDK下载
    downloadSDK(language) {
        const sdkUrls = {
            javascript: '/downloads/writerpro-admin-sdk-js.zip',
            python: '/downloads/writerpro-admin-sdk-python.zip'
        };
        
        const url = sdkUrls[language];
        if (url) {
            const link = document.createElement('a');
            link.href = url;
            link.download = `writerpro-admin-sdk-${language}.zip`;
            link.click();
        }
    }
    
    viewSDKDocs(language) {
        const docUrls = {
            javascript: '/docs/sdk/javascript',
            python: '/docs/sdk/python'
        };
        
        const url = docUrls[language];
        if (url) {
            window.open(url, '_blank');
        }
    }
    
    // 滚动到指定部分
    scrollToSection(target) {
        const element = document.querySelector(target);
        if (element) {
            element.scrollIntoView({ behavior: 'smooth' });
        }
    }
    
    // 更新活跃导航
    updateActiveNav(activeLink) {
        document.querySelectorAll('.api-nav .nav-link').forEach(link => {
            link.classList.remove('active');
        });
        activeLink.classList.add('active');
    }
    
    // 初始化滚动监听
    initScrollSpy() {
        const sections = document.querySelectorAll('.api-section');
        const navLinks = document.querySelectorAll('.api-nav .nav-link');
        
        window.addEventListener('scroll', () => {
            let current = '';
            
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                const sectionHeight = section.clientHeight;
                if (window.pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });
            
            navLinks.forEach(link => {
                link.classList.remove('active');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.add('active');
                }
            });
        });
    }
    
    // 加载API统计
    loadApiStats() {
        // 模拟API统计数据
        const stats = {
            totalEndpoints: 45,
            categories: 12,
            lastUpdated: new Date().toLocaleDateString()
        };
        
        // 可以在页面上显示这些统计信息
    }
    
    // 主题切换
    toggleTheme() {
        const body = document.body;
        const currentTheme = body.getAttribute('data-bs-theme');
        const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
        
        body.setAttribute('data-bs-theme', newTheme);
        localStorage.setItem('theme', newTheme);
        
        const icon = document.querySelector('#themeToggle i');
        icon.className = newTheme === 'dark' ? 'bi bi-sun' : 'bi bi-moon';
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
}

// 全局函数
function testLogin() {
    apiDocsManager.testLogin();
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.apiDocsManager = new ApiDocsManager();
});

// 导出
window.ApiDocsManager = ApiDocsManager;
