/**
 * 安全中心页面JavaScript
 * 处理风控规则、敏感操作审批、数据备份等安全功能
 */

class SecurityManager {
    constructor() {
        this.currentTab = 'risk-rules';
        this.currentPage = 1;
        this.pageSize = 20;
        this.charts = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadSecurityOverview();
        this.loadRiskRules();
        this.loadApprovals();
        this.loadSecurityLogs();
        this.loadBackups();
        this.loadIpBlacklist();
        this.initCharts();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#securityTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 刷新按钮
        document.getElementById('refreshBtn')?.addEventListener('click', () => {
            this.loadSecurityOverview();
            this.handleTabSwitch();
        });
        
        // 表单提交
        document.getElementById('riskRuleForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.saveRiskRule();
        });
        
        document.getElementById('ipBlacklistForm')?.addEventListener('submit', (e) => {
            e.preventDefault();
            this.addIpToBlacklist();
        });
        
        // 添加按钮
        document.getElementById('addRiskRuleBtn')?.addEventListener('click', () => {
            this.showAddRiskRuleModal();
        });
        
        document.getElementById('addIpBtn')?.addEventListener('click', () => {
            this.showAddIpModal();
        });
        
        document.getElementById('createBackupBtn')?.addEventListener('click', () => {
            this.createBackup();
        });
        
        // 搜索和筛选
        document.getElementById('riskRuleSearchBtn')?.addEventListener('click', () => {
            this.loadRiskRules();
        });
        
        document.getElementById('approvalSearchBtn')?.addEventListener('click', () => {
            this.loadApprovals();
        });
        
        document.getElementById('logSearchBtn')?.addEventListener('click', () => {
            this.loadSecurityLogs();
        });
        
        // 状态筛选
        document.getElementById('riskRuleStatusFilter')?.addEventListener('change', () => {
            this.loadRiskRules();
        });
        
        document.getElementById('approvalStatusFilter')?.addEventListener('change', () => {
            this.loadApprovals();
        });
        
        document.getElementById('logLevelFilter')?.addEventListener('change', () => {
            this.loadSecurityLogs();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'risk-rules':
                this.loadRiskRules();
                break;
            case 'ip-blacklist':
                this.loadIpBlacklist();
                break;
            case 'sensitive-operations':
                this.loadApprovals();
                break;
            case 'security-logs':
                this.loadSecurityLogs();
                break;
            case 'data-backup':
                this.loadBackups();
                break;
        }
    }
    
    async loadSecurityOverview() {
        try {
            const response = await fetch('/api/security/overview');
            const result = await response.json();
            
            if (result.success) {
                this.updateOverviewCards(result.data);
            }
        } catch (error) {
            console.error('加载安全概览失败:', error);
        }
    }
    
    updateOverviewCards(data) {
        // 更新安全状态卡片
        const securityStatusCard = document.querySelector('.security-card:first-child .card-text');
        if (securityStatusCard) {
            securityStatusCard.textContent = data.securityLevel || '未知';
            securityStatusCard.className = `card-text text-${this.getSecurityLevelColor(data.securityLevel)}`;
        }
        
        // 更新待处理警报
        const alertsCard = document.querySelector('.security-card:nth-child(2) .card-text');
        if (alertsCard) {
            alertsCard.textContent = data.pendingApprovals || 0;
        }
        
        // 更新风险事件
        const riskEventsCard = document.querySelector('.security-card:nth-child(3) .card-text');
        if (riskEventsCard) {
            riskEventsCard.textContent = data.todayRisks || 0;
        }
        
        // 更新最后备份时间
        const backupCard = document.querySelector('.security-card:nth-child(4) .card-text');
        if (backupCard) {
            backupCard.textContent = data.lastBackup || '未知';
        }
        
        // 更新风险统计
        document.getElementById('highRiskCount').textContent = data.riskStats?.high || 0;
        document.getElementById('mediumRiskCount').textContent = data.riskStats?.medium || 0;
        document.getElementById('lowRiskCount').textContent = data.riskStats?.low || 0;
        document.getElementById('blockedCount').textContent = data.riskStats?.blocked || 0;
    }
    
    async loadRiskRules() {
        try {
            const search = document.getElementById('riskRuleSearch')?.value || '';
            const status = document.getElementById('riskRuleStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/security/risk-rules?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderRiskRulesTable(result.data);
                this.renderPagination(result.pagination, 'riskRulesPagination');
            }
        } catch (error) {
            console.error('加载风控规则失败:', error);
        }
    }
    
    renderRiskRulesTable(rules) {
        const tbody = document.getElementById('riskRulesTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = rules.map(rule => `
            <tr>
                <td>${rule.name}</td>
                <td>
                    <span class="badge bg-${this.getRuleTypeColor(rule.type)}">
                        ${this.getRuleTypeText(rule.type)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getRiskLevelColor(rule.riskLevel)}">
                        ${this.getRiskLevelText(rule.riskLevel)}
                    </span>
                </td>
                <td>${rule.condition}</td>
                <td>${rule.action}</td>
                <td>
                    <span class="badge bg-${rule.status === 'active' ? 'success' : 'secondary'}">
                        ${rule.status === 'active' ? '启用' : '禁用'}
                    </span>
                </td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="securityManager.editRiskRule('${rule._id}')">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-outline-${rule.status === 'active' ? 'warning' : 'success'}" 
                                onclick="securityManager.toggleRiskRule('${rule._id}')">
                            <i class="bi bi-${rule.status === 'active' ? 'pause' : 'play'}"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="securityManager.deleteRiskRule('${rule._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadApprovals() {
        try {
            const search = document.getElementById('approvalSearch')?.value || '';
            const status = document.getElementById('approvalStatusFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                status
            });
            
            const response = await fetch(`/api/security/approvals?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderApprovalsTable(result.data);
                this.renderPagination(result.pagination, 'approvalsPagination');
            }
        } catch (error) {
            console.error('加载操作审批失败:', error);
        }
    }
    
    renderApprovalsTable(approvals) {
        const tbody = document.getElementById('approvalsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = approvals.map(approval => `
            <tr>
                <td>${approval.operationType}</td>
                <td>${approval.description}</td>
                <td>${approval.applicant}</td>
                <td>
                    <span class="badge bg-${this.getPriorityColor(approval.priority)}">
                        ${this.getPriorityText(approval.priority)}
                    </span>
                </td>
                <td>
                    <span class="badge bg-${this.getApprovalStatusColor(approval.status)}">
                        ${this.getApprovalStatusText(approval.status)}
                    </span>
                </td>
                <td>${new Date(approval.createdAt).toLocaleString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-info" onclick="securityManager.viewApproval('${approval._id}')">
                            <i class="bi bi-eye"></i>
                        </button>
                        ${approval.status === 'pending' ? `
                            <button class="btn btn-outline-success" onclick="securityManager.approveOperation('${approval._id}')">
                                <i class="bi bi-check"></i>
                            </button>
                            <button class="btn btn-outline-danger" onclick="securityManager.rejectOperation('${approval._id}')">
                                <i class="bi bi-x"></i>
                            </button>
                        ` : ''}
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async loadSecurityLogs() {
        try {
            const search = document.getElementById('logSearch')?.value || '';
            const level = document.getElementById('logLevelFilter')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                level
            });
            
            const response = await fetch(`/api/security/logs?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderSecurityLogsTable(result.data);
                this.renderPagination(result.pagination, 'logsPagination');
            }
        } catch (error) {
            console.error('加载安全日志失败:', error);
        }
    }
    
    renderSecurityLogsTable(logs) {
        const tbody = document.getElementById('securityLogsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = logs.map(log => `
            <tr>
                <td>${new Date(log.timestamp).toLocaleString()}</td>
                <td>
                    <span class="badge bg-${this.getLogLevelColor(log.level)}">
                        ${this.getLogLevelText(log.level)}
                    </span>
                </td>
                <td>${log.event}</td>
                <td>${log.description}</td>
                <td>${log.source}</td>
                <td>${log.ip || '-'}</td>
                <td>
                    <button class="btn btn-outline-info btn-sm" onclick="securityManager.viewLogDetail('${log._id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    async loadBackups() {
        try {
            const response = await fetch('/api/security/backups');
            const result = await response.json();
            
            if (result.success) {
                this.renderBackupsTable(result.data);
            }
        } catch (error) {
            console.error('加载数据备份失败:', error);
        }
    }
    
    renderBackupsTable(backups) {
        const tbody = document.getElementById('backupsTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = backups.map(backup => `
            <tr>
                <td>${backup.name}</td>
                <td>${backup.type}</td>
                <td>${this.formatFileSize(backup.size)}</td>
                <td>
                    <span class="badge bg-${this.getBackupStatusColor(backup.status)}">
                        ${this.getBackupStatusText(backup.status)}
                    </span>
                </td>
                <td>${new Date(backup.createdAt).toLocaleString()}</td>
                <td>
                    <div class="btn-group btn-group-sm">
                        <button class="btn btn-outline-primary" onclick="securityManager.downloadBackup('${backup._id}')">
                            <i class="bi bi-download"></i>
                        </button>
                        <button class="btn btn-outline-info" onclick="securityManager.verifyBackup('${backup._id}')">
                            <i class="bi bi-check-circle"></i>
                        </button>
                        <button class="btn btn-outline-danger" onclick="securityManager.deleteBackup('${backup._id}')">
                            <i class="bi bi-trash"></i>
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }
    
    async createBackup() {
        try {
            const response = await fetch('/api/security/backups', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    type: 'manual',
                    description: '手动创建的备份'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('备份创建成功', 'success');
                this.loadBackups();
            } else {
                this.showNotification(result.message || '备份创建失败', 'error');
            }
        } catch (error) {
            console.error('创建备份失败:', error);
            this.showNotification('备份创建失败', 'error');
        }
    }
    
    async approveOperation(id) {
        if (!confirm('确定要批准此操作吗？')) return;
        
        try {
            const response = await fetch(`/api/security/approvals/${id}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'approve',
                    comment: '管理员批准'
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('操作已批准', 'success');
                this.loadApprovals();
            } else {
                this.showNotification(result.message || '批准失败', 'error');
            }
        } catch (error) {
            console.error('批准操作失败:', error);
            this.showNotification('批准失败', 'error');
        }
    }
    
    async rejectOperation(id) {
        const reason = prompt('请输入拒绝理由：');
        if (!reason) return;
        
        try {
            const response = await fetch(`/api/security/approvals/${id}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    action: 'reject',
                    comment: reason
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.showNotification('操作已拒绝', 'success');
                this.loadApprovals();
            } else {
                this.showNotification(result.message || '拒绝失败', 'error');
            }
        } catch (error) {
            console.error('拒绝操作失败:', error);
            this.showNotification('拒绝失败', 'error');
        }
    }
    
    initCharts() {
        // 初始化安全图表
        this.charts.riskTrend = null;
        this.charts.threatDistribution = null;
    }
    
    getSecurityLevelColor(level) {
        const colors = {
            '高': 'success',
            '中': 'warning',
            '低': 'danger'
        };
        return colors[level] || 'secondary';
    }
    
    getRuleTypeColor(type) {
        const colors = {
            'login': 'primary',
            'payment': 'warning',
            'api': 'info',
            'data': 'success'
        };
        return colors[type] || 'secondary';
    }
    
    getRuleTypeText(type) {
        const texts = {
            'login': '登录安全',
            'payment': '支付安全',
            'api': 'API安全',
            'data': '数据安全'
        };
        return texts[type] || type;
    }
    
    getRiskLevelColor(level) {
        const colors = {
            'high': 'danger',
            'medium': 'warning',
            'low': 'info'
        };
        return colors[level] || 'secondary';
    }
    
    getRiskLevelText(level) {
        const texts = {
            'high': '高风险',
            'medium': '中风险',
            'low': '低风险'
        };
        return texts[level] || level;
    }
    
    getPriorityColor(priority) {
        const colors = {
            'urgent': 'danger',
            'high': 'warning',
            'normal': 'info',
            'low': 'secondary'
        };
        return colors[priority] || 'secondary';
    }
    
    getPriorityText(priority) {
        const texts = {
            'urgent': '紧急',
            'high': '高',
            'normal': '普通',
            'low': '低'
        };
        return texts[priority] || priority;
    }
    
    getApprovalStatusColor(status) {
        const colors = {
            'pending': 'warning',
            'approved': 'success',
            'rejected': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    getApprovalStatusText(status) {
        const texts = {
            'pending': '待审批',
            'approved': '已批准',
            'rejected': '已拒绝'
        };
        return texts[status] || status;
    }
    
    getLogLevelColor(level) {
        const colors = {
            'error': 'danger',
            'warning': 'warning',
            'info': 'info',
            'debug': 'secondary'
        };
        return colors[level] || 'secondary';
    }
    
    getLogLevelText(level) {
        const texts = {
            'error': '错误',
            'warning': '警告',
            'info': '信息',
            'debug': '调试'
        };
        return texts[level] || level;
    }
    
    getBackupStatusColor(status) {
        const colors = {
            'completed': 'success',
            'running': 'primary',
            'failed': 'danger'
        };
        return colors[status] || 'secondary';
    }
    
    getBackupStatusText(status) {
        const texts = {
            'completed': '已完成',
            'running': '进行中',
            'failed': '失败'
        };
        return texts[status] || status;
    }
    
    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination, containerId) {
        const paginationEl = document.getElementById(containerId);
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="securityManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="securityManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="securityManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        this.handleTabSwitch();
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.securityManager = new SecurityManager();
});

// 导出
window.SecurityManager = SecurityManager;
