/**
 * 批量操作组件
 * 为各个管理模块提供通用的批量选择和批量操作功能
 */

class BatchOperations {
    constructor(options = {}) {
        this.selectedItems = new Set();
        this.tableSelector = options.tableSelector || 'table';
        this.checkboxSelector = options.checkboxSelector || '.batch-checkbox';
        this.selectAllSelector = options.selectAllSelector || '#selectAll';
        this.batchActionsSelector = options.batchActionsSelector || '#batchActions';
        this.selectedCountSelector = options.selectedCountSelector || '#selectedCount';
        this.clearSelectionSelector = options.clearSelectionSelector || '#clearSelection';
        this.onSelectionChange = options.onSelectionChange || (() => {});
        this.onBatchAction = options.onBatchAction || (() => {});
        
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.createBatchActionsBar();
    }
    
    bindEvents() {
        // 全选/取消全选
        document.addEventListener('change', (e) => {
            if (e.target.matches(this.selectAllSelector)) {
                this.toggleSelectAll(e.target.checked);
            }
        });
        
        // 单个复选框变化
        document.addEventListener('change', (e) => {
            if (e.target.matches(this.checkboxSelector)) {
                this.toggleItemSelection(e.target.value, e.target.checked);
            }
        });
        
        // 清除选择
        document.addEventListener('click', (e) => {
            if (e.target.matches(this.clearSelectionSelector)) {
                this.clearSelection();
            }
        });
        
        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.ctrlKey || e.metaKey) {
                switch (e.key) {
                    case 'a':
                        if (e.target.closest(this.tableSelector)) {
                            e.preventDefault();
                            this.selectAll();
                        }
                        break;
                    case 'Escape':
                        this.clearSelection();
                        break;
                }
            }
        });
    }
    
    createBatchActionsBar() {
        const existingBar = document.querySelector(this.batchActionsSelector);
        if (existingBar) return;
        
        const batchActionsHTML = `
            <div id="batchActions" class="batch-actions-bar" style="display: none;">
                <div class="d-flex justify-content-between align-items-center p-3 bg-light border-bottom">
                    <div class="batch-info">
                        <i class="bi bi-check-square me-2"></i>
                        已选择 <strong id="selectedCount">0</strong> 项
                    </div>
                    <div class="batch-buttons d-flex gap-2">
                        <div class="dropdown">
                            <button class="btn btn-primary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                批量操作
                            </button>
                            <ul class="dropdown-menu" id="batchActionsMenu">
                                <!-- 动态生成批量操作选项 -->
                            </ul>
                        </div>
                        <button class="btn btn-outline-secondary btn-sm" id="clearSelection">
                            <i class="bi bi-x"></i> 取消选择
                        </button>
                    </div>
                </div>
            </div>
        `;
        
        // 插入到表格容器前面
        const tableContainer = document.querySelector(this.tableSelector)?.closest('.card-body, .table-responsive');
        if (tableContainer) {
            tableContainer.insertAdjacentHTML('afterbegin', batchActionsHTML);
        }
    }
    
    addBatchAction(action) {
        const menu = document.getElementById('batchActionsMenu');
        if (!menu) return;
        
        const actionItem = document.createElement('li');
        actionItem.innerHTML = `
            <a class="dropdown-item" href="#" data-action="${action.key}">
                <i class="bi bi-${action.icon}"></i> ${action.label}
            </a>
        `;
        
        actionItem.addEventListener('click', (e) => {
            e.preventDefault();
            this.executeBatchAction(action.key);
        });
        
        menu.appendChild(actionItem);
    }
    
    setBatchActions(actions) {
        const menu = document.getElementById('batchActionsMenu');
        if (!menu) return;
        
        menu.innerHTML = '';
        actions.forEach(action => this.addBatchAction(action));
    }
    
    toggleItemSelection(itemId, selected) {
        if (selected) {
            this.selectedItems.add(itemId);
        } else {
            this.selectedItems.delete(itemId);
        }
        
        this.updateUI();
        this.onSelectionChange(Array.from(this.selectedItems));
    }
    
    toggleSelectAll(selectAll) {
        const checkboxes = document.querySelectorAll(this.checkboxSelector);
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = selectAll;
            this.toggleItemSelection(checkbox.value, selectAll);
        });
    }
    
    selectAll() {
        const selectAllCheckbox = document.querySelector(this.selectAllSelector);
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = true;
            this.toggleSelectAll(true);
        }
    }
    
    clearSelection() {
        this.selectedItems.clear();
        
        // 取消所有复选框选中状态
        document.querySelectorAll(this.checkboxSelector).forEach(checkbox => {
            checkbox.checked = false;
        });
        
        // 取消全选复选框
        const selectAllCheckbox = document.querySelector(this.selectAllSelector);
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
        
        this.updateUI();
        this.onSelectionChange([]);
    }
    
    updateUI() {
        const batchActions = document.querySelector(this.batchActionsSelector);
        const selectedCount = document.querySelector(this.selectedCountSelector);
        const selectAllCheckbox = document.querySelector(this.selectAllSelector);
        
        // 更新选中数量显示
        if (selectedCount) {
            selectedCount.textContent = this.selectedItems.size;
        }
        
        // 显示/隐藏批量操作栏
        if (batchActions) {
            if (this.selectedItems.size > 0) {
                batchActions.style.display = 'block';
            } else {
                batchActions.style.display = 'none';
            }
        }
        
        // 更新全选复选框状态
        if (selectAllCheckbox) {
            const allCheckboxes = document.querySelectorAll(this.checkboxSelector);
            const checkedCount = Array.from(allCheckboxes).filter(cb => cb.checked).length;
            
            if (checkedCount === 0) {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = false;
            } else if (checkedCount === allCheckboxes.length) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            } else {
                selectAllCheckbox.checked = false;
                selectAllCheckbox.indeterminate = true;
            }
        }
    }
    
    executeBatchAction(actionKey) {
        if (this.selectedItems.size === 0) {
            this.showNotification('请先选择要操作的项目', 'warning');
            return;
        }
        
        this.onBatchAction(actionKey, Array.from(this.selectedItems));
    }
    
    getSelectedItems() {
        return Array.from(this.selectedItems);
    }
    
    getSelectedCount() {
        return this.selectedItems.size;
    }
    
    selectItems(itemIds) {
        itemIds.forEach(id => {
            this.selectedItems.add(id);
            const checkbox = document.querySelector(`${this.checkboxSelector}[value="${id}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
        
        this.updateUI();
        this.onSelectionChange(Array.from(this.selectedItems));
    }
    
    deselectItems(itemIds) {
        itemIds.forEach(id => {
            this.selectedItems.delete(id);
            const checkbox = document.querySelector(`${this.checkboxSelector}[value="${id}"]`);
            if (checkbox) {
                checkbox.checked = false;
            }
        });
        
        this.updateUI();
        this.onSelectionChange(Array.from(this.selectedItems));
    }
    
    // 批量操作确认对话框
    showBatchConfirmDialog(action, message, callback) {
        const modal = document.createElement('div');
        modal.className = 'modal fade';
        modal.innerHTML = `
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">确认批量操作</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle"></i>
                            ${message}
                        </div>
                        <p>您即将对 <strong>${this.selectedItems.size}</strong> 个项目执行 <strong>${action}</strong> 操作。</p>
                        <p>此操作可能无法撤销，请确认是否继续？</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="button" class="btn btn-danger" id="confirmBatchAction">确认执行</button>
                    </div>
                </div>
            </div>
        `;
        
        document.body.appendChild(modal);
        
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
        
        modal.querySelector('#confirmBatchAction').addEventListener('click', () => {
            bootstrapModal.hide();
            callback();
        });
        
        modal.addEventListener('hidden.bs.modal', () => {
            modal.remove();
        });
    }
    
    // 批量操作进度显示
    showBatchProgress(total, current, action) {
        let progressModal = document.getElementById('batchProgressModal');
        
        if (!progressModal) {
            progressModal = document.createElement('div');
            progressModal.id = 'batchProgressModal';
            progressModal.className = 'modal fade';
            progressModal.setAttribute('data-bs-backdrop', 'static');
            progressModal.innerHTML = `
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">批量操作进行中</h5>
                        </div>
                        <div class="modal-body">
                            <div class="mb-3">
                                <div class="d-flex justify-content-between">
                                    <span id="progressAction">执行中...</span>
                                    <span id="progressText">0 / 0</span>
                                </div>
                                <div class="progress mt-2">
                                    <div class="progress-bar" id="progressBar" style="width: 0%"></div>
                                </div>
                            </div>
                            <div id="progressDetails" class="small text-muted">
                                <!-- 详细进度信息 -->
                            </div>
                        </div>
                    </div>
                </div>
            `;
            
            document.body.appendChild(progressModal);
        }
        
        const modal = bootstrap.Modal.getOrCreateInstance(progressModal);
        modal.show();
        
        const progressBar = progressModal.querySelector('#progressBar');
        const progressText = progressModal.querySelector('#progressText');
        const progressAction = progressModal.querySelector('#progressAction');
        
        const percentage = Math.round((current / total) * 100);
        
        progressBar.style.width = percentage + '%';
        progressText.textContent = `${current} / ${total}`;
        progressAction.textContent = action;
        
        if (current >= total) {
            setTimeout(() => {
                modal.hide();
            }, 1000);
        }
    }
    
    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 
                          type === 'warning' ? 'alert-warning' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 3000);
    }
    
    destroy() {
        // 清理事件监听器和DOM元素
        const batchActions = document.querySelector(this.batchActionsSelector);
        if (batchActions) {
            batchActions.remove();
        }
        
        this.selectedItems.clear();
    }
}

// 导出
window.BatchOperations = BatchOperations;
