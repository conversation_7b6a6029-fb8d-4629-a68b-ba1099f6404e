/**
 * 语言切换组件
 */
import { languages, changeLanguage, getCurrentLanguage, t } from '../i18n.js';

/**
 * 创建语言切换组件
 * @param {string} containerId 容器ID
 */
export const createLanguageSwitcher = (containerId) => {
  const container = document.getElementById(containerId);
  if (!container) return;

  // 创建语言切换下拉菜单
  const currentLang = getCurrentLanguage();
  const currentLangObj = languages.find(lang => lang.code === currentLang) || languages[0];

  // 创建下拉菜单容器
  const dropdown = document.createElement('div');
  dropdown.className = 'language-dropdown';
  
  // 创建当前语言显示
  const selected = document.createElement('div');
  selected.className = 'language-selected';
  selected.innerHTML = `
    <span>${currentLangObj.name}</span>
    <i class="bi bi-chevron-down"></i>
  `;
  
  // 创建下拉菜单选项
  const options = document.createElement('div');
  options.className = 'language-options';
  
  // 添加语言选项
  languages.forEach(lang => {
    const option = document.createElement('div');
    option.className = 'language-option';
    if (lang.code === currentLang) {
      option.classList.add('active');
    }
    option.setAttribute('data-lang', lang.code);
    option.textContent = lang.name;
    
    // 点击切换语言
    option.addEventListener('click', () => {
      changeLanguage(lang.code);
      selected.querySelector('span').textContent = lang.name;
      
      // 更新活跃状态
      options.querySelectorAll('.language-option').forEach(opt => {
        opt.classList.remove('active');
      });
      option.classList.add('active');
      
      // 关闭下拉菜单
      dropdown.classList.remove('active');
    });
    
    options.appendChild(option);
  });
  
  // 点击显示/隐藏下拉菜单
  selected.addEventListener('click', () => {
    dropdown.classList.toggle('active');
  });
  
  // 点击外部关闭下拉菜单
  document.addEventListener('click', (e) => {
    if (!dropdown.contains(e.target)) {
      dropdown.classList.remove('active');
    }
  });
  
  // 组装组件
  dropdown.appendChild(selected);
  dropdown.appendChild(options);
  container.appendChild(dropdown);
  
  // 添加样式
  addLanguageSwitcherStyles();
};

/**
 * 添加语言切换组件样式
 */
const addLanguageSwitcherStyles = () => {
  // 检查是否已添加样式
  if (document.getElementById('language-switcher-styles')) return;
  
  const style = document.createElement('style');
  style.id = 'language-switcher-styles';
  style.textContent = `
    .language-dropdown {
      position: relative;
      min-width: 100px;
      cursor: pointer;
    }
    
    .language-selected {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 8px 12px;
      background-color: #f5f5f5;
      border-radius: 4px;
      font-size: 14px;
    }
    
    .language-options {
      position: absolute;
      top: 100%;
      left: 0;
      right: 0;
      background-color: #fff;
      border-radius: 4px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
      margin-top: 4px;
      display: none;
      z-index: 1000;
    }
    
    .language-dropdown.active .language-options {
      display: block;
    }
    
    .language-option {
      padding: 8px 12px;
      font-size: 14px;
      transition: background-color 0.2s;
    }
    
    .language-option:hover {
      background-color: #f5f5f5;
    }
    
    .language-option.active {
      background-color: #e6f7ff;
      color: #1677ff;
    }
  `;
  
  document.head.appendChild(style);
};

export default createLanguageSwitcher; 