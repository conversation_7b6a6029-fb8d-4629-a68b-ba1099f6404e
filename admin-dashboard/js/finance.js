/**
 * 财务管理页面JavaScript
 * 处理财务概览、交易记录、退款管理、财务报表、消费分析等功能
 */

class FinanceManager {
    constructor() {
        this.currentPage = 1;
        this.pageSize = 20;
        this.currentTab = 'overview';
        this.charts = {};
        this.init();
    }
    
    init() {
        this.bindEvents();
        this.loadFinanceOverview();
        this.loadTransactions();
        this.loadRefunds();
        this.initCharts();
    }
    
    bindEvents() {
        // 选项卡切换
        document.querySelectorAll('#financeTabs button').forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.currentTab = e.target.getAttribute('data-bs-target').replace('#', '');
                this.handleTabSwitch();
            });
        });
        
        // 搜索和筛选
        document.getElementById('transactionSearchBtn')?.addEventListener('click', () => {
            this.loadTransactions();
        });
        
        document.getElementById('transactionTypeFilter')?.addEventListener('change', () => {
            this.loadTransactions();
        });
        
        document.getElementById('transactionStatusFilter')?.addEventListener('change', () => {
            this.loadTransactions();
        });
        
        // 退款筛选
        document.getElementById('refundStatusFilter')?.addEventListener('change', () => {
            this.loadRefunds();
        });
        
        // 日期范围选择
        document.getElementById('dateRangeBtn')?.addEventListener('click', () => {
            this.applyDateRange();
        });
        
        // 报表类型切换
        document.getElementById('reportType')?.addEventListener('change', () => {
            this.loadFinanceReports();
        });
        
        document.getElementById('reportYear')?.addEventListener('change', () => {
            this.loadFinanceReports();
        });
    }
    
    handleTabSwitch() {
        switch(this.currentTab) {
            case 'overview':
                this.loadFinanceOverview();
                break;
            case 'transactions':
                this.loadTransactions();
                break;
            case 'refunds':
                this.loadRefunds();
                break;
            case 'reports':
                this.loadFinanceReports();
                break;
            case 'analytics':
                this.loadConsumptionAnalysis();
                break;
        }
    }
    
    async loadFinanceOverview() {
        try {
            const startDate = document.getElementById('startDate')?.value || '';
            const endDate = document.getElementById('endDate')?.value || '';

            const params = new URLSearchParams();
            if (startDate) params.append('startDate', startDate);
            if (endDate) params.append('endDate', endDate);

            // 使用真实的财务API
            const response = await fetch(`/api/real-finance/overview?${params}`);
            const result = await response.json();

            if (result.success) {
                this.updateOverviewCards(result.data);
                this.updateRevenueChart(result.data);
            } else {
                console.error('加载财务概览失败:', result.message);
                this.showNotification('加载财务概览失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载财务概览失败:', error);
            this.showNotification('加载财务概览失败', 'error');
        }
    }
    
    updateOverviewCards(data) {
        // 更新主要财务指标
        const elements = {
            'totalRevenue': `¥${(data.totalRevenue || 0).toLocaleString()}`,
            'totalRefund': `¥${(data.totalRefund || 0).toLocaleString()}`,
            'netRevenue': `¥${(data.netRevenue || 0).toLocaleString()}`,
            'totalOrders': (data.totalOrders || 0).toLocaleString(),
            'totalUsers': (data.totalUsers || 0).toLocaleString()
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });

        // 更新支付方式统计
        if (data.paymentMethodStats) {
            this.updatePaymentMethodStats(data.paymentMethodStats);
        }
    }
    
    async loadTransactions() {
        try {
            const search = document.getElementById('transactionSearch')?.value || '';
            const type = document.getElementById('transactionTypeFilter')?.value || '';
            const status = document.getElementById('transactionStatusFilter')?.value || '';
            const startDate = document.getElementById('transactionStartDate')?.value || '';
            const endDate = document.getElementById('transactionEndDate')?.value || '';
            
            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                search,
                type,
                status,
                startDate,
                endDate
            });
            
            // 使用真实的财务API
            const response = await fetch(`/api/real-finance/transactions?${params}`);
            const result = await response.json();

            if (result.success) {
                this.renderTransactionTable(result.data);
                this.renderPagination(result.pagination, 'transactionPagination');
            } else {
                console.error('加载交易记录失败:', result.message);
                this.showNotification('加载交易记录失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载交易记录失败:', error);
            this.showNotification('加载交易记录失败', 'error');
        }
    }
    
    renderTransactionTable(transactions) {
        const tbody = document.getElementById('transactionTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = transactions.map(transaction => `
            <tr>
                <td>${transaction.transactionId || transaction._id}</td>
                <td>${transaction.userId}</td>
                <td>
                    <span class="badge bg-${this.getTypeColor(transaction.type)}">
                        ${this.getTypeText(transaction.type)}
                    </span>
                </td>
                <td>¥${transaction.amount}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(transaction.status)}">
                        ${this.getStatusText(transaction.status)}
                    </span>
                </td>
                <td>${transaction.paymentMethod || '-'}</td>
                <td>${new Date(transaction.createdAt).toLocaleString()}</td>
                <td>
                    <button class="btn btn-outline-info btn-sm" onclick="financeManager.viewTransactionDetail('${transaction._id}')">
                        <i class="bi bi-eye"></i>
                    </button>
                </td>
            </tr>
        `).join('');
    }
    
    async loadRefunds() {
        try {
            const status = document.getElementById('refundStatusFilter')?.value || '';
            const startDate = document.getElementById('refundStartDate')?.value || '';
            const endDate = document.getElementById('refundEndDate')?.value || '';

            const params = new URLSearchParams({
                page: this.currentPage,
                limit: this.pageSize,
                status,
                startDate,
                endDate
            });

            // 使用真实的财务API
            const response = await fetch(`/api/real-finance/refunds?${params}`);
            const result = await response.json();

            if (result.success) {
                this.renderRefundTable(result.data);
                this.renderPagination(result.pagination, 'refundPagination');

                // 更新退款统计
                if (result.stats) {
                    this.updateRefundStats(result.stats);
                }
            } else {
                console.error('加载退款记录失败:', result.message);
                this.showNotification('加载退款记录失败: ' + result.message, 'error');
            }
        } catch (error) {
            console.error('加载退款记录失败:', error);
            this.showNotification('加载退款记录失败', 'error');
        }
    }
    
    renderRefundTable(refunds) {
        const tbody = document.getElementById('refundTableBody');
        if (!tbody) return;
        
        tbody.innerHTML = refunds.map(refund => `
            <tr>
                <td>${refund.orderId}</td>
                <td>${refund.username}</td>
                <td>¥${refund.amount}</td>
                <td>${refund.reason}</td>
                <td>
                    <span class="badge bg-${this.getStatusColor(refund.status)}">
                        ${this.getStatusText(refund.status)}
                    </span>
                </td>
                <td>${new Date(refund.submittedAt).toLocaleString()}</td>
                <td>
                    ${refund.status === 'pending' ? `
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-success" onclick="financeManager.processRefund('${refund._id}', 'approve')">
                                <i class="bi bi-check"></i> 批准
                            </button>
                            <button class="btn btn-outline-danger" onclick="financeManager.processRefund('${refund._id}', 'reject')">
                                <i class="bi bi-x"></i> 拒绝
                            </button>
                        </div>
                    ` : `
                        <button class="btn btn-outline-info btn-sm" onclick="financeManager.viewRefundDetail('${refund._id}')">
                            <i class="bi bi-eye"></i> 查看
                        </button>
                    `}
                </td>
            </tr>
        `).join('');
    }
    
    async processRefund(refundId, action) {
        try {
            const reason = action === 'reject' ?
                prompt('请输入拒绝原因:') : '符合退款条件，已批准';

            if (action === 'reject' && !reason) return;

            // 使用真实的财务API
            const response = await fetch(`/api/real-finance/refunds/${refundId}/process`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ action, reason })
            });

            const result = await response.json();

            if (result.success) {
                this.showNotification(result.message, 'success');
                this.loadRefunds(); // 重新加载退款列表
            } else {
                this.showNotification(result.message || '操作失败', 'error');
            }
        } catch (error) {
            console.error('处理退款失败:', error);
            this.showNotification('操作失败', 'error');
        }
    }
    
    async loadFinanceReports() {
        try {
            const type = document.getElementById('reportType')?.value || 'monthly';
            const year = document.getElementById('reportYear')?.value || new Date().getFullYear();
            
            const params = new URLSearchParams({ type, year });
            
            const response = await fetch(`/api/finance/reports?${params}`);
            const result = await response.json();
            
            if (result.success) {
                this.renderReportsChart(result.data);
                this.renderReportsTable(result.data);
            }
        } catch (error) {
            console.error('加载财务报表失败:', error);
        }
    }
    
    async loadConsumptionAnalysis() {
        try {
            const response = await fetch('/api/finance/consumption-analysis');
            const result = await response.json();
            
            if (result.success) {
                this.renderConsumptionAnalysis(result.data);
            }
        } catch (error) {
            console.error('加载消费分析失败:', error);
        }
    }
    
    initCharts() {
        // 初始化图表容器
        this.charts.revenue = null;
        this.charts.pie = null;
        this.charts.reports = null;
    }
    
    getTypeColor(type) {
        const colors = {
            'income': 'success',
            'expense': 'danger',
            'refund': 'warning'
        };
        return colors[type] || 'secondary';
    }
    
    getTypeText(type) {
        const texts = {
            'income': '收入',
            'expense': '支出',
            'refund': '退款'
        };
        return texts[type] || type;
    }
    
    getStatusColor(status) {
        const colors = {
            'completed': 'success',
            'pending': 'warning',
            'failed': 'danger',
            'rejected': 'secondary'
        };
        return colors[status] || 'secondary';
    }
    
    getStatusText(status) {
        const texts = {
            'completed': '已完成',
            'pending': '待处理',
            'failed': '失败',
            'rejected': '已拒绝'
        };
        return texts[status] || status;
    }

    // 更新退款统计显示
    updateRefundStats(stats) {
        const elements = {
            'pendingRefunds': stats.pendingRefunds || 0,
            'totalRefundAmount': `¥${(stats.totalRefundAmount || 0).toLocaleString()}`,
            'refundRate': `${stats.refundRate || 0}%`,
            'avgRefundTime': `${stats.avgRefundTime || 0}天`
        };

        Object.entries(elements).forEach(([id, value]) => {
            const element = document.getElementById(id);
            if (element) {
                element.textContent = value;
            }
        });
    }

    // 更新支付方式统计
    updatePaymentMethodStats(stats) {
        // 这里可以添加支付方式分布图表
        console.log('支付方式统计:', stats);
    }

    // 添加增长率指示器更新
    updateGrowthIndicators(data) {
        // 这里可以添加增长率计算和显示逻辑
        console.log('增长率数据:', data);
    }

    showNotification(message, type = 'info') {
        const alertClass = type === 'success' ? 'alert-success' : 
                          type === 'error' ? 'alert-danger' : 'alert-info';
        
        const alert = document.createElement('div');
        alert.className = `alert ${alertClass} alert-dismissible fade show position-fixed`;
        alert.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        alert.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        document.body.appendChild(alert);
        
        setTimeout(() => {
            alert.remove();
        }, 5000);
    }
    
    renderPagination(pagination, containerId) {
        const paginationEl = document.getElementById(containerId);
        if (!paginationEl) return;
        
        let html = '';
        
        if (pagination.page > 1) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="financeManager.goToPage(${pagination.page - 1})">上一页</a>
            </li>`;
        }
        
        for (let i = 1; i <= pagination.pages; i++) {
            if (i === pagination.page) {
                html += `<li class="page-item active">
                    <span class="page-link">${i}</span>
                </li>`;
            } else {
                html += `<li class="page-item">
                    <a class="page-link" href="#" onclick="financeManager.goToPage(${i})">${i}</a>
                </li>`;
            }
        }
        
        if (pagination.page < pagination.pages) {
            html += `<li class="page-item">
                <a class="page-link" href="#" onclick="financeManager.goToPage(${pagination.page + 1})">下一页</a>
            </li>`;
        }
        
        paginationEl.innerHTML = html;
    }
    
    goToPage(page) {
        this.currentPage = page;
        if (this.currentTab === 'transactions') {
            this.loadTransactions();
        } else if (this.currentTab === 'refunds') {
            this.loadRefunds();
        }
    }
}

// 初始化
document.addEventListener('DOMContentLoaded', () => {
    window.financeManager = new FinanceManager();
});

// 导出
window.FinanceManager = FinanceManager;
