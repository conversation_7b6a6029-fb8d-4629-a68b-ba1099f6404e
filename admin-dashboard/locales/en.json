{"common": {"dashboard": "Dashboard", "users": "User Management", "agents": "Agent Management", "roles": "Role Management", "security": "Security Center", "content": "Content Management", "api": "API Configuration", "marketing": "Marketing Tools", "finance": "Financial Management", "monitor": "System Monitor", "settings": "System Settings", "logout": "Logout", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "reset": "Reset", "status": "Status", "actions": "Actions", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "loading": "Loading...", "noData": "No Data", "welcome": "Welcome", "language": "Language", "export": "Export", "import": "Import", "refresh": "Refresh", "view": "View", "create": "Create", "update": "Update", "enable": "Enable", "disable": "Disable", "active": "Active", "inactive": "Inactive", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information"}, "login": {"title": "<PERSON><PERSON>", "subtitle": "Login to access admin features", "username": "Username", "usernamePlaceholder": "Enter admin username", "password": "Password", "passwordPlaceholder": "Enter password", "rememberMe": "Remember Me", "forgotPassword": "Forgot Password?", "loginButton": "<PERSON><PERSON>", "loginSuccess": "Login successful", "loginFailed": "<PERSON><PERSON> failed", "invalidCredentials": "Invalid username or password", "accountLocked": "Account is locked", "twoFactorRequired": "Two-factor authentication required"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome back, Administrator", "overview": "Here's today's data overview", "statistics": "Statistics", "recentActivity": "Recent Activity", "systemStatus": "System Status", "totalUsers": "Total Users", "activeUsers": "Active Users", "newUsers": "New Users", "totalRevenue": "Total Revenue", "monthlyRevenue": "Monthly Revenue", "apiCalls": "API Calls", "errorRate": "Error Rate", "responseTime": "Response Time", "uptime": "Uptime", "cpuUsage": "CPU Usage", "memoryUsage": "Memory Usage", "diskUsage": "Disk Usage", "networkTraffic": "Network Traffic"}, "users": {"title": "User Management", "userList": "User List", "userAnalytics": "User Analytics", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "username": "Username", "email": "Email", "role": "Role", "status": "Status", "createdAt": "Created At", "lastLogin": "Last Login", "totalConsumption": "Total Consumption", "searchUsers": "Search Users", "filterByRole": "Filter by Role", "filterByStatus": "Filter by Status", "batchOperations": "Batch Operations", "selectedUsers": "Selected Users", "exportUsers": "Export User Data", "userGrowth": "User Growth", "userActivity": "User Activity", "userRetention": "User Retention", "userDistribution": "User Distribution"}, "agents": {"title": "Agent Management", "agentList": "Agent List", "agentApplications": "Agent Applications", "commissionSettings": "Commission Settings", "performanceAnalysis": "Performance Analysis", "addAgent": "Add Agent", "editAgent": "Edit Agent", "agentName": "Agent Name", "contactPerson": "Contact Person", "contactPhone": "Contact Phone", "agentLevel": "Agent Level", "commissionRate": "Commission Rate", "totalSales": "Total Sales", "totalCommission": "Total Commission", "approveApplication": "Approve Application", "rejectApplication": "Reject Application", "viewDetails": "View Details"}, "finance": {"title": "Financial Management", "overview": "Financial Overview", "transactions": "Transactions", "refunds": "Refund Management", "reports": "Financial Reports", "revenue": "Revenue", "expenses": "Expenses", "profit": "Profit", "refundAmount": "Refund Amount", "refundReason": "Refund Reason", "processRefund": "Process Refund", "approveRefund": "Approve Refund", "rejectRefund": "Reject Refund", "transactionId": "Transaction ID", "amount": "Amount", "paymentMethod": "Payment Method", "transactionTime": "Transaction Time", "monthlyReport": "Monthly Report", "yearlyReport": "Yearly Report", "consumptionAnalysis": "Consumption Analysis", "conversionRate": "Conversion Rate"}, "settings": {"title": "System Settings", "basicSettings": "Basic Settings", "pricingSettings": "Pricing Settings", "paymentSettings": "Payment Settings", "systemParameters": "System Parameters", "notificationSettings": "Notification Settings", "operationLogs": "Operation Logs", "siteName": "Site Name", "siteDescription": "Site Description", "contactEmail": "Contact Email", "contactPhone": "Contact Phone", "companyAddress": "Company Address", "icpNumber": "ICP Number", "copyright": "Copyright", "timezone": "Timezone", "defaultLanguage": "Default Language", "maintenanceMode": "Maintenance Mode", "saveSettings": "Save Settings", "settingsSaved": "Settings Saved"}, "content": {"title": "Content Management", "agreements": "Agreement Management", "announcements": "Announcement Management", "qrCodes": "QR Code Management", "pageContent": "Page Content", "addAgreement": "Add Agreement", "editAgreement": "Edit Agreement", "agreementTitle": "Agreement Title", "agreementContent": "Agreement Content", "agreementVersion": "Agreement Version", "publishAgreement": "Publish Agreement", "addAnnouncement": "Add Announcement", "editAnnouncement": "Edit Announcement", "announcementTitle": "Announcement Title", "announcementContent": "Announcement Content", "priority": "Priority", "publishTime": "Publish Time", "customerServiceQR": "Customer Service QR", "wechatGroupQR": "WeChat Group QR", "officialAccountQR": "Official Account QR"}, "api": {"title": "API Configuration", "googleApi": "Google API", "paymentConfig": "Payment Configuration", "usageStats": "Usage Statistics", "keyManagement": "Key Management", "webhooks": "Webhooks", "apiKey": "API Key", "apiSecret": "API Secret", "testConnection": "Test Connection", "connectionSuccess": "Connection Successful", "connectionFailed": "Connection Failed", "generateKey": "Generate Key", "rotateKey": "Rotate Key", "revokeKey": "Revoke Key", "apiCalls": "API Calls", "errorCount": "Error Count", "avgResponseTime": "Average Response Time"}, "marketing": {"title": "Marketing Tools", "coupons": "Coupons", "campaigns": "Campaigns", "referrals": "Referral Program", "points": "Points System", "analytics": "Marketing Analytics", "createCoupon": "Create Coupon", "couponCode": "Coupon Code", "discountType": "Discount Type", "discountValue": "Discount Value", "usageLimit": "Usage Limit", "validPeriod": "Valid Period", "createCampaign": "Create Campaign", "campaignName": "Campaign Name", "campaignBudget": "Campaign Budget", "targetAudience": "Target Audience", "inviteReward": "<PERSON><PERSON><PERSON>", "pointsEarned": "Points Earned", "pointsSpent": "Points Spent", "conversionRate": "Conversion Rate", "roi": "Return on Investment"}, "security": {"title": "Security Center", "riskRules": "Risk Rules", "ipBlacklist": "IP Blacklist", "sensitiveOperations": "Sensitive Operations", "securityLogs": "Security Logs", "dataBackup": "Data Backup", "securityLevel": "Security Level", "riskEvents": "Risk Events", "pendingApprovals": "Pending Approvals", "lastBackup": "Last Backup", "addRule": "Add Rule", "ruleName": "Rule Name", "riskLevel": "Risk Level", "triggerCondition": "Trigger Condition", "actionTaken": "Action Taken", "addIpToBlacklist": "Add to Blacklist", "removeFromBlacklist": "<PERSON><PERSON><PERSON> from Blacklist", "approveOperation": "Approve Operation", "rejectOperation": "Reject Operation", "createBackup": "Create Backup", "restoreBackup": "Restore Backup", "downloadBackup": "Download Backup"}, "messages": {"confirmDelete": "Are you sure you want to delete?", "deleteSuccess": "Deleted successfully", "deleteFailed": "Delete failed", "saveSuccess": "Saved successfully", "saveFailed": "Save failed", "updateSuccess": "Updated successfully", "updateFailed": "Update failed", "createSuccess": "Created successfully", "createFailed": "Create failed", "operationSuccess": "Operation successful", "operationFailed": "Operation failed", "networkError": "Network error", "serverError": "Server error", "validationError": "Validation error", "permissionDenied": "Permission denied", "sessionExpired": "Session expired", "pleaseLogin": "Please login first"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "phone": "Please enter a valid phone number", "minLength": "Minimum {min} characters required", "maxLength": "Maximum {max} characters allowed", "numeric": "Please enter a number", "positive": "Please enter a positive number", "url": "Please enter a valid URL", "password": "Password must contain at least 8 characters including letters and numbers"}}