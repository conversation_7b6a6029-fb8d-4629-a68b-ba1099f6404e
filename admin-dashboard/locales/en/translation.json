{"common": {"dashboard": "Dashboard", "users": "User Management", "agents": "Agent Management", "roles": "Role Management", "security": "Security Center", "content": "Content Management", "api": "API Configuration", "marketing": "Marketing Tools", "finance": "Finance Management", "monitor": "System Monitor", "settings": "System Settings", "logout": "Logout", "save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "reset": "Reset", "status": "Status", "actions": "Actions", "confirm": "Confirm", "back": "Back", "next": "Next", "submit": "Submit", "loading": "Loading...", "noData": "No Data", "welcome": "Welcome", "language": "Language"}, "login": {"title": "<PERSON><PERSON>", "subtitle": "Login to access management features", "username": "Username", "usernamePlaceholder": "Enter admin username", "password": "Password", "passwordPlaceholder": "Enter password", "remember": "Remember me", "login": "<PERSON><PERSON>", "forgotPassword": "Forgot Password?", "twoFactorTitle": "Two-Factor Authentication", "twoFactorDesc": "Please enter the 6-digit verification code", "verify": "Verify", "resend": "Resend", "countdown": "Countdown", "seconds": "seconds", "expired": "Expired", "codeValidTime": "Code valid for", "securityTip": "Security tip: Do not login to admin panel in public places", "infoDesc": "Comprehensive management features to boost your business", "feature1": "User management and data analysis", "feature2": "Agent recruitment and commission settings", "feature3": "Financial statistics and payment management", "feature4": "System settings and content management"}, "dashboard": {"title": "Dashboard", "welcome": "Welcome to WriterPro Admin Dashboard", "totalUsers": "Total Users", "activeUsers": "Active Users", "totalContent": "Total Content", "totalApiKeys": "Total API Keys", "totalCoupons": "Total Coupons", "blacklistedIp": "Blacklisted IPs", "recentUsers": "Recent Users", "recentContent": "Recent Content"}, "users": {"title": "User Management", "addUser": "Add User", "editUser": "Edit User", "deleteUser": "Delete User", "username": "Username", "name": "Name", "email": "Email", "role": "Role", "status": "Status", "lastLogin": "Last Login", "createdAt": "Created At", "password": "Password", "confirmPassword": "Confirm Password", "active": "Active", "inactive": "Inactive", "locked": "Locked", "admin": "Admin", "financeAdmin": "Finance Admin", "contentAdmin": "Content Admin", "customerService": "Customer Service", "agent": "Agent", "twoFactorAuth": "Two-Factor Authentication", "enable": "Enable", "disable": "Disable"}, "security": {"title": "Security Center", "ipBlacklist": "IP Blacklist", "addIp": "Add IP", "ip": "IP Address", "reason": "Reason", "createdBy": "Created By", "createdAt": "Created At", "expiryDate": "Expiry Date", "permanent": "Permanent", "expired": "Expired", "active": "Active", "auditLogs": "<PERSON><PERSON>", "dataBackup": "Data Backup", "createBackup": "Create Backup", "restoreBackup": "Restore Backup", "backupList": "Backup List", "backupName": "Backup Name", "backupSize": "Backup Size", "backupTime": "Backup Time", "sensitiveOps": "Sensitive Operations Approval", "operation": "Operation", "operator": "Operator", "operationTime": "Operation Time", "status": "Status", "approve": "Approve", "reject": "Reject", "pending": "Pending", "approved": "Approved", "rejected": "Rejected"}, "content": {"title": "Content Management", "agreements": "Agreement Management", "announcements": "Announcement Management", "qrcode": "Customer Service QR Code", "pageContent": "Page Content", "addContent": "Add Content", "editContent": "Edit Content", "deleteContent": "Delete Content", "contentTitle": "Title", "contentType": "Type", "version": "Version", "status": "Status", "publishedAt": "Published At", "createdBy": "Created By", "createdAt": "Created At", "updatedAt": "Updated At", "content": "Content", "draft": "Draft", "published": "Published", "publish": "Publish", "saveDraft": "Save Draft", "preview": "Preview", "history": "History"}, "api": {"title": "API Configuration", "googleApi": "Google API Configuration", "paymentApi": "Payment API Configuration", "apiStats": "API Usage Statistics", "clientKeys": "Client API Keys", "addApiKey": "Add API Key", "editApiKey": "Edit API Key", "deleteApiKey": "Delete API Key", "name": "Name", "key": "Key", "type": "Type", "provider": "Provider", "services": "Services", "limit": "Limit", "status": "Status", "createdBy": "Created By", "createdAt": "Created At", "expiryDate": "Expiry Date", "lastUsed": "Last Used", "active": "Active", "inactive": "Inactive", "regenerate": "Regenerate Key", "google": "Google", "payment": "Payment", "client": "Client"}, "marketing": {"title": "Marketing Tools", "coupons": "Coupon Management", "campaigns": "Promotion Campaigns", "referral": "Referral Program", "points": "Points System", "addCoupon": "Add Coupon", "editCoupon": "Edit Coupon", "deleteCoupon": "Delete Coupon", "code": "Coupon Code", "name": "Name", "type": "Type", "value": "Value", "limitType": "Limit Type", "startDate": "Start Date", "endDate": "End Date", "totalAmount": "Total Amount", "usedAmount": "Used Amount", "description": "Description", "status": "Status", "createdBy": "Created By", "createdAt": "Created At", "percentage": "Percentage", "fixed": "Fixed Amount", "free": "Free", "none": "No Limit", "new": "New User", "vip": "VIP User", "custom": "Custom", "active": "Active", "pending": "Pending", "expired": "Expired", "disabled": "Disabled"}, "notifications": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "loginSuccess": "Login successful, redirecting...", "loginError": "Username or password is incorrect", "verifySuccess": "Verification successful, redirecting...", "verifyError": "Verification code is incorrect", "saveSuccess": "Save successful", "saveError": "Save failed", "deleteSuccess": "Delete successful", "deleteError": "Delete failed", "operationSuccess": "Operation successful", "operationError": "Operation failed", "requiredField": "Required fields cannot be empty", "networkError": "Network error, please try again later", "serverError": "Server error, please try again later", "codeSent": "Verification code has been sent", "sendCodeError": "Failed to send verification code"}}