<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学术专家服务管理 - WriterPro</title>
    <link rel="stylesheet" href="css/admin.css">
    <style>
        .expert-orders-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .page-header {
            background: linear-gradient(135deg, #8A2BE2, #9932CC);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #8A2BE2;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .orders-table {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .table-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .table-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .table-content {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
        }

        th, td {
            padding: 15px;
            text-align: left;
            border-bottom: 1px solid #f0f0f0;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-processing {
            background: #fff3cd;
            color: #856404;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .action-buttons {
            display: flex;
            gap: 8px;
        }

        .btn {
            padding: 6px 12px;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 12px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 4px;
            transition: all 0.3s;
        }

        .btn-primary {
            background: #8A2BE2;
            color: white;
        }

        .btn-primary:hover {
            background: #7B1FA2;
        }

        .btn-success {
            background: #28a745;
            color: white;
        }

        .btn-success:hover {
            background: #218838;
        }

        .btn-danger {
            background: #dc3545;
            color: white;
        }

        .btn-danger:hover {
            background: #c82333;
        }

        .upload-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }

        .file-upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .file-upload-area:hover {
            border-color: #8A2BE2;
            background: #f8f9ff;
        }

        .file-upload-area.dragover {
            border-color: #8A2BE2;
            background: #f0f8ff;
        }

        .upload-icon {
            font-size: 48px;
            color: #ddd;
            margin-bottom: 10px;
        }

        .upload-text {
            color: #666;
            margin-bottom: 10px;
        }

        .file-info {
            display: none;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            margin-top: 10px;
        }

        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            margin-top: 20px;
        }

        .pagination button {
            padding: 8px 12px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 6px;
            cursor: pointer;
        }

        .pagination button:hover {
            background: #f8f9fa;
        }

        .pagination button.active {
            background: #8A2BE2;
            color: white;
            border-color: #8A2BE2;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #666;
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .files-list {
            max-height: 200px;
            overflow-y: auto;
        }

        .file-item {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
            margin-bottom: 8px;
            border: 1px solid #e0e0e0;
        }

        .file-item:last-child {
            margin-bottom: 0;
        }

        .file-info-item {
            display: flex;
            align-items: center;
            gap: 8px;
            flex: 1;
        }

        .file-icon {
            font-size: 20px;
        }

        .file-details {
            flex: 1;
        }

        .file-name {
            font-weight: 500;
            color: #333;
            margin-bottom: 2px;
        }

        .file-size {
            font-size: 12px;
            color: #666;
        }

        .file-remove {
            background: #dc3545;
            color: white;
            border: none;
            border-radius: 4px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
        }

        .file-remove:hover {
            background: #c82333;
        }
    </style>
</head>
<body>
    <div class="expert-orders-container">
        <!-- 页面头部 -->
        <div class="page-header">
            <h1>🎓 学术专家服务管理</h1>
            <p>管理和处理学术专家润色服务订单</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProcessing">-</div>
                <div class="stat-label">处理中订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCompleted">-</div>
                <div class="stat-label">已完成订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRevenue">-</div>
                <div class="stat-label">总收入 (元)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgProcessTime">-</div>
                <div class="stat-label">平均处理时间</div>
            </div>
        </div>

        <!-- 订单表格 -->
        <div class="orders-table">
            <div class="table-header">
                <h2 class="table-title">处理中的订单</h2>
            </div>
            <div class="table-content">
                <div id="loadingState" class="loading">
                    <div>📊 正在加载订单数据...</div>
                </div>
                <div id="emptyState" class="empty-state" style="display: none;">
                    <div class="empty-icon">📝</div>
                    <h3>暂无处理中的订单</h3>
                    <p>所有订单都已处理完成</p>
                </div>
                <table id="ordersTable" style="display: none;">
                    <thead>
                        <tr>
                            <th>订单ID</th>
                            <th>文档名</th>
                            <th>创建时间</th>
                            <th>状态</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody id="ordersTableBody">
                    </tbody>
                </table>
            </div>
            <div class="pagination" id="pagination" style="display: none;"></div>
        </div>
    </div>

    <!-- 上传结果弹窗 -->
    <div id="uploadModal" class="upload-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">上传处理结果</h3>
            </div>
            <form id="uploadForm">
                <input type="hidden" id="currentOrderId" value="">
                
                <div class="form-group">
                    <label class="form-label">订单信息</label>
                    <div id="orderInfo" style="background: #f8f9fa; padding: 10px; border-radius: 6px; font-size: 14px;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">处理结果文件 (最多2个)</label>
                    <div class="file-upload-area" id="fileUploadArea">
                        <div class="upload-icon">📄</div>
                        <div class="upload-text">点击选择文件或拖拽文件到此处</div>
                        <div style="font-size: 12px; color: #999;">支持 PDF, DOCX, TXT 格式，最多选择2个文件</div>
                    </div>
                    <input type="file" id="fileInput" accept=".docx,.pdf,.txt" multiple style="display: none;">
                    <div class="files-list" id="filesList" style="margin-top: 10px;"></div>
                </div>

                <div class="form-group">
                    <button type="submit" class="btn btn-primary" style="width: 100%; padding: 12px;">
                        📤 上传并完成订单
                    </button>
                </div>

                <div class="form-group">
                    <button type="button" class="btn" onclick="closeUploadModal()" style="width: 100%; padding: 12px; background: #6c757d; color: white;">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 防止被重定向的临时脚本
        console.log('管理后台页面加载中...');
        console.log('当前URL:', window.location.href);

        // 检查是否被意外重定向
        if (window.location.pathname !== '/admin/expert-orders.html') {
            console.warn('检测到可能的重定向，当前路径:', window.location.pathname);
        }
    </script>
    <script src="js/expert-orders.js?v=20250727"></script>
</body>
</html>
