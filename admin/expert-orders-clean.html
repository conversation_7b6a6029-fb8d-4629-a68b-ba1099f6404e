<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学术专家服务管理 - WriterPro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #8A2BE2, #9932CC);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .content-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .content-body {
            padding: 40px;
        }

        .status-message {
            background: #d4edda;
            color: #155724;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #8A2BE2;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #7B1FA2;
            transform: translateY(-2px);
        }

        .debug-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }

        .upload-area {
            border: 2px dashed #ddd;
            border-radius: 8px;
            padding: 30px;
            text-align: center;
            margin: 20px 0;
            cursor: pointer;
            transition: all 0.3s;
        }

        .upload-area:hover {
            border-color: #8A2BE2;
            background: #f8f9ff;
        }

        .file-input {
            display: none;
        }

        .orders-list {
            margin-top: 20px;
        }

        .order-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #8A2BE2;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .order-info {
            flex: 1;
        }

        .order-header {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .order-meta {
            font-size: 12px;
            color: #666;
        }

        .order-actions {
            display: flex;
            gap: 10px;
        }

        .btn-small {
            padding: 6px 12px;
            font-size: 12px;
        }

        .btn-success {
            background: #28a745;
        }

        .btn-success:hover {
            background: #218838;
        }

        .modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1000;
        }

        .modal-content {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: white;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
        }

        .modal-header {
            margin-bottom: 20px;
        }

        .modal-title {
            font-size: 20px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 12px;
            border: 1px solid #ddd;
            border-radius: 8px;
            font-size: 14px;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🎓 学术专家服务管理</h1>
            <p>管理和处理学术专家润色服务订单</p>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">处理中的订单</h2>
            </div>
            <div class="content-body">
                <div class="status-message">
                    <strong>✅ 页面加载成功！</strong><br>
                    这是完全独立的管理后台页面，不依赖任何外部CSS或JS文件。
                </div>

                <div id="ordersContainer">
                    <div class="orders-list" id="ordersList">
                        <div class="order-item">
                            <div class="order-info">
                                <div class="order-header">订单 #1001 - 演示数据</div>
                                <div class="order-meta">
                                    用户: 张三 | 金额: ¥299.00 | 创建时间: 2025-07-21 15:30:00
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-small btn-success" onclick="openUploadModal(1001)">
                                    📤 上传结果
                                </button>
                            </div>
                        </div>

                        <div class="order-item">
                            <div class="order-info">
                                <div class="order-header">订单 #1002 - 演示数据</div>
                                <div class="order-meta">
                                    用户: 李四 | 金额: ¥399.00 | 创建时间: 2025-07-21 14:20:00
                                </div>
                            </div>
                            <div class="order-actions">
                                <button class="btn btn-small btn-success" onclick="openUploadModal(1002)">
                                    📤 上传结果
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="loadRealData()">🔄 刷新订单数据</button>
                    <button class="btn" onclick="testAPI()">🧪 测试API连接</button>
                    <button class="btn" onclick="goToLogin()" style="background: #6c757d;">🔐 管理员登录</button>
                    <button class="btn" onclick="logout()" style="background: #dc3545;">🚪 退出登录</button>
                </div>

                <div class="debug-info">
                    <strong>调试信息：</strong><br>
                    当前URL: <span id="currentUrl"></span><br>
                    页面路径: <span id="currentPath"></span><br>
                    加载时间: <span id="loadTime"></span><br>
                    状态: 页面正常加载，未发生重定向
                </div>
            </div>
        </div>
    </div>

    <!-- 上传弹窗 -->
    <div id="uploadModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3 class="modal-title">上传处理结果</h3>
            </div>
            <form id="uploadForm">
                <input type="hidden" id="currentOrderId" value="">
                
                <div class="form-group">
                    <label class="form-label">订单信息</label>
                    <div id="orderInfo" style="background: #f8f9fa; padding: 10px; border-radius: 6px; font-size: 14px;"></div>
                </div>

                <div class="form-group">
                    <label class="form-label">处理结果文件</label>
                    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
                        <div style="font-size: 48px; margin-bottom: 10px;">📄</div>
                        <div>点击选择文件或拖拽文件到此处</div>
                        <div style="font-size: 12px; color: #999; margin-top: 5px;">支持 .docx, .pdf, .txt 格式</div>
                    </div>
                    <input type="file" id="fileInput" class="file-input" accept=".docx,.pdf,.txt">
                </div>

                <div class="form-group">
                    <button type="submit" class="btn" style="width: 100%; padding: 12px;">
                        📤 上传并完成订单
                    </button>
                </div>

                <div class="form-group">
                    <button type="button" class="btn" onclick="closeUploadModal()" style="width: 100%; padding: 12px; background: #6c757d;">
                        取消
                    </button>
                </div>
            </form>
        </div>
    </div>

    <script>
        // 显示调试信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentPath').textContent = window.location.pathname;
        document.getElementById('loadTime').textContent = new Date().toLocaleString('zh-CN');

        console.log('✅ 管理后台页面加载成功');
        console.log('当前URL:', window.location.href);
        console.log('页面路径:', window.location.pathname);

        // 页面加载完成后检查登录状态并加载数据
        window.addEventListener('load', function() {
            console.log('页面完全加载，检查登录状态...');
            checkLoginStatus();
        });

        // 检查登录状态
        function checkLoginStatus() {
            const token = localStorage.getItem('adminToken');
            const user = localStorage.getItem('adminUser');

            if (token && user) {
                try {
                    const userInfo = JSON.parse(user);
                    console.log('已登录用户:', userInfo);
                    showLoginStatus(userInfo);
                    loadRealData();
                } catch (error) {
                    console.error('用户信息解析失败:', error);
                    showLoginPrompt();
                }
            } else {
                console.log('未登录，显示登录提示');
                showLoginPrompt();
            }
        }

        // 显示登录状态
        function showLoginStatus(userInfo) {
            const statusMessage = document.querySelector('.status-message');
            statusMessage.innerHTML = `
                <strong>✅ 欢迎，${userInfo.name}！</strong><br>
                您已成功登录管理后台，角色：${userInfo.role}
            `;
            statusMessage.className = 'status-message';
        }

        // 显示登录提示
        function showLoginPrompt() {
            const statusMessage = document.querySelector('.status-message');
            statusMessage.innerHTML = `
                <strong>🔐 需要管理员权限</strong><br>
                请先登录管理员账户才能查看和管理订单。
            `;
            statusMessage.style.background = '#fff3cd';
            statusMessage.style.color = '#856404';
            statusMessage.style.borderColor = '#ffeaa7';
        }

        // 跳转到登录页面
        function goToLogin() {
            window.location.href = 'login.html';
        }

        // 退出登录
        function logout() {
            if (confirm('确定要退出登录吗？')) {
                localStorage.removeItem('adminToken');
                localStorage.removeItem('adminUser');
                alert('已退出登录');
                checkLoginStatus();
            }
        }

        // API基础URL
        const API_BASE_URL = '/api';

        // 打开上传弹窗
        function openUploadModal(orderId, username = '', amount = 0) {
            document.getElementById('currentOrderId').value = orderId;
            document.getElementById('orderInfo').innerHTML = `
                <strong>订单 #${orderId}</strong><br>
                用户: ${username || '未知用户'}<br>
                金额: ¥${amount.toFixed(2)}
            `;
            document.getElementById('uploadModal').style.display = 'block';
        }

        // 关闭上传弹窗
        function closeUploadModal() {
            document.getElementById('uploadModal').style.display = 'none';
        }

        // 加载真实数据
        async function loadRealData() {
            try {
                console.log('尝试加载真实订单数据...');

                // 尝试从localStorage获取token，如果没有就使用demo token
                const token = localStorage.getItem('adminToken') || 'demo-token';

                const response = await fetch(`${API_BASE_URL}/payments/orders/processing/list?page=1&pageSize=20`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    console.log('API响应:', data);

                    if (data.success && data.orders) {
                        displayRealOrders(data.orders);
                        alert(`✅ 成功加载 ${data.orders.length} 个处理中的订单`);
                    } else {
                        alert('⚠️ API返回数据格式异常');
                    }
                } else {
                    console.log('API返回状态:', response.status);
                    if (response.status === 401) {
                        alert('⚠️ 需要管理员登录权限');
                    } else {
                        alert(`⚠️ API返回状态: ${response.status}`);
                    }
                }

            } catch (error) {
                console.error('API调用失败:', error);
                alert('❌ API调用失败: ' + error.message);
            }
        }

        // 显示真实订单数据
        function displayRealOrders(orders) {
            const ordersList = document.getElementById('ordersList');

            if (orders.length === 0) {
                ordersList.innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📋</div>
                        <h3>暂无处理中的订单</h3>
                        <p>所有订单都已处理完成</p>
                    </div>
                `;
                return;
            }

            ordersList.innerHTML = orders.map(order => `
                <div class="order-item">
                    <div class="order-info">
                        <div class="order-header">订单 #${order.id}</div>
                        <div class="order-meta">
                            用户: ${order.user?.username || '未知用户'} |
                            邮箱: ${order.user?.email || '未知'} |
                            金额: ¥${order.amount?.toFixed(2) || '0.00'} |
                            创建时间: ${formatDate(order.createdAt)}
                        </div>
                    </div>
                    <div class="order-actions">
                        <button class="btn btn-small btn-success" onclick="openUploadModal(${order.id}, '${order.user?.username || ''}', ${order.amount || 0})">
                            📤 上传结果
                        </button>
                    </div>
                </div>
            `).join('');
        }

        // 格式化日期
        function formatDate(dateString) {
            if (!dateString) return '未知时间';
            const date = new Date(dateString);
            return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        // 测试API连接
        async function testAPI() {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    alert('✅ API连接正常');
                } else {
                    alert('⚠️ API连接异常');
                }
            } catch (error) {
                alert('❌ API连接失败: ' + error.message);
            }
        }

        // 文件上传处理
        document.getElementById('uploadForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const orderId = document.getElementById('currentOrderId').value;
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                alert('请选择要上传的文件');
                return;
            }

            // 验证文件类型
            const allowedTypes = ['.docx', '.pdf', '.txt'];
            const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

            if (!allowedTypes.includes(fileExtension)) {
                alert('不支持的文件格式，请选择 .docx, .pdf 或 .txt 文件');
                return;
            }

            // 验证文件大小 (最大50MB)
            if (file.size > 50 * 1024 * 1024) {
                alert('文件大小不能超过50MB');
                return;
            }

            try {
                // 显示上传进度
                const submitBtn = e.target.querySelector('button[type="submit"]');
                const originalText = submitBtn.innerHTML;
                submitBtn.innerHTML = '📤 上传中...';
                submitBtn.disabled = true;

                console.log('开始上传文件:', file.name, '订单ID:', orderId);

                // 创建FormData
                const formData = new FormData();
                formData.append('file', file);
                formData.append('orderId', orderId);

                // 获取token
                const token = localStorage.getItem('adminToken') || 'demo-token';

                // 上传文件
                const response = await fetch(`${API_BASE_URL}/payments/orders/${orderId}/result`, {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${token}`
                    },
                    body: formData
                });

                if (response.ok) {
                    const result = await response.json();

                    if (result.success) {
                        alert(`✅ 处理结果上传成功！\n文件: ${file.name}\n订单: #${orderId}`);
                        closeUploadModal();
                        // 重新加载订单列表
                        loadRealData();
                    } else {
                        throw new Error(result.message || '上传失败');
                    }
                } else {
                    throw new Error(`上传失败，状态码: ${response.status}`);
                }

            } catch (error) {
                console.error('上传失败:', error);
                alert('❌ 上传失败: ' + error.message);
            } finally {
                // 恢复按钮状态
                const submitBtn = e.target.querySelector('button[type="submit"]');
                submitBtn.innerHTML = originalText;
                submitBtn.disabled = false;
            }
        });

        // 点击模态框外部关闭
        document.getElementById('uploadModal').addEventListener('click', function(e) {
            if (e.target === this) {
                closeUploadModal();
            }
        });
    </script>
</body>
</html>
