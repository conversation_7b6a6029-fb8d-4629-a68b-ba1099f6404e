<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>学术专家服务管理 - WriterPro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f7fa;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #8A2BE2, #9932CC);
            color: white;
            padding: 30px;
            border-radius: 15px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #8A2BE2;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 14px;
        }

        .main-content {
            background: white;
            border-radius: 15px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .content-header {
            background: #f8f9fa;
            padding: 20px;
            border-bottom: 1px solid #e0e0e0;
        }

        .content-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin: 0;
        }

        .content-body {
            padding: 40px;
            text-align: center;
        }

        .status-message {
            background: #d1ecf1;
            color: #0c5460;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            border: 1px solid #bee5eb;
        }

        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #8A2BE2;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: all 0.3s;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        .btn:hover {
            background: #7B1FA2;
            transform: translateY(-2px);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .loading {
            display: inline-block;
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #8A2BE2;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .debug-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-top: 20px;
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }

        .orders-list {
            margin-top: 20px;
        }

        .order-item {
            background: #f8f9fa;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 8px;
            border-left: 4px solid #8A2BE2;
        }

        .order-header {
            font-weight: bold;
            margin-bottom: 5px;
        }

        .order-meta {
            font-size: 12px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 页面头部 -->
        <div class="header">
            <h1>🎓 学术专家服务管理</h1>
            <p>管理和处理学术专家润色服务订单</p>
        </div>

        <!-- 统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-number" id="totalProcessing">-</div>
                <div class="stat-label">处理中订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalCompleted">-</div>
                <div class="stat-label">已完成订单</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalRevenue">-</div>
                <div class="stat-label">总收入 (元)</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="avgProcessTime">-</div>
                <div class="stat-label">平均处理时间</div>
            </div>
        </div>

        <!-- 主要内容 -->
        <div class="main-content">
            <div class="content-header">
                <h2 class="content-title">处理中的订单</h2>
            </div>
            <div class="content-body">
                <div class="status-message">
                    <strong>✅ 页面加载成功！</strong><br>
                    管理后台页面已正常加载，nginx配置工作正常。
                </div>

                <div id="loadingState">
                    <div class="loading"></div>
                    <p>正在加载订单数据...</p>
                </div>

                <div id="ordersContainer" style="display: none;">
                    <div class="orders-list" id="ordersList">
                        <!-- 订单列表将在这里显示 -->
                    </div>
                </div>

                <div id="errorState" style="display: none;">
                    <p>⚠️ 加载订单数据时出现问题</p>
                    <button class="btn" onclick="retryLoad()">🔄 重试</button>
                </div>

                <div class="debug-info">
                    <strong>调试信息：</strong><br>
                    当前URL: <span id="currentUrl"></span><br>
                    页面路径: <span id="currentPath"></span><br>
                    加载时间: <span id="loadTime"></span><br>
                    用户代理: <span id="userAgent"></span>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 显示调试信息
        document.getElementById('currentUrl').textContent = window.location.href;
        document.getElementById('currentPath').textContent = window.location.pathname;
        document.getElementById('loadTime').textContent = new Date().toLocaleString('zh-CN');
        document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 100) + '...';

        // API基础URL
        const API_BASE_URL = '/api';

        // 模拟加载订单数据
        async function loadOrders() {
            try {
                // 显示加载状态
                document.getElementById('loadingState').style.display = 'block';
                document.getElementById('ordersContainer').style.display = 'none';
                document.getElementById('errorState').style.display = 'none';

                // 模拟API调用延迟
                await new Promise(resolve => setTimeout(resolve, 2000));

                // 尝试获取真实数据
                const token = localStorage.getItem('adminToken') || 'demo-token';
                
                console.log('尝试获取订单数据...');
                
                const response = await fetch(`${API_BASE_URL}/payments/orders/processing/list?page=1&pageSize=20`, {
                    headers: {
                        'Authorization': `Bearer ${token}`,
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    displayOrders(data.orders || []);
                } else {
                    throw new Error(`API返回错误: ${response.status}`);
                }

            } catch (error) {
                console.error('加载订单失败:', error);
                showError(error.message);
            }
        }

        function displayOrders(orders) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('errorState').style.display = 'none';
            document.getElementById('ordersContainer').style.display = 'block';

            const ordersList = document.getElementById('ordersList');
            
            if (orders.length === 0) {
                ordersList.innerHTML = '<p>暂无处理中的订单</p>';
                return;
            }

            ordersList.innerHTML = orders.map(order => `
                <div class="order-item">
                    <div class="order-header">订单 #${order.id}</div>
                    <div class="order-meta">
                        用户: ${order.user?.username || '未知'} | 
                        金额: ¥${order.amount?.toFixed(2) || '0.00'} | 
                        创建时间: ${new Date(order.createdAt).toLocaleString('zh-CN')}
                    </div>
                </div>
            `).join('');

            // 更新统计数据
            document.getElementById('totalProcessing').textContent = orders.length;
        }

        function showError(message) {
            document.getElementById('loadingState').style.display = 'none';
            document.getElementById('ordersContainer').style.display = 'none';
            document.getElementById('errorState').style.display = 'block';
            document.getElementById('errorState').innerHTML = `
                <p>⚠️ ${message}</p>
                <button class="btn" onclick="retryLoad()">🔄 重试</button>
                <button class="btn btn-secondary" onclick="showDemo()">📊 显示演示数据</button>
            `;
        }

        function retryLoad() {
            loadOrders();
        }

        function showDemo() {
            const demoOrders = [
                {
                    id: 1001,
                    user: { username: '张三' },
                    amount: 299.00,
                    createdAt: new Date().toISOString()
                },
                {
                    id: 1002,
                    user: { username: '李四' },
                    amount: 399.00,
                    createdAt: new Date(Date.now() - 3600000).toISOString()
                }
            ];
            displayOrders(demoOrders);
        }

        // 页面加载完成后开始加载数据
        window.addEventListener('load', () => {
            console.log('管理后台页面加载完成');
            loadOrders();
        });
    </script>
</body>
</html>
