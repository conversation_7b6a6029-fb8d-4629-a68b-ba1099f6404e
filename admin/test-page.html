<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }
        h1 {
            color: #8A2BE2;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #c3e6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #bee5eb;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #8A2BE2;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            margin: 10px;
            transition: all 0.3s;
        }
        .btn:hover {
            background: #7B1FA2;
            transform: translateY(-2px);
        }
        .test-results {
            margin-top: 30px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 管理后台测试页面</h1>
        
        <div class="status">
            ✅ 恭喜！如果您能看到这个页面，说明管理后台路径配置已经正常工作！
        </div>

        <div class="info">
            <strong>测试信息：</strong><br>
            • 当前页面：/admin/test-page.html<br>
            • 访问时间：<span id="currentTime"></span><br>
            • 页面状态：正常加载<br>
            • nginx配置：已生效
        </div>

        <div style="text-align: center;">
            <a href="expert-orders.html" class="btn">📊 进入专家订单管理</a>
            <a href="index.html" class="btn">🏠 管理后台首页</a>
            <a href="../index.html" class="btn">🌐 返回网站首页</a>
        </div>

        <div class="test-results">
            <h3>🔧 技术测试结果</h3>
            <div id="testResults">
                <p>正在进行技术测试...</p>
            </div>
        </div>
    </div>

    <script>
        // 显示当前时间
        document.getElementById('currentTime').textContent = new Date().toLocaleString('zh-CN');

        // 进行技术测试
        function runTests() {
            const results = [];
            
            // 测试1：检查当前URL
            results.push(`✅ 当前URL: ${window.location.href}`);
            
            // 测试2：检查是否被重定向
            if (window.location.pathname === '/admin/test-page.html') {
                results.push('✅ URL路径正确，未被重定向');
            } else {
                results.push('❌ URL路径异常，可能被重定向');
            }
            
            // 测试3：检查CSS加载
            const stylesheets = document.styleSheets.length;
            results.push(`✅ 样式表数量: ${stylesheets}`);
            
            // 测试4：检查JavaScript执行
            results.push('✅ JavaScript正常执行');
            
            // 测试5：测试API连接
            testAPI().then(apiResult => {
                results.push(apiResult);
                displayResults(results);
            }).catch(() => {
                results.push('⚠️ API连接测试失败（可能需要登录）');
                displayResults(results);
            });
        }

        async function testAPI() {
            try {
                const response = await fetch('/api/health');
                if (response.ok) {
                    return '✅ API连接正常';
                } else {
                    return '⚠️ API返回错误状态';
                }
            } catch (error) {
                return '⚠️ API连接失败';
            }
        }

        function displayResults(results) {
            document.getElementById('testResults').innerHTML = results.map(r => `<p>${r}</p>`).join('');
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
