const express = require('express');
const mysql = require('mysql2/promise');
const bcrypt = require('bcrypt');
const jwt = require('jsonwebtoken');
const crypto = require('crypto');

class AdminRoutes {
    constructor(dbPool) {
        this.router = express.Router();
        this.dbPool = dbPool;
        this.jwtSecret = process.env.JWT_SECRET || 'your-secret-key-change-in-production';
        this.setupRoutes();
    }

    // JWT验证中间件
    authenticateToken(req, res, next) {
        const authHeader = req.headers['authorization'];
        const token = authHeader && authHeader.split(' ')[1];

        if (!token) {
            return res.status(401).json({ error: '需要登录' });
        }

        jwt.verify(token, this.jwtSecret, (err, user) => {
            if (err) {
                return res.status(403).json({ error: '无效的令牌' });
            }
            req.user = user;
            next();
        });
    }

    setupRoutes() {
        // 管理员登录
        this.router.post('/login', async (req, res) => {
            try {
                const { username, password } = req.body;
                
                if (!username || !password) {
                    return res.status(400).json({ error: '用户名和密码不能为空' });
                }

                const [users] = await this.dbPool.execute(
                    'SELECT * FROM admin_users WHERE username = ? AND status = "active"',
                    [username]
                );

                if (users.length === 0) {
                    return res.status(401).json({ error: '用户名或密码错误' });
                }

                const user = users[0];
                const validPassword = await bcrypt.compare(password, user.password_hash);

                if (!validPassword) {
                    return res.status(401).json({ error: '用户名或密码错误' });
                }

                // 更新最后登录时间
                await this.dbPool.execute(
                    'UPDATE admin_users SET last_login_at = NOW() WHERE id = ?',
                    [user.id]
                );

                // 生成JWT令牌
                const token = jwt.sign(
                    { 
                        id: user.id, 
                        username: user.username, 
                        role: user.role 
                    },
                    this.jwtSecret,
                    { expiresIn: '24h' }
                );

                res.json({
                    success: true,
                    token: token,
                    user: {
                        id: user.id,
                        username: user.username,
                        email: user.email,
                        role: user.role
                    }
                });

            } catch (error) {
                console.error('登录错误:', error);
                res.status(500).json({ error: '登录失败' });
            }
        });

        // 获取仪表板数据
        this.router.get('/dashboard', this.authenticateToken.bind(this), async (req, res) => {
            try {
                // 企业总数
                const [enterpriseCount] = await this.dbPool.execute(
                    'SELECT COUNT(*) as total, SUM(CASE WHEN status = "active" THEN 1 ELSE 0 END) as active FROM api_keys'
                );

                // 今日调用统计
                const [todayCalls] = await this.dbPool.execute(
                    'SELECT COUNT(*) as total, COUNT(CASE WHEN error_code IS NULL THEN 1 END) as success FROM usage_logs WHERE DATE(created_at) = CURDATE()'
                );

                // 本月调用统计
                const [monthCalls] = await this.dbPool.execute(
                    'SELECT COUNT(*) as total FROM usage_logs WHERE YEAR(created_at) = YEAR(CURDATE()) AND MONTH(created_at) = MONTH(CURDATE())'
                );

                // 企业使用排行
                const [topEnterprises] = await this.dbPool.execute(`
                    SELECT 
                        enterprise_name,
                        COUNT(*) as calls_today,
                        AVG(ai_probability) as avg_ai_prob
                    FROM usage_logs 
                    WHERE DATE(created_at) = CURDATE() AND error_code IS NULL
                    GROUP BY enterprise_name 
                    ORDER BY calls_today DESC 
                    LIMIT 10
                `);

                // 最近24小时调用趋势
                const [hourlyTrend] = await this.dbPool.execute(`
                    SELECT 
                        HOUR(created_at) as hour,
                        COUNT(*) as calls
                    FROM usage_logs 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
                    GROUP BY HOUR(created_at)
                    ORDER BY hour
                `);

                res.json({
                    summary: {
                        total_enterprises: enterpriseCount[0].total,
                        active_enterprises: enterpriseCount[0].active,
                        today_calls: todayCalls[0].total,
                        today_success_calls: todayCalls[0].success,
                        month_calls: monthCalls[0].total,
                        success_rate: todayCalls[0].total > 0 ? (todayCalls[0].success / todayCalls[0].total * 100).toFixed(2) : 0
                    },
                    top_enterprises: topEnterprises,
                    hourly_trend: hourlyTrend
                });

            } catch (error) {
                console.error('获取仪表板数据错误:', error);
                res.status(500).json({ error: '获取数据失败' });
            }
        });

        // 获取企业列表
        this.router.get('/enterprises', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const [enterprises] = await this.dbPool.execute(`
                    SELECT 
                        ak.*,
                        COALESCE(qu.daily_usage, 0) as used_today,
                        COALESCE(qu.monthly_usage, 0) as used_month,
                        (ak.quota_daily - COALESCE(qu.daily_usage, 0)) as remaining_daily,
                        (ak.quota_monthly - COALESCE(qu.monthly_usage, 0)) as remaining_monthly
                    FROM api_keys ak
                    LEFT JOIN quota_usage qu ON ak.key_id = qu.key_id AND qu.date = CURDATE()
                    ORDER BY ak.created_at DESC
                `);

                res.json({ enterprises });

            } catch (error) {
                console.error('获取企业列表错误:', error);
                res.status(500).json({ error: '获取企业列表失败' });
            }
        });

        // 创建新企业
        this.router.post('/enterprises', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const {
                    enterprise_name,
                    quota_daily = 1000,
                    quota_monthly = 30000,
                    price_per_call = 0.01,
                    contact_email,
                    contact_phone,
                    contact_person,
                    company_address,
                    notes
                } = req.body;

                if (!enterprise_name) {
                    return res.status(400).json({ error: '企业名称不能为空' });
                }

                // 生成唯一的API密钥
                const keyId = `enterprise_${Date.now()}_${crypto.randomBytes(8).toString('hex')}`;
                const keyHash = crypto.createHash('sha256').update(keyId).digest('hex');

                await this.dbPool.execute(`
                    INSERT INTO api_keys (
                        key_id, enterprise_name, key_hash, quota_daily, quota_monthly,
                        price_per_call, contact_email, contact_phone, contact_person,
                        company_address, notes
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                `, [
                    keyId, enterprise_name, keyHash, quota_daily, quota_monthly,
                    price_per_call, contact_email, contact_phone, contact_person,
                    company_address, notes
                ]);

                res.json({
                    success: true,
                    message: '企业创建成功',
                    key_id: keyId
                });

            } catch (error) {
                console.error('创建企业错误:', error);
                if (error.code === 'ER_DUP_ENTRY') {
                    res.status(400).json({ error: '企业名称已存在' });
                } else {
                    res.status(500).json({ error: '创建企业失败' });
                }
            }
        });

        // 更新企业信息
        this.router.put('/enterprises/:id', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { id } = req.params;
                const {
                    enterprise_name,
                    quota_daily,
                    quota_monthly,
                    price_per_call,
                    status,
                    contact_email,
                    contact_phone,
                    contact_person,
                    company_address,
                    notes
                } = req.body;

                await this.dbPool.execute(`
                    UPDATE api_keys SET
                        enterprise_name = ?,
                        quota_daily = ?,
                        quota_monthly = ?,
                        price_per_call = ?,
                        status = ?,
                        contact_email = ?,
                        contact_phone = ?,
                        contact_person = ?,
                        company_address = ?,
                        notes = ?
                    WHERE id = ?
                `, [
                    enterprise_name, quota_daily, quota_monthly, price_per_call,
                    status, contact_email, contact_phone, contact_person,
                    company_address, notes, id
                ]);

                res.json({ success: true, message: '企业信息更新成功' });

            } catch (error) {
                console.error('更新企业错误:', error);
                res.status(500).json({ error: '更新企业信息失败' });
            }
        });

        // 删除企业
        this.router.delete('/enterprises/:id', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { id } = req.params;

                // 检查是否有使用记录
                const [usageCount] = await this.dbPool.execute(
                    'SELECT COUNT(*) as count FROM usage_logs WHERE key_id = (SELECT key_id FROM api_keys WHERE id = ?)',
                    [id]
                );

                if (usageCount[0].count > 0) {
                    // 如果有使用记录，只是标记为删除
                    await this.dbPool.execute(
                        'UPDATE api_keys SET status = "expired" WHERE id = ?',
                        [id]
                    );
                    res.json({ success: true, message: '企业已标记为过期' });
                } else {
                    // 如果没有使用记录，可以直接删除
                    await this.dbPool.execute('DELETE FROM api_keys WHERE id = ?', [id]);
                    res.json({ success: true, message: '企业删除成功' });
                }

            } catch (error) {
                console.error('删除企业错误:', error);
                res.status(500).json({ error: '删除企业失败' });
            }
        });

        // 获取使用统计
        this.router.get('/usage-stats', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { start_date, end_date, enterprise } = req.query;
                
                let whereClause = 'WHERE 1=1';
                let params = [];

                if (start_date) {
                    whereClause += ' AND DATE(created_at) >= ?';
                    params.push(start_date);
                }

                if (end_date) {
                    whereClause += ' AND DATE(created_at) <= ?';
                    params.push(end_date);
                }

                if (enterprise) {
                    whereClause += ' AND enterprise_name = ?';
                    params.push(enterprise);
                }

                const [dailyStats] = await this.dbPool.execute(`
                    SELECT 
                        DATE(created_at) as date,
                        enterprise_name,
                        COUNT(*) as total_calls,
                        COUNT(CASE WHEN error_code IS NULL THEN 1 END) as success_calls,
                        AVG(CASE WHEN ai_probability IS NOT NULL THEN ai_probability END) as avg_ai_probability,
                        AVG(CASE WHEN processing_time_ms IS NOT NULL THEN processing_time_ms END) as avg_processing_time
                    FROM usage_logs 
                    ${whereClause}
                    GROUP BY DATE(created_at), enterprise_name
                    ORDER BY date DESC, total_calls DESC
                `, params);

                res.json({ daily_stats: dailyStats });

            } catch (error) {
                console.error('获取使用统计错误:', error);
                res.status(500).json({ error: '获取使用统计失败' });
            }
        });

        // 重置企业配额
        this.router.post('/enterprises/:id/reset-quota', this.authenticateToken.bind(this), async (req, res) => {
            try {
                const { id } = req.params;
                const { type } = req.body; // 'daily' 或 'monthly'

                const [enterprise] = await this.dbPool.execute(
                    'SELECT key_id FROM api_keys WHERE id = ?',
                    [id]
                );

                if (enterprise.length === 0) {
                    return res.status(404).json({ error: '企业不存在' });
                }

                const keyId = enterprise[0].key_id;

                if (type === 'daily') {
                    await this.dbPool.execute(
                        'UPDATE quota_usage SET daily_usage = 0 WHERE key_id = ? AND date = CURDATE()',
                        [keyId]
                    );
                } else if (type === 'monthly') {
                    await this.dbPool.execute(
                        'UPDATE quota_usage SET monthly_usage = 0 WHERE key_id = ? AND YEAR(date) = YEAR(CURDATE()) AND MONTH(date) = MONTH(CURDATE())',
                        [keyId]
                    );
                }

                res.json({ success: true, message: `${type === 'daily' ? '日' : '月'}配额重置成功` });

            } catch (error) {
                console.error('重置配额错误:', error);
                res.status(500).json({ error: '重置配额失败' });
            }
        });
    }

    getRouter() {
        return this.router;
    }
}

module.exports = AdminRoutes;
