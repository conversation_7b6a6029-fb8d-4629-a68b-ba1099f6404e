/**
 * 学术专家服务管理页面
 */

// API基础URL
const API_BASE_URL = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1'
    ? window.location.protocol + '//' + window.location.hostname + ':3001/api'
    : '/api';

// 全局变量
let currentPage = 1;
let totalPages = 1;
let selectedFiles = []; // 改为数组支持多文件
const maxFiles = 2; // 最多2个文件

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initializePage();
});

/**
 * 初始化页面
 */
function initializePage() {
    // 首先检查登录状态
    const token = localStorage.getItem('adminToken');
    if (!token) {
        console.warn('未找到管理员token，重定向到登录页面');
        window.location.href = 'login.html';
        return;
    }

    loadStatistics();
    loadProcessingOrders();
    initializeFileUpload();
}

/**
 * 加载统计数据
 */
async function loadStatistics() {
    try {
        // 这里可以调用统计API，暂时使用模拟数据
        document.getElementById('totalProcessing').textContent = '0';
        document.getElementById('totalCompleted').textContent = '0';
        document.getElementById('totalRevenue').textContent = '0';
        document.getElementById('avgProcessTime').textContent = '0天';
    } catch (error) {
        console.error('加载统计数据失败:', error);
    }
}

/**
 * 加载处理中的订单
 */
async function loadProcessingOrders(page = 1) {
    try {
        showLoading();
        
        const token = localStorage.getItem('adminToken');
        if (!token) {
            console.warn('未找到管理员token，重定向到登录页面');
            window.location.href = 'login.html';
            return;
        }

        const response = await fetch(`${API_BASE_URL}/payments/orders/processing/list?page=${page}&pageSize=20`, {
            headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
            }
        });

        if (!response.ok) {
            throw new Error('获取订单列表失败');
        }

        const data = await response.json();
        
        if (data.success) {
            displayOrders(data.orders);
            updatePagination(data.pagination);
            updateStatistics(data.orders);
        } else {
            throw new Error(data.message || '获取订单列表失败');
        }

    } catch (error) {
        console.error('加载订单失败:', error);
        showError('加载订单失败: ' + error.message);
    }
}

/**
 * 显示加载状态
 */
function showLoading() {
    document.getElementById('loadingState').style.display = 'block';
    document.getElementById('emptyState').style.display = 'none';
    document.getElementById('ordersTable').style.display = 'none';
    document.getElementById('pagination').style.display = 'none';
}

/**
 * 显示订单列表
 */
function displayOrders(orders) {
    const loadingState = document.getElementById('loadingState');
    const emptyState = document.getElementById('emptyState');
    const ordersTable = document.getElementById('ordersTable');
    const tableBody = document.getElementById('ordersTableBody');

    loadingState.style.display = 'none';

    if (!orders || orders.length === 0) {
        emptyState.style.display = 'block';
        ordersTable.style.display = 'none';
        return;
    }

    emptyState.style.display = 'none';
    ordersTable.style.display = 'table';

    // 清空表格内容
    tableBody.innerHTML = '';

    // 填充订单数据
    orders.forEach(order => {
        const row = document.createElement('tr');
        // 获取显示用的文档名称
        const displayFileName = order.displayFileName || '未命名文档';

        row.innerHTML = `
            <td>
                <div style="font-weight: 500;">#${order.id}</div>
                <div style="font-size: 12px; color: #666;">${order.serviceType === 'expert_optimization' ? '学术专家' : 'AI优化'}</div>
            </td>
            <td>
                <div style="font-weight: 500; color: #8A2BE2;">📄 ${displayFileName}</div>
                <div style="font-size: 12px; color: #666; margin-top: 4px;">用户: ${order.user?.username || '未知用户'}</div>
                <div style="font-size: 12px; color: #666;">${order.user?.email || ''}</div>
            </td>
            <td>
                <div>${formatDate(order.createdAt)}</div>
                <div style="font-size: 12px; color: #666;">${getTimeAgo(order.createdAt)}</div>
            </td>
            <td>
                <span class="status-badge status-processing">🔄 处理中</span>
            </td>
            <td>
                <div class="action-buttons">
                    <button class="btn btn-primary" onclick="openUploadModal(${order.id}, '${order.user?.username || ''}', ${order.amount})">
                        📤 上传结果
                    </button>
                </div>
            </td>
        `;
        tableBody.appendChild(row);
    });
}

/**
 * 更新分页
 */
function updatePagination(pagination) {
    const paginationDiv = document.getElementById('pagination');
    
    if (pagination.totalPages <= 1) {
        paginationDiv.style.display = 'none';
        return;
    }

    paginationDiv.style.display = 'flex';
    paginationDiv.innerHTML = '';

    currentPage = pagination.page;
    totalPages = pagination.totalPages;

    // 上一页按钮
    const prevBtn = document.createElement('button');
    prevBtn.textContent = '‹ 上一页';
    prevBtn.disabled = currentPage === 1;
    prevBtn.onclick = () => loadProcessingOrders(currentPage - 1);
    paginationDiv.appendChild(prevBtn);

    // 页码按钮
    for (let i = 1; i <= totalPages; i++) {
        if (i === 1 || i === totalPages || (i >= currentPage - 2 && i <= currentPage + 2)) {
            const pageBtn = document.createElement('button');
            pageBtn.textContent = i;
            pageBtn.className = i === currentPage ? 'active' : '';
            pageBtn.onclick = () => loadProcessingOrders(i);
            paginationDiv.appendChild(pageBtn);
        } else if (i === currentPage - 3 || i === currentPage + 3) {
            const ellipsis = document.createElement('span');
            ellipsis.textContent = '...';
            ellipsis.style.padding = '8px';
            paginationDiv.appendChild(ellipsis);
        }
    }

    // 下一页按钮
    const nextBtn = document.createElement('button');
    nextBtn.textContent = '下一页 ›';
    nextBtn.disabled = currentPage === totalPages;
    nextBtn.onclick = () => loadProcessingOrders(currentPage + 1);
    paginationDiv.appendChild(nextBtn);
}

/**
 * 更新统计数据
 */
function updateStatistics(orders) {
    document.getElementById('totalProcessing').textContent = orders.length;
    
    // 计算总收入
    const totalRevenue = orders.reduce((sum, order) => sum + order.amount, 0);
    document.getElementById('totalRevenue').textContent = totalRevenue.toFixed(2);
}

/**
 * 打开上传弹窗
 */
function openUploadModal(orderId, username, amount) {
    document.getElementById('currentOrderId').value = orderId;
    document.getElementById('orderInfo').innerHTML = `
        <strong>订单 #${orderId}</strong><br>
        用户: ${username}<br>
        金额: ¥${amount.toFixed(2)}
    `;

    // 先显示弹窗
    document.getElementById('uploadModal').style.display = 'block';

    // 然后重置文件选择（确保DOM元素可见）
    setTimeout(() => {
        resetFileUpload();
    }, 10);
}

/**
 * 关闭上传弹窗
 */
function closeUploadModal() {
    document.getElementById('uploadModal').style.display = 'none';
    resetFileUpload();
}

/**
 * 初始化文件上传
 */
function initializeFileUpload() {
    const fileUploadArea = document.getElementById('fileUploadArea');
    const fileInput = document.getElementById('fileInput');
    const uploadForm = document.getElementById('uploadForm');

    // 点击上传区域
    fileUploadArea.addEventListener('click', () => {
        fileInput.click();
    });

    // 文件选择
    fileInput.addEventListener('change', handleFileSelect);

    // 拖拽上传
    fileUploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        fileUploadArea.classList.add('dragover');
    });

    fileUploadArea.addEventListener('dragleave', () => {
        fileUploadArea.classList.remove('dragover');
    });

    fileUploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        fileUploadArea.classList.remove('dragover');
        
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            handleFileSelect({ target: { files } });
        }
    });

    // 表单提交
    uploadForm.addEventListener('submit', handleUploadSubmit);
}

/**
 * 处理文件选择
 */
function handleFileSelect(event) {
    const files = Array.from(event.target.files);
    if (files.length === 0) return;

    // 验证文件数量
    if (selectedFiles.length + files.length > maxFiles) {
        showError(`最多只能选择${maxFiles}个文件，当前已选择${selectedFiles.length}个`);
        return;
    }

    // 验证每个文件
    for (const file of files) {
        // 验证文件类型
        const allowedTypes = ['.docx', '.pdf', '.txt'];
        const fileExtension = '.' + file.name.split('.').pop().toLowerCase();

        if (!allowedTypes.includes(fileExtension)) {
            showError(`文件 "${file.name}" 格式不支持，请选择 PDF, DOCX 或 TXT 文件`);
            return;
        }

        // 验证文件大小 (最大50MB)
        if (file.size > 50 * 1024 * 1024) {
            showError(`文件 "${file.name}" 大小不能超过50MB`);
            return;
        }

        // 检查是否重复
        if (selectedFiles.some(f => f.name === file.name)) {
            showError(`文件 "${file.name}" 已经选择过了`);
            return;
        }
    }

    // 添加文件到选择列表
    selectedFiles.push(...files);

    // 更新显示
    updateFilesDisplay();
    updateUploadAreaText();
}

/**
 * 更新文件列表显示
 */
function updateFilesDisplay() {
    const filesList = document.getElementById('filesList');

    // 安全检查：确保元素存在
    if (!filesList) {
        console.warn('filesList元素不存在，跳过更新');
        return;
    }

    if (selectedFiles.length === 0) {
        filesList.innerHTML = '';
        return;
    }

    filesList.innerHTML = selectedFiles.map((file, index) => `
        <div class="file-item">
            <div class="file-info-item">
                <div class="file-icon">${getFileIcon(file.name)}</div>
                <div class="file-details">
                    <div class="file-name">${file.name}</div>
                    <div class="file-size">${formatFileSize(file.size)}</div>
                </div>
            </div>
            <button class="file-remove" onclick="removeFile(${index})">删除</button>
        </div>
    `).join('');
}

/**
 * 获取文件图标
 */
function getFileIcon(fileName) {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
        case 'pdf': return '📄';
        case 'docx': return '📝';
        case 'txt': return '📃';
        default: return '📄';
    }
}

/**
 * 移除指定文件
 */
function removeFile(index) {
    selectedFiles.splice(index, 1);
    updateFilesDisplay();
    updateUploadAreaText();

    // 清空input以允许重新选择相同文件
    document.getElementById('fileInput').value = '';
}

/**
 * 更新上传区域文本
 */
function updateUploadAreaText() {
    const uploadText = document.querySelector('.upload-text');

    // 安全检查：确保元素存在
    if (!uploadText) {
        console.warn('upload-text元素不存在，跳过更新');
        return;
    }

    if (selectedFiles.length === 0) {
        uploadText.textContent = '点击选择文件或拖拽文件到此处';
    } else if (selectedFiles.length < maxFiles) {
        uploadText.textContent = `已选择${selectedFiles.length}个文件，还可以选择${maxFiles - selectedFiles.length}个`;
    } else {
        uploadText.textContent = `已选择${selectedFiles.length}个文件（已达上限）`;
    }
}

/**
 * 重置文件上传
 */
function resetFileUpload() {
    selectedFiles = [];

    // 安全检查：确保元素存在
    const fileInput = document.getElementById('fileInput');
    if (fileInput) {
        fileInput.value = '';
    } else {
        console.warn('fileInput元素不存在');
    }

    updateFilesDisplay();
    updateUploadAreaText();
}

/**
 * 处理上传提交
 */
async function handleUploadSubmit(event) {
    event.preventDefault();
    
    if (selectedFiles.length === 0) {
        showError('请先选择文件');
        return;
    }

    const orderId = document.getElementById('currentOrderId').value;
    if (!orderId) {
        showError('订单ID无效');
        return;
    }

    try {
        // 显示上传进度
        const submitBtn = event.target.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        submitBtn.innerHTML = '📤 上传中...';
        submitBtn.disabled = true;

        // 创建FormData
        const formData = new FormData();

        // 添加所有选择的文件
        selectedFiles.forEach((file, index) => {
            formData.append('files', file);
        });

        formData.append('orderId', orderId);

        console.log(`开始上传${selectedFiles.length}个文件，订单ID: ${orderId}`);

        // 上传文件
        const token = localStorage.getItem('adminToken');
        const response = await fetch(`${API_BASE_URL}/payments/orders/${orderId}/result`, {
            method: 'PUT',
            headers: {
                'Authorization': `Bearer ${token}`
            },
            body: formData
        });

        if (!response.ok) {
            throw new Error('上传失败');
        }

        const result = await response.json();
        
        if (result.success) {
            showSuccess(`${selectedFiles.length}个文件上传成功！订单已完成`);
            closeUploadModal();
            loadProcessingOrders(currentPage); // 刷新订单列表
        } else {
            throw new Error(result.message || '上传失败');
        }

    } catch (error) {
        console.error('上传失败:', error);
        showError('上传失败: ' + error.message);
    } finally {
        // 恢复按钮状态
        const submitBtn = event.target.querySelector('button[type="submit"]');
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }
}

/**
 * 工具函数
 */

// 格式化日期
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('zh-CN') + ' ' + date.toLocaleTimeString('zh-CN', { hour12: false });
}

// 计算时间差
function getTimeAgo(dateString) {
    const now = new Date();
    const date = new Date(dateString);
    const diffMs = now - date;
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    
    if (diffDays > 0) {
        return `${diffDays}天前`;
    } else if (diffHours > 0) {
        return `${diffHours}小时前`;
    } else {
        return '刚刚';
    }
}

// 格式化文件大小
function formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// 显示成功消息
function showSuccess(message) {
    // 这里可以实现通知组件
    alert('✅ ' + message);
}

// 显示错误消息
function showError(message) {
    // 这里可以实现通知组件
    alert('❌ ' + message);
}
