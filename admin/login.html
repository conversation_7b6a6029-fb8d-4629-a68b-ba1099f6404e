<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - WriterPro</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #333;
        }

        .login-container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            width: 100%;
            max-width: 400px;
            text-align: center;
        }

        .logo {
            font-size: 48px;
            margin-bottom: 20px;
        }

        .login-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .login-subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 14px;
        }

        .form-group {
            margin-bottom: 20px;
            text-align: left;
        }

        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #333;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s;
        }

        .form-control:focus {
            outline: none;
            border-color: #8A2BE2;
            box-shadow: 0 0 0 3px rgba(138, 43, 226, 0.1);
        }

        .btn {
            width: 100%;
            padding: 15px;
            background: linear-gradient(135deg, #8A2BE2, #9932CC);
            color: white;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s;
            margin-bottom: 20px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(138, 43, 226, 0.3);
        }

        .btn:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .alert {
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
        }

        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .quick-login {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-top: 20px;
        }

        .quick-login h4 {
            margin-bottom: 15px;
            color: #333;
            font-size: 16px;
        }

        .quick-login-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: all 0.3s;
        }

        .quick-login-btn:hover {
            background: #5a6268;
        }

        .footer-links {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #e0e0e0;
        }

        .footer-links a {
            color: #8A2BE2;
            text-decoration: none;
            font-size: 14px;
            margin: 0 10px;
        }

        .footer-links a:hover {
            text-decoration: underline;
        }

        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 2px solid #ffffff;
            border-radius: 50%;
            border-top-color: transparent;
            animation: spin 1s ease-in-out infinite;
            margin-right: 10px;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        @media (max-width: 480px) {
            .login-container {
                margin: 20px;
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="logo">🎓</div>
        <h1 class="login-title">管理员登录</h1>
        <p class="login-subtitle">WriterPro 学术专家服务管理系统</p>

        <div id="alertContainer"></div>

        <form id="loginForm">
            <div class="form-group">
                <label class="form-label" for="username">用户名</label>
                <input type="text" id="username" class="form-control" placeholder="请输入管理员用户名" required>
            </div>

            <div class="form-group">
                <label class="form-label" for="password">密码</label>
                <input type="password" id="password" class="form-control" placeholder="请输入密码" required>
            </div>

            <button type="submit" class="btn" id="loginBtn">
                <span id="loginBtnText">🔐 登录</span>
            </button>
        </form>

        <div class="quick-login">
            <h4>🚀 快速登录（开发测试）</h4>
            <button class="quick-login-btn" onclick="quickLogin('admin', 'admin123')">
                管理员账户
            </button>
            <button class="quick-login-btn" onclick="quickLogin('expert', 'expert123')">
                专家账户
            </button>
        </div>

        <div class="footer-links">
            <a href="../index.html">返回首页</a>
            <a href="expert-orders.html">直接访问管理后台</a>
        </div>
    </div>

    <script>
        // API基础URL
        const API_BASE_URL = '/api';

        // 页面加载时检查是否已登录
        document.addEventListener('DOMContentLoaded', function() {
            const token = localStorage.getItem('adminToken');
            if (token) {
                showAlert('您已登录，正在跳转到管理后台...', 'success');
                setTimeout(() => {
                    window.location.href = 'expert-orders.html';
                }, 1500);
            }
        });

        // 登录表单提交
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            
            if (!username || !password) {
                showAlert('请输入用户名和密码', 'error');
                return;
            }

            await performLogin(username, password);
        });

        // 执行登录
        async function performLogin(username, password) {
            const loginBtn = document.getElementById('loginBtn');
            const loginBtnText = document.getElementById('loginBtnText');
            
            // 显示加载状态
            loginBtn.disabled = true;
            loginBtnText.innerHTML = '<span class="loading"></span>登录中...';

            try {
                // 这里可以调用真实的登录API
                // 暂时使用模拟登录逻辑
                const result = await mockLogin(username, password);
                
                if (result.success) {
                    // 保存token
                    localStorage.setItem('adminToken', result.token);
                    localStorage.setItem('adminUser', JSON.stringify(result.user));
                    
                    showAlert('登录成功！正在跳转...', 'success');
                    
                    setTimeout(() => {
                        window.location.href = 'expert-orders.html';
                    }, 1500);
                } else {
                    showAlert(result.message || '登录失败', 'error');
                }

            } catch (error) {
                console.error('登录错误:', error);
                showAlert('登录失败：' + error.message, 'error');
            } finally {
                // 恢复按钮状态
                loginBtn.disabled = false;
                loginBtnText.innerHTML = '🔐 登录';
            }
        }

        // 模拟登录API（实际项目中应该调用真实API）
        async function mockLogin(username, password) {
            // 模拟网络延迟
            await new Promise(resolve => setTimeout(resolve, 1000));

            // 预设的管理员账户
            const adminAccounts = {
                'admin': { password: 'admin123', role: 'admin', name: '系统管理员' },
                'expert': { password: 'expert123', role: 'expert', name: '学术专家' },
                'manager': { password: 'manager123', role: 'manager', name: '订单管理员' }
            };

            if (adminAccounts[username] && adminAccounts[username].password === password) {
                return {
                    success: true,
                    token: 'admin_token_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9),
                    user: {
                        username: username,
                        name: adminAccounts[username].name,
                        role: adminAccounts[username].role
                    }
                };
            } else {
                return {
                    success: false,
                    message: '用户名或密码错误'
                };
            }
        }

        // 快速登录
        async function quickLogin(username, password) {
            document.getElementById('username').value = username;
            document.getElementById('password').value = password;
            await performLogin(username, password);
        }

        // 显示提示信息
        function showAlert(message, type = 'error') {
            const alertContainer = document.getElementById('alertContainer');
            const alertClass = type === 'success' ? 'alert-success' : 'alert-error';
            
            alertContainer.innerHTML = `
                <div class="alert ${alertClass}">
                    ${message}
                </div>
            `;

            // 3秒后自动隐藏
            setTimeout(() => {
                alertContainer.innerHTML = '';
            }, 3000);
        }
    </script>
</body>
</html>
