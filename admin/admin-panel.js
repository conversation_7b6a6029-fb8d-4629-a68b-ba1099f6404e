// 全局变量
let authToken = localStorage.getItem('authToken');
let currentUser = null;
const API_BASE = '/admin';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    if (authToken) {
        showMainPage();
        loadDashboard();
    } else {
        showLoginPage();
    }
});

// 显示登录页面
function showLoginPage() {
    document.getElementById('loginPage').style.display = 'flex';
    document.getElementById('mainPage').style.display = 'none';
}

// 显示主页面
function showMainPage() {
    document.getElementById('loginPage').style.display = 'none';
    document.getElementById('mainPage').style.display = 'block';
}

// 登录处理
document.getElementById('loginForm').addEventListener('submit', async function(e) {
    e.preventDefault();
    
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;
    
    try {
        const response = await fetch(`${API_BASE}/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        const data = await response.json();
        
        if (data.success) {
            authToken = data.token;
            currentUser = data.user;
            localStorage.setItem('authToken', authToken);
            document.getElementById('currentUser').textContent = currentUser.username;
            showMainPage();
            loadDashboard();
        } else {
            showError('loginError', data.error || '登录失败');
        }
    } catch (error) {
        showError('loginError', '网络错误，请稍后重试');
    }
});

// 退出登录
function logout() {
    localStorage.removeItem('authToken');
    authToken = null;
    currentUser = null;
    showLoginPage();
}

// 显示错误信息
function showError(elementId, message) {
    const errorElement = document.getElementById(elementId);
    errorElement.textContent = message;
    errorElement.style.display = 'block';
    setTimeout(() => {
        errorElement.style.display = 'none';
    }, 5000);
}

// API请求封装
async function apiRequest(url, options = {}) {
    const defaultOptions = {
        headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${authToken}`
        }
    };
    
    const mergedOptions = {
        ...defaultOptions,
        ...options,
        headers: {
            ...defaultOptions.headers,
            ...options.headers
        }
    };
    
    const response = await fetch(url, mergedOptions);
    
    if (response.status === 401) {
        logout();
        return null;
    }
    
    return response.json();
}

// 页面导航
document.querySelectorAll('[data-page]').forEach(link => {
    link.addEventListener('click', function(e) {
        e.preventDefault();
        const page = this.dataset.page;
        showPage(page);
        
        // 更新导航状态
        document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
        this.classList.add('active');
    });
});

function showPage(page) {
    // 隐藏所有页面
    document.querySelectorAll('.page-content').forEach(p => p.style.display = 'none');
    
    // 显示目标页面
    const targetPage = document.getElementById(page + 'Page');
    if (targetPage) {
        targetPage.style.display = 'block';
        
        // 加载页面数据
        switch(page) {
            case 'dashboard':
                loadDashboard();
                break;
            case 'enterprises':
                loadEnterprises();
                break;
            case 'usage':
                loadUsageStats();
                break;
        }
    }
}

// 加载仪表板数据
async function loadDashboard() {
    try {
        const data = await apiRequest(`${API_BASE}/dashboard`);
        if (data) {
            updateDashboardStats(data.summary);
            updateHourlyChart(data.hourly_trend);
            updateTopEnterprises(data.top_enterprises);
        }
    } catch (error) {
        console.error('加载仪表板数据失败:', error);
    }
}

function updateDashboardStats(summary) {
    document.getElementById('totalEnterprises').textContent = summary.total_enterprises;
    document.getElementById('todayCalls').textContent = summary.today_calls;
    document.getElementById('successRate').textContent = summary.success_rate + '%';
    document.getElementById('monthCalls').textContent = summary.month_calls;
}

let hourlyChart = null;

function updateHourlyChart(hourlyData) {
    const ctx = document.getElementById('hourlyTrendChart').getContext('2d');
    
    // 销毁现有图表
    if (hourlyChart) {
        hourlyChart.destroy();
    }
    
    // 准备24小时数据
    const hours = Array.from({length: 24}, (_, i) => i);
    const calls = hours.map(hour => {
        const found = hourlyData.find(d => d.hour === hour);
        return found ? found.calls : 0;
    });
    
    hourlyChart = new Chart(ctx, {
        type: 'line',
        data: {
            labels: hours.map(h => h + ':00'),
            datasets: [{
                label: '调用次数',
                data: calls,
                borderColor: 'rgb(75, 192, 192)',
                backgroundColor: 'rgba(75, 192, 192, 0.2)',
                tension: 0.1,
                fill: true
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            scales: {
                y: {
                    beginAtZero: true,
                    ticks: {
                        stepSize: 1
                    }
                }
            },
            plugins: {
                legend: {
                    display: false
                }
            }
        }
    });
}

function updateTopEnterprises(topEnterprises) {
    const container = document.getElementById('topEnterprises');
    container.innerHTML = '';
    
    if (topEnterprises.length === 0) {
        container.innerHTML = '<p class="text-muted">今日暂无调用记录</p>';
        return;
    }
    
    topEnterprises.forEach((enterprise, index) => {
        const item = document.createElement('div');
        item.className = 'd-flex justify-content-between align-items-center mb-2 p-2 bg-light rounded';
        item.innerHTML = `
            <div>
                <strong>${enterprise.enterprise_name}</strong>
                <small class="text-muted d-block">AI概率: ${enterprise.avg_ai_prob ? (enterprise.avg_ai_prob * 100).toFixed(1) + '%' : 'N/A'}</small>
            </div>
            <span class="badge bg-primary">${enterprise.calls_today}</span>
        `;
        container.appendChild(item);
    });
}

// 刷新仪表板
function refreshDashboard() {
    loadDashboard();
}

// 加载企业列表
async function loadEnterprises() {
    try {
        const data = await apiRequest(`${API_BASE}/enterprises`);
        if (data) {
            updateEnterprisesTable(data.enterprises);
        }
    } catch (error) {
        console.error('加载企业列表失败:', error);
    }
}

function updateEnterprisesTable(enterprises) {
    const tbody = document.querySelector('#enterprisesTable tbody');
    tbody.innerHTML = '';
    
    if (enterprises.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无企业数据</td></tr>';
        return;
    }
    
    enterprises.forEach(enterprise => {
        const row = document.createElement('tr');
        
        // 状态颜色映射
        const statusMap = {
            'active': { class: 'success', text: '活跃' },
            'suspended': { class: 'warning', text: '暂停' },
            'expired': { class: 'danger', text: '过期' }
        };
        
        const status = statusMap[enterprise.status] || { class: 'secondary', text: enterprise.status };
        
        row.innerHTML = `
            <td>
                <strong>${enterprise.enterprise_name}</strong>
                <br><small class="text-muted">${enterprise.contact_person || ''}</small>
            </td>
            <td>
                <code class="small">${enterprise.key_id.substring(0, 20)}...</code>
                <button class="btn btn-sm btn-outline-secondary ms-1" onclick="copyToClipboard('${enterprise.key_id}')" title="复制完整密钥">
                    <i class="bi bi-clipboard"></i>
                </button>
            </td>
            <td>${enterprise.quota_daily.toLocaleString()}</td>
            <td>${enterprise.quota_monthly.toLocaleString()}</td>
            <td>
                <span class="badge bg-info">${enterprise.used_today || 0}</span>
                <small class="text-muted d-block">剩余: ${enterprise.remaining_daily || enterprise.quota_daily}</small>
            </td>
            <td>
                <span class="badge bg-info">${enterprise.used_month || 0}</span>
                <small class="text-muted d-block">剩余: ${enterprise.remaining_monthly || enterprise.quota_monthly}</small>
            </td>
            <td>
                <span class="badge bg-${status.class}">${status.text}</span>
            </td>
            <td>
                <div class="btn-group btn-group-sm">
                    <button class="btn btn-outline-primary" onclick="editEnterprise(${enterprise.id})" title="编辑">
                        <i class="bi bi-pencil"></i>
                    </button>
                    <button class="btn btn-outline-success" onclick="resetQuota(${enterprise.id}, 'daily')" title="重置日配额">
                        <i class="bi bi-arrow-clockwise"></i>
                    </button>
                    <button class="btn btn-outline-danger" onclick="deleteEnterprise(${enterprise.id})" title="删除">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// 复制到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        // 显示成功提示
        const toast = document.createElement('div');
        toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed top-0 end-0 m-3';
        toast.style.zIndex = '9999';
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">API密钥已复制到剪贴板</div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
            </div>
        `;
        document.body.appendChild(toast);
        
        const bsToast = new bootstrap.Toast(toast);
        bsToast.show();
        
        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }).catch(() => {
        alert('复制失败，请手动复制：' + text);
    });
}

// 添加企业
async function addEnterprise() {
    const form = document.getElementById('addEnterpriseForm');
    const formData = new FormData(form);
    const data = Object.fromEntries(formData);
    
    // 数据类型转换
    data.quota_daily = parseInt(data.quota_daily) || 1000;
    data.quota_monthly = parseInt(data.quota_monthly) || 30000;
    data.price_per_call = parseFloat(data.price_per_call) || 0.01;
    
    try {
        const result = await apiRequest(`${API_BASE}/enterprises`, {
            method: 'POST',
            body: JSON.stringify(data)
        });
        
        if (result && result.success) {
            // 显示成功消息和API密钥
            const modal = bootstrap.Modal.getInstance(document.getElementById('addEnterpriseModal'));
            modal.hide();
            
            // 显示API密钥
            alert(`企业添加成功！\n\nAPI密钥：${result.key_id}\n\n请妥善保管此密钥，它将用于API调用认证。`);
            
            form.reset();
            loadEnterprises();
        } else {
            alert('添加失败：' + (result?.error || '未知错误'));
        }
    } catch (error) {
        alert('添加失败：网络错误');
        console.error('添加企业失败:', error);
    }
}

// 编辑企业
function editEnterprise(id) {
    alert('编辑功能开发中...\n\n当前可以通过删除后重新添加的方式来修改企业信息。');
}

// 重置配额
async function resetQuota(id, type) {
    const typeText = type === 'daily' ? '日' : '月';
    if (!confirm(`确定要重置这个企业的${typeText}配额吗？`)) return;
    
    try {
        const result = await apiRequest(`${API_BASE}/enterprises/${id}/reset-quota`, {
            method: 'POST',
            body: JSON.stringify({ type })
        });
        
        if (result && result.success) {
            alert(`${typeText}配额重置成功`);
            loadEnterprises();
        } else {
            alert('重置失败：' + (result?.error || '未知错误'));
        }
    } catch (error) {
        alert('重置失败：网络错误');
        console.error('重置配额失败:', error);
    }
}

// 删除企业
async function deleteEnterprise(id) {
    if (!confirm('确定要删除这个企业吗？\n\n注意：如果企业有使用记录，将标记为过期而不是直接删除。')) return;
    
    try {
        const result = await apiRequest(`${API_BASE}/enterprises/${id}`, {
            method: 'DELETE'
        });
        
        if (result && result.success) {
            alert(result.message);
            loadEnterprises();
        } else {
            alert('删除失败：' + (result?.error || '未知错误'));
        }
    } catch (error) {
        alert('删除失败：网络错误');
        console.error('删除企业失败:', error);
    }
}

// 加载使用统计
function loadUsageStats() {
    // 开发中...
    console.log('使用统计功能开发中...');
}
