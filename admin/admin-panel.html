<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC检测企业管理后台</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        .sidebar {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .nav-link {
            color: rgba(255,255,255,0.8) !important;
            transition: all 0.3s;
        }
        .nav-link:hover, .nav-link.active {
            color: white !important;
            background-color: rgba(255,255,255,0.1);
            border-radius: 8px;
        }
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            transition: all 0.3s;
        }
        .card:hover {
            box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
        }
        .stat-card {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
        }
        .stat-card.success {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        .stat-card.warning {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        .stat-card.info {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        .login-container {
            min-height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        .login-card {
            backdrop-filter: blur(10px);
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .table-responsive {
            border-radius: 10px;
            overflow: hidden;
        }
        .btn-gradient {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            color: white;
        }
        .btn-gradient:hover {
            background: linear-gradient(135deg, #764ba2 0%, #667eea 100%);
            color: white;
        }
    </style>
</head>
<body>
    <!-- 登录页面 -->
    <div id="loginPage" class="login-container d-flex align-items-center justify-content-center">
        <div class="container">
            <div class="row justify-content-center">
                <div class="col-md-6 col-lg-4">
                    <div class="card login-card">
                        <div class="card-body p-5">
                            <div class="text-center mb-4">
                                <h2 class="text-white">管理后台</h2>
                                <p class="text-white-50">AIGC检测企业管理系统</p>
                            </div>
                            <form id="loginForm">
                                <div class="mb-3">
                                    <input type="text" class="form-control" id="username" placeholder="用户名" required>
                                </div>
                                <div class="mb-3">
                                    <input type="password" class="form-control" id="password" placeholder="密码" required>
                                </div>
                                <button type="submit" class="btn btn-gradient w-100">登录</button>
                            </form>
                            <div id="loginError" class="alert alert-danger mt-3" style="display: none;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainPage" style="display: none;">
        <div class="container-fluid">
            <div class="row">
                <!-- 侧边栏 -->
                <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
                    <div class="position-sticky pt-3">
                        <div class="text-center mb-4">
                            <h4 class="text-white">AIGC管理</h4>
                        </div>
                        <ul class="nav flex-column">
                            <li class="nav-item">
                                <a class="nav-link active" href="#" data-page="dashboard">
                                    <i class="bi bi-speedometer2"></i> 仪表板
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-page="enterprises">
                                    <i class="bi bi-building"></i> 企业管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-page="usage">
                                    <i class="bi bi-graph-up"></i> 使用统计
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-page="settings">
                                    <i class="bi bi-gear"></i> 系统设置
                                </a>
                            </li>
                        </ul>
                        <hr class="text-white-50">
                        <div class="dropdown">
                            <a href="#" class="d-flex align-items-center text-white text-decoration-none dropdown-toggle" id="dropdownUser" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle me-2"></i>
                                <span id="currentUser">管理员</span>
                            </a>
                            <ul class="dropdown-menu dropdown-menu-dark">
                                <li><a class="dropdown-item" href="#" onclick="logout()">退出登录</a></li>
                            </ul>
                        </div>
                    </div>
                </nav>

                <!-- 主内容区 -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
                    <!-- 仪表板页面 -->
                    <div id="dashboardPage" class="page-content">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">仪表板</h1>
                            <div class="btn-toolbar mb-2 mb-md-0">
                                <button class="btn btn-sm btn-gradient" onclick="refreshDashboard()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>

                        <!-- 统计卡片 -->
                        <div class="row mb-4">
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card stat-card">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-uppercase mb-1">总企业数</div>
                                                <div class="h5 mb-0 font-weight-bold" id="totalEnterprises">0</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-building fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card stat-card success">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-uppercase mb-1">今日调用</div>
                                                <div class="h5 mb-0 font-weight-bold" id="todayCalls">0</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-graph-up fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card stat-card warning">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-uppercase mb-1">成功率</div>
                                                <div class="h5 mb-0 font-weight-bold" id="successRate">0%</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-check-circle fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-xl-3 col-md-6 mb-4">
                                <div class="card stat-card info">
                                    <div class="card-body">
                                        <div class="row no-gutters align-items-center">
                                            <div class="col mr-2">
                                                <div class="text-xs font-weight-bold text-uppercase mb-1">本月调用</div>
                                                <div class="h5 mb-0 font-weight-bold" id="monthCalls">0</div>
                                            </div>
                                            <div class="col-auto">
                                                <i class="bi bi-calendar-month fa-2x"></i>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 图表区域 -->
                        <div class="row">
                            <div class="col-lg-8">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">24小时调用趋势</h5>
                                    </div>
                                    <div class="card-body">
                                        <canvas id="hourlyTrendChart" width="400" height="200"></canvas>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4">
                                <div class="card">
                                    <div class="card-header">
                                        <h5 class="card-title">企业使用排行</h5>
                                    </div>
                                    <div class="card-body">
                                        <div id="topEnterprises"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 企业管理页面 -->
                    <div id="enterprisesPage" class="page-content" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">企业管理</h1>
                            <div class="btn-toolbar mb-2 mb-md-0">
                                <button class="btn btn-sm btn-gradient" data-bs-toggle="modal" data-bs-target="#addEnterpriseModal">
                                    <i class="bi bi-plus"></i> 添加企业
                                </button>
                            </div>
                        </div>

                        <div class="card">
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-hover" id="enterprisesTable">
                                        <thead>
                                            <tr>
                                                <th>企业名称</th>
                                                <th>API密钥</th>
                                                <th>日配额</th>
                                                <th>月配额</th>
                                                <th>今日使用</th>
                                                <th>本月使用</th>
                                                <th>状态</th>
                                                <th>操作</th>
                                            </tr>
                                        </thead>
                                        <tbody></tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 使用统计页面 -->
                    <div id="usagePage" class="page-content" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">使用统计</h1>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <p>使用统计功能开发中...</p>
                            </div>
                        </div>
                    </div>

                    <!-- 系统设置页面 -->
                    <div id="settingsPage" class="page-content" style="display: none;">
                        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                            <h1 class="h2">系统设置</h1>
                        </div>
                        <div class="card">
                            <div class="card-body">
                                <p>系统设置功能开发中...</p>
                            </div>
                        </div>
                    </div>
                </main>
            </div>
        </div>
    </div>

    <!-- 添加企业模态框 -->
    <div class="modal fade" id="addEnterpriseModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">添加企业</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <form id="addEnterpriseForm">
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">企业名称 *</label>
                                    <input type="text" class="form-control" name="enterprise_name" required>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系人</label>
                                    <input type="text" class="form-control" name="contact_person">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系邮箱</label>
                                    <input type="email" class="form-control" name="contact_email">
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="mb-3">
                                    <label class="form-label">联系电话</label>
                                    <input type="text" class="form-control" name="contact_phone">
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">日配额</label>
                                    <input type="number" class="form-control" name="quota_daily" value="1000">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">月配额</label>
                                    <input type="number" class="form-control" name="quota_monthly" value="30000">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="mb-3">
                                    <label class="form-label">单价(元)</label>
                                    <input type="number" class="form-control" name="price_per_call" value="0.01" step="0.001">
                                </div>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">公司地址</label>
                            <textarea class="form-control" name="company_address" rows="2"></textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">备注</label>
                            <textarea class="form-control" name="notes" rows="2"></textarea>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-gradient" onclick="addEnterprise()">添加</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // 全局变量
        let authToken = localStorage.getItem('authToken');
        let currentUser = null;
        const API_BASE = '/admin';

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            if (authToken) {
                showMainPage();
                loadDashboard();
            } else {
                showLoginPage();
            }
        });

        // 显示登录页面
        function showLoginPage() {
            document.getElementById('loginPage').style.display = 'flex';
            document.getElementById('mainPage').style.display = 'none';
        }

        // 显示主页面
        function showMainPage() {
            document.getElementById('loginPage').style.display = 'none';
            document.getElementById('mainPage').style.display = 'block';
        }

        // 登录处理
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;

            try {
                const response = await fetch(`${API_BASE}/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();

                if (data.success) {
                    authToken = data.token;
                    currentUser = data.user;
                    localStorage.setItem('authToken', authToken);
                    document.getElementById('currentUser').textContent = currentUser.username;
                    showMainPage();
                    loadDashboard();
                } else {
                    showError('loginError', data.error || '登录失败');
                }
            } catch (error) {
                showError('loginError', '网络错误，请稍后重试');
            }
        });

        // 退出登录
        function logout() {
            localStorage.removeItem('authToken');
            authToken = null;
            currentUser = null;
            showLoginPage();
        }

        // 显示错误信息
        function showError(elementId, message) {
            const errorElement = document.getElementById(elementId);
            errorElement.textContent = message;
            errorElement.style.display = 'block';
            setTimeout(() => {
                errorElement.style.display = 'none';
            }, 5000);
        }

        // API请求封装
        async function apiRequest(url, options = {}) {
            const defaultOptions = {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${authToken}`
                }
            };

            const mergedOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            const response = await fetch(url, mergedOptions);

            if (response.status === 401) {
                logout();
                return null;
            }

            return response.json();
        }

        // 页面导航
        document.querySelectorAll('[data-page]').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const page = this.dataset.page;
                showPage(page);

                // 更新导航状态
                document.querySelectorAll('.nav-link').forEach(nav => nav.classList.remove('active'));
                this.classList.add('active');
            });
        });

        function showPage(page) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(p => p.style.display = 'none');

            // 显示目标页面
            const targetPage = document.getElementById(page + 'Page');
            if (targetPage) {
                targetPage.style.display = 'block';

                // 加载页面数据
                switch(page) {
                    case 'dashboard':
                        loadDashboard();
                        break;
                    case 'enterprises':
                        loadEnterprises();
                        break;
                    case 'usage':
                        loadUsageStats();
                        break;
                }
            }
        }

        // 加载仪表板数据
        async function loadDashboard() {
            try {
                const data = await apiRequest(`${API_BASE}/dashboard`);
                if (data) {
                    updateDashboardStats(data.summary);
                    updateHourlyChart(data.hourly_trend);
                    updateTopEnterprises(data.top_enterprises);
                }
            } catch (error) {
                console.error('加载仪表板数据失败:', error);
            }
        }

        function updateDashboardStats(summary) {
            document.getElementById('totalEnterprises').textContent = summary.total_enterprises;
            document.getElementById('todayCalls').textContent = summary.today_calls;
            document.getElementById('successRate').textContent = summary.success_rate + '%';
            document.getElementById('monthCalls').textContent = summary.month_calls;
        }

        function updateHourlyChart(hourlyData) {
            const ctx = document.getElementById('hourlyTrendChart').getContext('2d');

            // 准备24小时数据
            const hours = Array.from({length: 24}, (_, i) => i);
            const calls = hours.map(hour => {
                const found = hourlyData.find(d => d.hour === hour);
                return found ? found.calls : 0;
            });

            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: hours.map(h => h + ':00'),
                    datasets: [{
                        label: '调用次数',
                        data: calls,
                        borderColor: 'rgb(75, 192, 192)',
                        backgroundColor: 'rgba(75, 192, 192, 0.2)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    }
                }
            });
        }

        function updateTopEnterprises(topEnterprises) {
            const container = document.getElementById('topEnterprises');
            container.innerHTML = '';

            topEnterprises.forEach((enterprise, index) => {
                const item = document.createElement('div');
                item.className = 'd-flex justify-content-between align-items-center mb-2';
                item.innerHTML = `
                    <div>
                        <strong>${enterprise.enterprise_name}</strong>
                        <small class="text-muted d-block">AI概率: ${(enterprise.avg_ai_prob * 100).toFixed(1)}%</small>
                    </div>
                    <span class="badge bg-primary">${enterprise.calls_today}</span>
                `;
                container.appendChild(item);
            });
        }

        // 刷新仪表板
        function refreshDashboard() {
            loadDashboard();
        }

        // 加载企业列表
        async function loadEnterprises() {
            try {
                const data = await apiRequest(`${API_BASE}/enterprises`);
                if (data) {
                    updateEnterprisesTable(data.enterprises);
                }
            } catch (error) {
                console.error('加载企业列表失败:', error);
            }
        }

        function updateEnterprisesTable(enterprises) {
            const tbody = document.querySelector('#enterprisesTable tbody');
            tbody.innerHTML = '';

            enterprises.forEach(enterprise => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${enterprise.enterprise_name}</td>
                    <td><code>${enterprise.key_id.substring(0, 20)}...</code></td>
                    <td>${enterprise.quota_daily}</td>
                    <td>${enterprise.quota_monthly}</td>
                    <td>${enterprise.used_today || 0}</td>
                    <td>${enterprise.used_month || 0}</td>
                    <td>
                        <span class="badge bg-${enterprise.status === 'active' ? 'success' : 'danger'}">
                            ${enterprise.status === 'active' ? '活跃' : '暂停'}
                        </span>
                    </td>
                    <td>
                        <button class="btn btn-sm btn-outline-primary" onclick="editEnterprise(${enterprise.id})">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-danger" onclick="deleteEnterprise(${enterprise.id})">
                            <i class="bi bi-trash"></i>
                        </button>
                    </td>
                `;
                tbody.appendChild(row);
            });
        }

        // 添加企业
        async function addEnterprise() {
            const form = document.getElementById('addEnterpriseForm');
            const formData = new FormData(form);
            const data = Object.fromEntries(formData);

            try {
                const result = await apiRequest(`${API_BASE}/enterprises`, {
                    method: 'POST',
                    body: JSON.stringify(data)
                });

                if (result && result.success) {
                    alert('企业添加成功！API密钥：' + result.key_id);
                    bootstrap.Modal.getInstance(document.getElementById('addEnterpriseModal')).hide();
                    form.reset();
                    loadEnterprises();
                } else {
                    alert('添加失败：' + (result?.error || '未知错误'));
                }
            } catch (error) {
                alert('添加失败：网络错误');
            }
        }

        // 编辑企业
        function editEnterprise(id) {
            alert('编辑功能开发中...');
        }

        // 删除企业
        async function deleteEnterprise(id) {
            if (!confirm('确定要删除这个企业吗？')) return;

            try {
                const result = await apiRequest(`${API_BASE}/enterprises/${id}`, {
                    method: 'DELETE'
                });

                if (result && result.success) {
                    alert('删除成功');
                    loadEnterprises();
                } else {
                    alert('删除失败：' + (result?.error || '未知错误'));
                }
            } catch (error) {
                alert('删除失败：网络错误');
            }
        }

        // 加载使用统计
        function loadUsageStats() {
            // 开发中...
        }
    </script>
</body>
</html>
