// AIGC检测页面JavaScript - 版本 20250804-v5
console.log('🚀 AIGC检测页面加载 - 版本 20250804-v5 (A4分页PDF修复版)');
class AigcDetection {
    constructor() {
        this.apiBaseUrl = 'https://writerpro.cn/api/aigc';
        this.isDetecting = false;
        this.manualLanguageSelection = false; // 是否手动选择了语言
        this.init();
    }

    init() {
        this.initAuthManager();
        this.bindEvents();
        this.loadRecentHistory();
        this.updateCharCount();
        this.updateUIBasedOnAuthState();
        this.initLanguageSelector();
        this.initReportGeneration();
    }

    initAuthManager() {
        // 等待AuthManager准备就绪
        if (window.AuthManager) {
            // 强制重新初始化AuthManager
            window.AuthManager._initialized = false;
            window.AuthManager.init();
            console.log('AIGC页面 - AuthManager重新初始化完成，登录状态:', window.AuthManager.isLoggedIn());
        } else {
            console.warn('AIGC页面 - AuthManager未找到，可能影响登录状态同步');
        }

        // 监听localStorage变化，实时更新UI状态
        window.addEventListener('storage', (e) => {
            if (e.key === 'token') {
                console.log('AIGC页面 - 检测到token变化，更新UI状态');
                // 同步AuthManager的状态
                if (window.AuthManager) {
                    window.AuthManager._token = e.newValue;
                }
                this.updateUIBasedOnAuthState();
            }
        });

        // 定期检查token状态（防止同页面内的token变化）
        setInterval(() => {
            if (window.AuthManager) {
                const isLoggedIn = window.AuthManager.isLoggedIn();
                const userMenu = document.getElementById('userMenu');
                const uiShowsLoggedIn = userMenu && userMenu.style.display !== 'none';

                if (isLoggedIn !== uiShowsLoggedIn) {
                    console.log('AIGC页面 - 检测到token状态与UI不一致，更新UI');
                    this.updateUIBasedOnAuthState();
                }
            }
        }, 1000);
    }

    updateUIBasedOnAuthState() {
        const isLoggedIn = window.AuthManager && window.AuthManager.isLoggedIn();

        // 更新导航栏登录状态
        const loginBtn = document.getElementById('loginBtn');
        const userMenu = document.getElementById('userMenu');

        if (isLoggedIn) {
            if (loginBtn) loginBtn.style.display = 'none';
            if (userMenu) userMenu.style.display = 'block';
            this.updateNavUserInfo();
        } else {
            if (loginBtn) loginBtn.style.display = 'block';
            if (userMenu) userMenu.style.display = 'none';
        }
    }

    async updateNavUserInfo() {
        if (!window.AuthManager || !window.AuthManager.isLoggedIn()) return;

        try {
            const token = window.AuthManager.getToken();
            const response = await fetch('/api/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const responseData = await response.json();
                // 处理 {success: true, user: {...}} 格式的响应
                const userData = responseData.user || responseData;
                const navUsername = document.getElementById('navUsername');
                const userInitial = document.getElementById('userInitial');

                if (navUsername && userData) {
                    navUsername.textContent = userData.username || userData.email || '用户';
                }

                if (userInitial && userData) {
                    const username = userData.username || userData.email || '用户';
                    userInitial.textContent = username.charAt(0).toUpperCase();
                }
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }
    }

    bindEvents() {
        // 文本输入事件
        const textInput = document.getElementById('inputText');
        if (textInput) {
            textInput.addEventListener('input', () => this.updateCharCount());
        }

        // 文件上传事件处理已移除，统一使用script.js中的处理逻辑

        // 按钮事件 - 添加安全检查
        const detectBtn = document.getElementById('detectBtn');
        const clearBtn = document.getElementById('clearBtn');
        const sampleBtn = document.getElementById('sampleBtn');
        const downloadBtn = document.getElementById('downloadBtn');
        const optimizeBtn = document.getElementById('optimizeBtn');

        if (detectBtn) {
            detectBtn.addEventListener('click', () => this.startDetection());
        }
        if (clearBtn) {
            clearBtn.addEventListener('click', () => this.clearContent());
        }
        if (sampleBtn) {
            sampleBtn.addEventListener('click', () => this.loadSampleText());
        }
        if (downloadBtn) {
            downloadBtn.addEventListener('click', () => this.downloadReport());
        }
        if (optimizeBtn) {
            optimizeBtn.addEventListener('click', () => this.optimizeContent());
        }

        // 退出登录按钮
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleLogout();
            });
        }

        // 修改密码按钮
        const changePasswordBtn = document.getElementById('changePasswordBtn');
        if (changePasswordBtn) {
            changePasswordBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleChangePassword();
            });
        }
    }

    handleLogout() {
        if (window.AuthManager) {
            window.AuthManager.clearToken();
            this.updateUIBasedOnAuthState();
            this.showNotification('已退出登录', 'success');
        }
    }

    handleChangePassword() {
        // 使用主页的修改密码功能
        if (window.showPasswordModal) {
            window.showPasswordModal();
        } else {
            // 如果主页的函数不可用，跳转到主页
            window.location.href = 'index.html';
        }
    }

    updateCharCount() {
        // 新的HTML结构中暂时没有字符计数显示
        // 如果需要可以在控制台显示字符数
        const textInput = document.getElementById('inputText');
        if (textInput) {
            const count = textInput.value.length;
            console.log(`当前字符数: ${count}`);
        }
    }

    // 文件处理方法已移除，统一使用script.js中的处理逻辑

    // processFile方法已移除，统一使用script.js中的处理逻辑

    // 所有文件读取方法已移除，统一使用script.js中的处理逻辑

    initLanguageSelector() {
        const inputText = document.getElementById('inputText');
        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        // 监听语言选择器的手动切换
        if (langZh && langEn) {
            langZh.addEventListener('change', () => {
                if (langZh.checked) {
                    this.manualLanguageSelection = true;
                    console.log('手动选择中文模式');
                }
            });

            langEn.addEventListener('change', () => {
                if (langEn.checked) {
                    this.manualLanguageSelection = true;
                    console.log('手动选择英文模式');
                }
            });
        }

        // 监听文本输入，自动检测语言
        if (inputText) {
            inputText.addEventListener('input', () => {
                if (!this.manualLanguageSelection) {
                    this.autoDetectLanguage(inputText.value);
                }
            });
        }
    }

    autoDetectLanguage(text) {
        if (!text || text.length < 10) return;

        // 检测中文字符比例
        const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
        const totalChars = text.replace(/\s/g, '').length;
        const chineseRatio = totalChars > 0 ? chineseChars / totalChars : 0;

        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        if (chineseRatio > 0.3) {
            // 中文字符超过30%，选择中文模式
            if (langZh) langZh.checked = true;
            console.log(`自动检测为中文模式 (中文比例: ${(chineseRatio * 100).toFixed(1)}%)`);
        } else {
            // 否则选择英文模式
            if (langEn) langEn.checked = true;
            console.log(`自动检测为英文模式 (中文比例: ${(chineseRatio * 100).toFixed(1)}%)`);
        }
    }

    getSelectedLanguage() {
        const langZh = document.getElementById('lang-zh');
        const langEn = document.getElementById('lang-en');

        if (langZh && langZh.checked) return 'zh';
        if (langEn && langEn.checked) return 'en';
        return 'zh'; // 默认中文
    }

    async startDetection() {
        const textInput = document.getElementById('inputText');
        const text = textInput ? textInput.value.trim() : '';

        if (!text) {
            this.showNotification('请输入要检测的文本内容', 'warning');
            return;
        }

        if (text.length < 10) {
            this.showNotification('文本长度至少需要10个字符', 'warning');
            return;
        }

        // 检查登录状态 - 使用统一的AuthManager
        if (!window.AuthManager || !window.AuthManager.isLoggedIn()) {
            this.showNotification('请先登录后再使用检测功能', 'warning');
            setTimeout(() => {
                window.location.href = 'login.html';
            }, 2000);
            return;
        }

        const token = window.AuthManager.getToken();

        this.setDetecting(true);

        try {
            const selectedLanguage = this.getSelectedLanguage();
            console.log(`发送检测请求，语言: ${selectedLanguage}`);

            const response = await fetch(`${this.apiBaseUrl}/detect`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify({
                    text: text,
                    language: selectedLanguage
                })
            });

            const data = await response.json();

            if (!response.ok) {
                throw new Error(data.message || '检测失败');
            }

            this.displayResult(data);
            this.saveToHistory(text, data);
            this.loadRecentHistory();

        } catch (error) {
            console.error('检测错误:', error);
            this.showNotification(error.message || '检测服务暂时不可用，请稍后重试', 'error');
        } finally {
            this.setDetecting(false);
        }
    }

    setDetecting(detecting) {
        this.isDetecting = detecting;
        const detectBtn = document.getElementById('detectBtn');
        const detectBtnText = document.getElementById('detectBtnText');

        if (detecting) {
            detectBtn.disabled = true;
            detectBtnText.textContent = '检测中...';
            detectBtn.style.opacity = '0.7';
        } else {
            detectBtn.disabled = false;
            detectBtnText.textContent = '开始检测';
            detectBtn.style.opacity = '1';
        }
    }

    displayResult(data) {
        const resultSection = document.getElementById('resultSection');
        const probabilityText = document.getElementById('probabilityText');
        const detectionConclusion = document.getElementById('detectionConclusion');
        const detectionDetails = document.getElementById('detectionDetails');
        const probabilityRing = document.querySelector('.probability-ring');

        // 显示结果区域
        resultSection.classList.add('show');

        // 显示AI概率
        const aiProbability = Math.round((data.ai_probability || 0) * 100);

        // 动画更新概率显示
        this.animatePercentage(0, aiProbability, (value) => {
            probabilityText.textContent = `${value}%`;
            probabilityRing.style.setProperty('--percentage', `${value * 3.6}deg`);
        });

        // 显示检测结论 - 统一显示AI率整数百分比
        let conclusionColor = '';
        let ringColor = '';

        if (aiProbability < 30) {
            conclusionColor = '#34C759';
            ringColor = '#34C759';
        } else if (aiProbability < 70) {
            conclusionColor = '#FF9500';
            ringColor = '#FF9500';
        } else {
            conclusionColor = '#FF3B30';
            ringColor = '#FF3B30';
        }

        // 统一显示格式：AI率 XX%
        detectionConclusion.textContent = `AI率 ${aiProbability}%`;
        detectionConclusion.style.color = conclusionColor;
        detectionDetails.textContent = ''; // 不显示详细描述

        // 更新圆环颜色
        setTimeout(() => {
            probabilityRing.style.background = `conic-gradient(from 0deg, ${ringColor} 0%, ${ringColor} ${aiProbability * 3.6}deg, rgba(0, 0, 0, 0.1) ${aiProbability * 3.6}deg, rgba(0, 0, 0, 0.1) 100%)`;
        }, 1000);

        this.showNotification('检测完成', 'success');
    }

    animatePercentage(start, end, callback) {
        const duration = 1500;
        const startTime = performance.now();

        const animate = (currentTime) => {
            const elapsed = currentTime - startTime;
            const progress = Math.min(elapsed / duration, 1);

            // 使用缓动函数
            const easeOutCubic = 1 - Math.pow(1 - progress, 3);
            const currentValue = Math.round(start + (end - start) * easeOutCubic);

            callback(currentValue);

            if (progress < 1) {
                requestAnimationFrame(animate);
            }
        };

        requestAnimationFrame(animate);
    }

    clearContent() {
        const inputText = document.getElementById('inputText');
        const resultSection = document.getElementById('resultSection');

        if (inputText) {
            inputText.value = '';
        }
        if (resultSection) {
            resultSection.classList.remove('show');
        }
        this.updateCharCount();
    }

    loadSampleText() {
        const sampleText = `人工智能技术的快速发展正在改变我们的生活方式。从智能手机到自动驾驶汽车，AI技术已经渗透到各个领域。机器学习算法能够分析大量数据，识别模式，并做出预测。深度学习网络模拟人脑神经元的工作方式，在图像识别、自然语言处理等任务中表现出色。然而，AI技术的发展也带来了一些挑战，包括就业影响、隐私保护和伦理问题。我们需要在推进技术创新的同时，确保AI的发展符合人类的最佳利益。`;

        const inputText = document.getElementById('inputText');
        if (inputText) {
            inputText.value = sampleText;
            this.updateCharCount();
        }
    }

    downloadReport() {
        // 检查是否有检测结果
        const resultSection = document.getElementById('resultSection');
        if (!resultSection || !resultSection.classList.contains('show')) {
            this.showNotification('请先进行AIGC检测', 'warning');
            return;
        }

        // 开始生成详细报告
        this.generateDetailedReport();
    }

    optimizeContent() {
        // 跳转到主页的优化功能
        window.location.href = 'index.html';
    }

    saveToHistory(text, result) {
        try {
            const history = JSON.parse(localStorage.getItem('aigcHistory') || '[]');
            const record = {
                id: Date.now(),
                text: text.substring(0, 100) + (text.length > 100 ? '...' : ''),
                aiProbability: result.ai_probability,
                timestamp: new Date().toISOString(),
                conclusion: result.prediction || 'unknown'
            };
            
            history.unshift(record);
            // 只保留最近20条记录
            if (history.length > 20) {
                history.splice(20);
            }
            
            localStorage.setItem('aigcHistory', JSON.stringify(history));
        } catch (error) {
            console.error('保存历史记录失败:', error);
        }
    }

    loadRecentHistory() {
        try {
            const history = JSON.parse(localStorage.getItem('aigcHistory') || '[]');
            const recentHistory = document.getElementById('recentHistory');

            if (history.length === 0) {
                recentHistory.innerHTML = '<p style="color: #86868b; text-align: center; font-size: 0.9rem;">暂无检测记录</p>';
                return;
            }

            const recentItems = history.slice(0, 2);
            recentHistory.innerHTML = recentItems.map(item => {
                const date = new Date(item.timestamp).toLocaleDateString('zh-CN', {
                    month: 'short',
                    day: 'numeric'
                });
                const probability = Math.round((item.aiProbability || 0) * 100);

                let badgeColor = '#34C759';
                if (probability >= 70) badgeColor = '#FF3B30';
                else if (probability >= 30) badgeColor = '#FF9500';

                return `
                    <div class="history-item">
                        <div class="history-text">${item.text}</div>
                        <div class="history-meta">
                            <span>${date}</span>
                            <span class="probability-badge" style="background: ${badgeColor}20; color: ${badgeColor};">
                                ${probability}%
                            </span>
                        </div>
                    </div>
                `;
            }).join('');
        } catch (error) {
            console.error('加载历史记录失败:', error);
        }
    }

    showNotification(message, type = 'info') {
        // 使用现有的通知系统
        if (window.showNotification) {
            window.showNotification(message, type);
        } else {
            alert(message);
        }
    }

    // 初始化报告生成功能
    initReportGeneration() {
        // 确保库已加载
        this.checkLibrariesLoaded();
    }

    checkLibrariesLoaded() {
        const checkInterval = setInterval(() => {
            if (typeof Chart !== 'undefined' && typeof html2pdf !== 'undefined') {
                console.log('✅ 报告生成库已加载完成');
                clearInterval(checkInterval);
            }
        }, 100);

        // 10秒后停止检查
        setTimeout(() => {
            clearInterval(checkInterval);
            if (typeof Chart === 'undefined' || typeof html2pdf === 'undefined') {
                console.warn('⚠️ 报告生成库加载失败，报告功能可能不可用');
            }
        }, 10000);
    }

    // 生成详细报告
    async generateDetailedReport() {
        const text = document.getElementById('inputText').value;
        const language = this.getSelectedLanguage();

        if (!text) {
            this.showNotification('没有可用的文本内容', 'warning');
            return;
        }

        try {
            // 显示加载状态
            this.showLoadingMessage('正在进行分段检测，请稍候...');

            // 调用分段检测API
            const response = await fetch(`${this.apiBaseUrl}/detect-paragraphs`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${window.AuthManager.getToken()}`
                },
                body: JSON.stringify({ text, language })
            });

            const result = await response.json();

            if (!result.success) {
                throw new Error(result.message || '分段检测失败');
            }

            // 生成PDF报告
            await this.generatePDFReport(result.data, text);

        } catch (error) {
            console.error('报告生成错误:', error);
            this.showNotification('网络连接超时，重新上传检测', 'error');
        } finally {
            this.hideLoadingMessage();
        }
    }

    showLoadingMessage(message) {
        // 创建加载提示
        const loadingDiv = document.createElement('div');
        loadingDiv.id = 'reportLoading';
        loadingDiv.style.cssText = `
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 20px 40px;
            border-radius: 10px;
            z-index: 10000;
            text-align: center;
        `;
        loadingDiv.innerHTML = `
            <div style="margin-bottom: 10px;">📊</div>
            <div>${message}</div>
        `;
        document.body.appendChild(loadingDiv);
    }

    hideLoadingMessage() {
        const loadingDiv = document.getElementById('reportLoading');
        if (loadingDiv) {
            loadingDiv.remove();
        }
    }

    // 生成PDF报告
    async generatePDFReport(data, originalText) {
        try {
            console.log('🔍 开始生成PDF报告...');
            console.log('📊 检测数据:', data);
            console.log('📝 原始文本长度:', originalText?.length);

            // 获取用户信息
            const userInfo = await this.getUserInfo();
            console.log('👤 用户信息:', userInfo);

            const documentTitle = this.extractDocumentTitle(originalText);
            console.log('📄 文档标题:', documentTitle);

            // 检查关键数据
            if (!data.paragraphs || data.paragraphs.length === 0) {
                console.error('❌ 检测数据中没有段落信息！');
                this.showNotification('检测数据异常，无法生成报告', 'error');
                return;
            }

            if (!data.statistics) {
                console.error('❌ 检测数据中没有统计信息！');
                this.showNotification('统计数据异常，无法生成报告', 'error');
                return;
            }

            // 显示加载状态
            this.showNotification('正在生成PDF报告，请稍候...', 'info');

            // 混合方案：主方案html-to-pdfmake + 备用方案wkhtmltopdf
            console.log('🔄 开始PDF生成 - 混合方案');

            // 主方案：智能分页 + Canvas饼图 + html2canvas + jsPDF
            try {
                console.log('📄 使用智能分页方案 (Canvas饼图 + 多页PDF)');
                await this.generateSimplePDFWithCanvas(data, userInfo, documentTitle);
                return;
            } catch (error) {
                console.error('❌ 智能分页方案失败:', error);
                console.log('🔄 尝试传统单页方案');
                try {
                    await this.generatePDFWithCanvas(data, userInfo, documentTitle);
                    return;
                } catch (simpleError) {
                    console.error('❌ 传统方案也失败:', simpleError);
                }
            }

            // 备用方案：截图转PDF
            try {
                console.log('📄 尝试备用方案: 截图转PDF');
                await this.generatePDFWithScreenshot(data, userInfo, documentTitle);
                return;
            } catch (error) {
                console.error('❌ 截图转PDF方案失败:', error);
            }

            // 备用方案：wkhtmltopdf (后端生成)
            console.log('⚠️ 主方案失败，尝试备用方案: wkhtmltopdf');
            try {
                await this.generatePDFWithWkhtmltopdf(data, userInfo, documentTitle, originalText);
                return;
            } catch (error) {
                console.error('❌ wkhtmltopdf备用方案失败:', error);
            }

            // 最后备用：html2pdf (前端生成)
            console.log('⚠️ 备用方案失败，尝试最后方案: html2pdf');
            try {
                await this.generateClientSidePDF(data, userInfo, documentTitle, originalText);
            } catch (error) {
                console.error('❌ 所有PDF生成方案失败:', error);
                this.showNotification('PDF生成失败，请重试', 'error');
            }



        } catch (error) {
            console.error('PDF生成失败:', error);
            this.showNotification('网络连接超时，重新上传检测', 'error');
        }
    }

    // 备用PDF生成方法（使用浏览器原生打印）
    async generateFallbackPDF(html, userInfo) {
        try {
            console.log('🔄 使用浏览器原生打印方案生成PDF...');

            // 生成文件名
            const filename = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}`;

            // 创建新窗口用于打印
            const printWindow = window.open('', '_blank', 'width=800,height=600');

            if (!printWindow) {
                console.error('❌ 无法打开打印窗口，可能被浏览器阻止');
                this.showNotification('无法打开打印窗口，请允许弹窗后重试', 'error');
                return;
            }

            // 优化HTML内容用于打印
            const printHtml = this.optimizeHtmlForPrint(html, filename);

            // 写入HTML内容
            printWindow.document.write(printHtml);
            printWindow.document.close();

            // 等待内容加载完成
            printWindow.onload = () => {
                setTimeout(() => {
                    // 自动触发打印对话框
                    printWindow.print();

                    // 打印完成后关闭窗口
                    printWindow.onafterprint = () => {
                        printWindow.close();
                    };
                }, 500);
            };

            this.showNotification('请在打印对话框中选择"保存为PDF"', 'info');

        } catch (error) {
            console.error('❌ 打印方案失败:', error);

            // 提供备用下载方案
            this.showNotification('打印失败，为您提供HTML版本下载', 'warning');
            this.downloadHTMLReport(html, userInfo);
        }
    }

    // 优化HTML内容用于打印
    optimizeHtmlForPrint(html, filename) {
        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${filename}</title>
    <style>
        @media print {
            @page {
                size: A4;
                margin: 15mm;
            }
            body {
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                font-size: 12pt;
                line-height: 1.5;
                color: #000;
                background: white;
                margin: 0;
                padding: 0;
            }
            .no-print {
                display: none !important;
            }
            .page-break {
                page-break-before: always;
            }
            .header h1 {
                font-size: 18pt;
                margin-bottom: 10pt;
            }
            .section {
                margin-bottom: 15pt;
                break-inside: avoid;
            }
            .section h2 {
                font-size: 14pt;
                margin-bottom: 8pt;
            }
            .info-grid {
                display: table;
                width: 100%;
                border-collapse: collapse;
            }
            .info-item {
                display: table-cell;
                padding: 6pt;
                border: 1pt solid #ccc;
                width: 50%;
                vertical-align: top;
            }
            .paragraph-item {
                margin: 8pt 0;
                padding: 8pt;
                border-left: 3pt solid #ddd;
                break-inside: avoid;
            }
            .paragraph-item.ai {
                background-color: #f8f8f8;
                border-left-color: #dc3545;
            }
            .paragraph-item.human {
                background-color: #f0f8f0;
                border-left-color: #28a745;
            }
            .risk-badge {
                padding: 3pt 8pt;
                border-radius: 10pt;
                color: white;
                background-color: #dc3545;
                font-weight: bold;
            }
        }
        @media screen {
            body {
                font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
                line-height: 1.6;
                margin: 20px;
                color: #333;
                max-width: 800px;
                margin: 20px auto;
            }
            .print-hint {
                background: #e3f2fd;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                text-align: center;
                border: 1px solid #2196f3;
            }
            .print-btn {
                background: #2196f3;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 14px;
                margin: 0 5px;
            }
            .print-btn:hover {
                background: #1976d2;
            }
        }
    </style>
</head>
<body>
    <div class="print-hint no-print">
        <h3>📄 PDF报告预览</h3>
        <p>请使用浏览器的打印功能保存为PDF：</p>
        <button class="print-btn" onclick="window.print()">🖨️ 打印/保存PDF</button>
        <button class="print-btn" onclick="window.close()">❌ 关闭窗口</button>
        <p><small>💡 提示：在打印对话框中选择"保存为PDF"即可生成PDF文件</small></p>
    </div>
    ${html}
</body>
</html>`;
    }



    // 备用HTML下载方案
    downloadHTMLReport(html, userInfo) {
        try {
            const filename = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}.html`;
            const blob = new Blob([html], { type: 'text/html;charset=utf-8' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            window.URL.revokeObjectURL(url);

            this.showNotification('HTML报告下载成功，可用浏览器打开并打印为PDF', 'info');
        } catch (error) {
            console.error('❌ HTML下载失败:', error);
            this.showNotification('下载失败: ' + error.message, 'error');
        }
    }

    // 获取用户信息
    async getUserInfo() {
        try {
            const token = window.AuthManager.getToken();
            const response = await fetch('/api/auth/profile', {
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                const responseData = await response.json();
                // 处理 {success: true, user: {...}} 格式的响应
                const userData = responseData.user || responseData;
                return {
                    username: userData.username || userData.email || '用户',
                    email: userData.email || ''
                };
            }
        } catch (error) {
            console.error('获取用户信息失败:', error);
        }

        return { username: '用户', email: '' };
    }

    // 提取文档标题
    extractDocumentTitle(text) {
        // 尝试提取第一行作为标题
        const lines = text.split('\n').filter(line => line.trim());
        if (lines.length > 0) {
            const firstLine = lines[0].trim();
            // 如果第一行较短且不包含句号，可能是标题
            if (firstLine.length <= 50 && !firstLine.includes('。')) {
                return firstLine;
            }
        }

        // 否则使用前10个字符
        return text.substring(0, 10) + (text.length > 10 ? '...' : '');
    }

    // 创建报告HTML
    createReportHTML(data, originalText, userInfo, documentTitle) {
        const currentDate = new Date().toLocaleDateString('zh-CN');
        const coloredText = this.generateColoredText(data.paragraphs);

        return `
        <div style="font-family: 'Microsoft YaHei', Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 40px; background: white;">
            <!-- 报告头部 -->
            <div style="text-align: center; margin-bottom: 40px; border-bottom: 2px solid #ff6b35; padding-bottom: 20px;">
                <h1 style="color: #333; margin: 0 0 10px 0; font-size: 28px;">AIGC检测报告</h1>
                <p style="color: #666; margin: 0; font-size: 14px;">AI Generated Content Detection Report</p>
            </div>

            <!-- 基本信息 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">基本信息</h2>
                <table style="width: 100%; border-collapse: collapse;">
                    <tr>
                        <td style="padding: 8px 0; color: #666; width: 120px;">文档标题：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${documentTitle}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">作者：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${userInfo.username}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">检测时间：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${currentDate}</td>
                    </tr>
                    <tr>
                        <td style="padding: 8px 0; color: #666;">检测语言：</td>
                        <td style="padding: 8px 0; color: #333; font-weight: 500;">${data.language === 'zh' ? '中文' : '英文'}</td>
                    </tr>
                </table>
            </div>

            <!-- 检测统计 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">检测统计</h2>
                <div style="display: flex; justify-content: space-between; margin-bottom: 20px;">
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #ff3b30;">${data.statistics.highRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">高风险段落 (≥80%)</div>
                    </div>
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #ff9500;">${data.statistics.mediumRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">中风险段落 (50-80%)</div>
                    </div>
                    <div style="text-align: center; flex: 1;">
                        <div style="font-size: 24px; font-weight: bold; color: #34c759;">${data.statistics.lowRiskParagraphs}</div>
                        <div style="color: #666; font-size: 12px;">低风险段落 (<50%)</div>
                    </div>
                </div>
                <div style="text-align: center;">
                    <canvas id="reportChart" width="400" height="200"></canvas>
                </div>
            </div>

            <!-- 详细分析 -->
            <div style="margin-bottom: 30px;">
                <h2 style="color: #333; font-size: 18px; margin-bottom: 15px; border-left: 4px solid #ff6b35; padding-left: 10px;">详细分析</h2>
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; line-height: 1.8;">
                    ${coloredText}
                </div>
            </div>

            <!-- 报告说明 -->
            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #eee;">
                <h3 style="color: #333; font-size: 16px; margin-bottom: 10px;">说明：</h3>
                <ul style="color: #666; font-size: 14px; line-height: 1.6; margin: 0; padding-left: 20px;">
                    <li><span style="color: #ff3b30;">红色文本</span>：AI概率 ≥ 80%，高度疑似AI生成</li>
                    <li><span style="color: #ff9500;">橙色文本</span>：AI概率 50-80%，中等疑似AI生成</li>
                    <li><span style="color: #333;">黑色文本</span>：AI概率 < 50%，疑似人工创作</li>
                </ul>
                <p style="color: #999; font-size: 12px; margin-top: 15px; text-align: center;">
                    本报告由WriterPro AIGC检测系统生成 | 检测时间：${new Date().toLocaleString('zh-CN')}
                </p>
            </div>
        </div>
        `;
    }

    // 生成带颜色标记的文本
    generateColoredText(paragraphs) {
        return paragraphs.map((para, index) => {
            if (para.skipped) {
                return `<p style="color: #999; font-style: italic; margin: 10px 0;">[段落${index + 1}：内容过短，已跳过检测]</p>`;
            }

            if (para.error) {
                return `<p style="color: #ff3b30; margin: 10px 0;">[段落${index + 1}：检测失败] ${para.text}</p>`;
            }

            const aiPercentage = Math.round(para.ai_probability * 100);
            let color = '#333'; // 默认黑色

            if (aiPercentage >= 80) {
                color = '#ff3b30'; // 红色
            } else if (aiPercentage >= 50) {
                color = '#ff9500'; // 橙色
            }

            return `<p style="color: ${color}; margin: 15px 0; padding: 10px; border-left: 3px solid ${color}; background: ${color}10;">
                <strong>[段落${index + 1} - AI率: ${aiPercentage}%]</strong><br>
                ${para.text}
            </p>`;
        }).join('');
    }

    // 生成图表
    async generateCharts(container, statistics) {
        const canvas = container.querySelector('#reportChart');
        if (!canvas || typeof Chart === 'undefined') return;

        const ctx = canvas.getContext('2d');

        new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['高风险段落', '中风险段落', '低风险段落'],
                datasets: [{
                    data: [
                        statistics.highRiskParagraphs,
                        statistics.mediumRiskParagraphs,
                        statistics.lowRiskParagraphs
                    ],
                    backgroundColor: ['#ff3b30', '#ff9500', '#34c759'],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    }
                }
            }
        });

        // 等待图表渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));
    }

    // 主方案：使用html-to-pdfmake生成PDF
    // 主方案：使用截图转PDF生成
    async generatePDFWithScreenshot(data, userInfo, documentTitle) {
        console.log('🎯 使用截图转PDF生成');

        try {
            // 1. 创建专门的PDF布局容器
            const pdfContainer = this.createPDFContainer(data, userInfo, documentTitle);

            // 调试：检查容器内容
            console.log('📋 PDF容器HTML长度:', pdfContainer.innerHTML.length);
            console.log('📋 PDF容器内容预览:', pdfContainer.innerHTML.substring(0, 500));

            // 2. 临时添加到页面（隐藏位置）
            document.body.appendChild(pdfContainer);

            // 3. 等待渲染完成
            await this.waitForRender(pdfContainer);

            // 4. 检查是否支持html2pdf
            if (typeof html2pdf === 'undefined') {
                throw new Error('html2pdf库未加载');
            }

            // 5. 配置高清截图选项
            const options = {
                margin: 0.5,
                filename: `AIGC检测报告_${documentTitle}_${new Date().toISOString().split('T')[0]}.pdf`,
                image: {
                    type: 'jpeg',
                    quality: 1.0
                },
                html2canvas: {
                    scale: 3,                    // 超高清
                    useCORS: true,              // 跨域支持
                    allowTaint: true,           // 允许跨域图片
                    backgroundColor: '#ffffff', // 白色背景
                    letterRendering: true,      // 更好的文字渲染
                    scrollX: 0,
                    scrollY: 0,
                    width: pdfContainer.scrollWidth,
                    height: pdfContainer.scrollHeight
                },
                jsPDF: {
                    unit: 'in',
                    format: 'a4',
                    orientation: 'portrait',
                    compress: true
                },
                pagebreak: {
                    mode: ['avoid-all', 'css', 'legacy'],
                    before: '.page-break-before',
                    after: '.page-break-after'
                }
            };

            // 6. 生成PDF
            console.log('📸 开始截图转PDF...');
            await html2pdf().set(options).from(pdfContainer).save();

            // 7. 清理临时容器
            document.body.removeChild(pdfContainer);

            console.log('✅ 截图转PDF生成成功');
            this.showNotification('PDF报告生成成功！', 'success');

        } catch (error) {
            console.error('❌ 截图转PDF失败:', error);

            // 备用方案：显示PDF内容让用户手动保存
            console.log('🔄 尝试备用方案：显示PDF内容');
            try {
                // 使用现有的HTML生成方法
                const htmlContent = this.createReportHTML(data, userInfo, documentTitle);
                this.showPDFPreviewFromHTML(htmlContent, documentTitle);
            } catch (previewError) {
                console.error('❌ 预览方案也失败:', previewError);
                this.showNotification('PDF生成失败: ' + error.message, 'error');
            }
            throw error;
        }
    }

    // 配置PDFMake中文字体 - 方案B：简化配置
    configurePDFMakeFonts() {
        console.log('🔤 配置PDFMake中文字体（简化配置）...');

        pdfMake.addFonts({
            Roboto: {
                normal: 'Roboto-Regular.ttf',
                bold: 'Roboto-Medium.ttf',
                italics: 'Roboto-Italic.ttf',
                bolditalics: 'Roboto-MediumItalic.ttf'
            },
            // 使用Google Fonts的Noto Sans SC字体（可靠的CDN）
            NotoSansSC: {
                normal: 'https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf',
                bold: 'https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf',
                italics: 'https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaG9_FnYw.ttf',
                bolditalics: 'https://fonts.gstatic.com/s/notosanssc/v38/k3kCo84MPvpLmixcA63oeAL7Iqp5IZJF9bmaGzjCnYw.ttf'
            }
        });

        console.log('✅ 中文字体配置完成');
    }

    // 备用方案：调用后端wkhtmltopdf
    async generatePDFWithWkhtmltopdf(data, userInfo, documentTitle, originalText) {
        console.log('🔄 调用后端wkhtmltopdf生成API...');

        const response = await fetch('/api/pdf/generate-wkhtmltopdf', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${window.AuthManager.getToken()}`
            },
            body: JSON.stringify({
                detectionData: data,
                userInfo: userInfo,
                documentTitle: documentTitle,
                originalText: originalText
            })
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }

        console.log('✅ wkhtmltopdf PDF生成成功');
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `AIGC检测报告_${userInfo.username}_${new Date().toISOString().split('T')[0]}.pdf`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        this.showNotification('PDF报告生成成功！', 'success');
    }

    // 生成报告HTML内容 - 通用版本
    createReportHTML(data, userInfo, documentTitle) {
        const { paragraphs, statistics, language } = data;

        // 1. 生成报告编号
        const reportNumber = this.generateReportNumber();

        // 2. 计算字符数统计
        const charStats = this.calculateCharacterStats(paragraphs);

        // 3. 章节分组
        const chapters = this.groupParagraphsIntoChapters(paragraphs, documentTitle);

        // 4. 生成章节统计
        const chapterStats = chapters.map(chapter => this.calculateChapterStats(chapter));

        // 5. 生成图表
        const chartSVG = this.generateChartSVG(statistics);

        // 6. 生成章节统计表格
        const chapterTableRows = chapterStats.map((stats, index) => `
            <tr>
                <td>${index + 1}</td>
                <td>${stats.aiFeatureValue}%</td>
                <td>${stats.redChars} / ${stats.totalChars}</td>
                <td>${stats.chapterName}</td>
            </tr>
        `).join('');

        // 7. 生成详细章节内容
        const chapterDetails = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `<span style="color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'};">
                    ${para.text}
                    <span class="para-stats">[${probability}% | ${charCount}字]</span>
                </span><br><br>`;
            }).join('');

            return `
                <div class="chapter">
                    <h3>${chapter.name}</h3>
                    <div class="chapter-stats">
                        AI特征值：${stats.aiFeatureValue}% AI特征字符数/章节字符数 ${stats.redChars} / ${stats.totalChars}
                    </div>
                    <div class="chapter-content">
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC检测 · 全文报告单</title>
</head>
<body>
    <div class="report-container">
        <!-- 报告头部 -->
        <div class="report-header">
            <h1>AIGC检测 · 全文报告单</h1>
            <div class="report-meta">
                <div class="report-number">NO. ${reportNumber}</div>
                <div class="detection-time">检测时间：${new Date().toLocaleString('zh-CN')}</div>
            </div>
            <div class="document-info">
                <div>篇名：${documentTitle}</div>
                <div>作者：${userInfo.username || '未知用户'}</div>
                <div>文件名：${documentTitle}</div>
                <div>全文检测结果 writerpro检测 https://writerpro.cn</div>
            </div>
        </div>

        <!-- 统计概览 -->
        <div class="statistics-overview">
            <div class="chart-section">
                ${chartSVG}
            </div>
            <div class="stats-section">
                <div class="main-stats">
                    <div>AI特征值：${charStats.aiFeatureValue}%</div>
                    <div>AI特征字符数：${charStats.redChars}</div>
                    <div>总字符数：${charStats.totalChars}</div>
                </div>
                <div class="legend">
                    <div class="legend-item red">■ AI特征显著（计入AI特征字符数）红色</div>
                    <div class="legend-item yellow">■ AI特征疑似（未计入AI特征字符数）黄色</div>
                    <div class="legend-item black">■ 未标识部分 黑色</div>
                </div>
                <div class="detailed-stats">
                    <div>红色AI特征值：${charStats.aiFeatureValue}%</div>
                    <div>红色AI特征字符数：${charStats.redChars}</div>
                </div>
            </div>
        </div>

        <!-- 章节统计表格 -->
        <div class="chapter-table">
            <h2>论文章节检测结果</h2>
            <table>
                <thead>
                    <tr>
                        <th>序号</th>
                        <th>AI特征值</th>
                        <th>AI特征字符数/章节字符数</th>
                        <th>章节名称</th>
                    </tr>
                </thead>
                <tbody>
                    ${chapterTableRows}
                </tbody>
            </table>
        </div>

        <!-- 详细章节内容 -->
        <div class="chapter-details">
            ${chapterDetails}
        </div>

        <!-- 报告说明 -->
        <div class="report-notes">
            <h3>最后说明:</h3>
            <ol>
                <li>支持中、英文内容检测；</li>
                <li>AI特征值=AI特征字符数/总字符数；</li>
                <li>红色代表AI特征显著部分，计入AI特征字符数；</li>
                <li>黄色代表AI特征疑似部分，未计入AI特征字符数；</li>
                <li>检测结果仅供参考，最终判定是否存在学术不端行为时，需结合人工复核、机构审查以及具体学术政策的综合应用进行审慎判断。</li>
            </ol>
        </div>
    </div>

    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        .report-container {
            max-width: 210mm;
            margin: 0 auto;
            padding: 20mm;
            background: white;
            color: #333;
            line-height: 1.6;
        }
        .report-header {
            text-align: center;
            border-bottom: 2px solid #333;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .report-header h1 {
            margin: 0 0 20px 0;
            font-size: 28px;
            font-weight: bold;
            color: #333;
        }
        .report-meta {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
            font-size: 14px;
            font-weight: bold;
        }
        .document-info {
            text-align: left;
            margin-top: 20px;
        }
        .document-info div {
            margin: 8px 0;
            font-size: 16px;
        }
        .statistics-overview {
            display: flex;
            gap: 40px;
            margin: 30px 0;
            align-items: flex-start;
        }
        .chart-section {
            flex: 1;
            text-align: center;
        }
        .stats-section {
            flex: 1;
        }
        .main-stats {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
        }
        .main-stats div {
            margin: 10px 0;
            font-size: 16px;
            font-weight: bold;
        }
        .legend {
            margin: 20px 0;
        }
        .legend-item {
            display: flex;
            align-items: center;
            margin: 8px 0;
            font-size: 14px;
        }
        .legend-item::before {
            content: '■';
            margin-right: 8px;
            font-size: 16px;
        }
        .legend-item.red::before { color: #dc3545; }
        .legend-item.yellow::before { color: #fd7e14; }
        .legend-item.black::before { color: #333333; }
        .detailed-stats {
            background: #fff3cd;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #fd7e14;
        }
        .detailed-stats div {
            margin: 8px 0;
            font-size: 14px;
        }
        .chapter-table {
            margin: 30px 0;
        }
        .chapter-table h2 {
            font-size: 20px;
            margin-bottom: 15px;
            color: #333;
        }
        .chapter-table table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            font-size: 14px;
        }
        .chapter-table th,
        .chapter-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .chapter-table th {
            background: #f8f9fa;
            font-weight: bold;
            color: #333;
        }
        .chapter-table tr:nth-child(even) {
            background: #f9f9f9;
        }
        .chapter-details {
            margin: 30px 0;
        }
        .chapter {
            margin: 30px 0;
            page-break-inside: avoid;
        }
        .chapter h3 {
            font-size: 18px;
            color: #333;
            margin-bottom: 10px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 8px;
        }
        .chapter-stats {
            background: #f8f9fa;
            padding: 10px;
            margin: 15px 0;
            border-radius: 4px;
            font-size: 14px;
            font-weight: bold;
        }
        .chapter-content {
            line-height: 2.0;
            text-align: justify;
        }
        .para-stats {
            font-size: 12px;
            color: #666;
            margin-left: 10px;
            font-weight: normal;
        }
        .report-notes {
            margin-top: 40px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }
        .report-notes h3 {
            color: #333;
            margin-bottom: 15px;
        }
        .report-notes ol {
            padding-left: 20px;
        }
        .report-notes li {
            margin: 8px 0;
            line-height: 1.6;
            font-size: 14px;
        }
    </style>
</body>
</html>
            </div>
        `;
    }

    // 生成SVG图表（移植自backend）
    generateChartSVG(statistics) {
        const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = statistics;
        const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;

        if (total === 0) {
            return '<p style="text-align: center; color: #666;">暂无数据</p>';
        }

        // 计算百分比和角度
        const highPercent = Math.round((highRiskParagraphs / total) * 100);
        const mediumPercent = Math.round((mediumRiskParagraphs / total) * 100);
        const lowPercent = 100 - highPercent - mediumPercent;

        // 计算饼图路径
        const radius = 80;
        const centerX = 120;
        const centerY = 120;

        let currentAngle = 0;
        const paths = [];

        // 高风险扇形
        if (highRiskParagraphs > 0) {
            const angle = (highRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#dc3545" stroke="white" stroke-width="2"/>`);
            currentAngle += angle;
        }

        // 中风险扇形
        if (mediumRiskParagraphs > 0) {
            const angle = (mediumRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#fd7e14" stroke="white" stroke-width="2"/>`);
            currentAngle += angle;
        }

        // 低风险扇形
        if (lowRiskParagraphs > 0) {
            const angle = (lowRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#198754" stroke="white" stroke-width="2"/>`);
        }

        return `
            <div style="display: flex; align-items: center; justify-content: center; gap: 40px;">
                <svg width="240" height="240" viewBox="0 0 240 240">
                    ${paths.join('')}
                </svg>
                <div style="display: flex; flex-direction: column; gap: 15px;">
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 20px; height: 20px; background: #dc3545; border-radius: 3px;"></div>
                        <span style="font-size: 16px;">高风险: ${highRiskParagraphs}段 (${highPercent}%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 20px; height: 20px; background: #fd7e14; border-radius: 3px;"></div>
                        <span style="font-size: 16px;">中风险: ${mediumRiskParagraphs}段 (${mediumPercent}%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 10px;">
                        <div style="width: 20px; height: 20px; background: #198754; border-radius: 3px;"></div>
                        <span style="font-size: 16px;">低风险: ${lowRiskParagraphs}段 (${lowPercent}%)</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 创建饼图扇形路径
    createPieSlice(centerX, centerY, radius, startAngle, endAngle) {
        const start = this.polarToCartesian(centerX, centerY, radius, endAngle);
        const end = this.polarToCartesian(centerX, centerY, radius, startAngle);
        const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

        return [
            "M", centerX, centerY,
            "L", start.x, start.y,
            "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
            "Z"
        ].join(" ");
    }

    // 极坐标转直角坐标
    polarToCartesian(centerX, centerY, radius, angleInDegrees) {
        const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
        return {
            x: centerX + (radius * Math.cos(angleInRadians)),
            y: centerY + (radius * Math.sin(angleInRadians))
        };
    }

    // 截图转PDF专用方法

    // 创建PDF专用容器
    createPDFContainer(data, userInfo, documentTitle) {
        const container = document.createElement('div');
        container.style.cssText = `
            position: fixed;
            left: -9999px;
            top: 0;
            width: 794px;
            background: white;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            padding: 40px;
            box-sizing: border-box;
            z-index: -1000;
        `;

        // 生成专门为截图优化的HTML
        const htmlContent = this.createScreenshotOptimizedHTML(data, userInfo, documentTitle);
        console.log('📋 生成的HTML内容长度:', htmlContent.length);
        console.log('📋 HTML内容预览:', htmlContent.substring(0, 200));
        container.innerHTML = htmlContent;

        return container;
    }

    // 等待渲染完成
    async waitForRender(container) {
        // 1. 等待图片加载
        const images = container.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                    // 超时保护
                    setTimeout(resolve, 5000);
                }
            });
        });

        await Promise.all(imagePromises);

        // 2. 等待字体加载
        if (document.fonts && document.fonts.ready) {
            await document.fonts.ready;
        }

        // 3. 额外等待确保渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('✅ 渲染完成，准备截图');
    }

    // 生成专门为截图优化的HTML
    createScreenshotOptimizedHTML(data, userInfo, documentTitle) {
        console.log('🔧 开始生成截图优化HTML');
        console.log('📊 数据检查:', {
            paragraphs: data.paragraphs?.length || 0,
            statistics: data.statistics,
            language: data.language,
            userInfo: userInfo,
            documentTitle: documentTitle
        });

        const { paragraphs, statistics, language } = data;

        // 1. 生成报告编号
        const reportNumber = this.generateReportNumber();

        // 2. 计算字符数统计
        const charStats = this.calculateCharacterStats(paragraphs);

        // 3. 章节分组
        const chapters = this.groupParagraphsIntoChapters(paragraphs, documentTitle);

        // 4. 生成章节统计
        const chapterStats = chapters.map(chapter => this.calculateChapterStats(chapter));

        // 5. 生成图表（简化版，适合截图）
        const chartSVG = this.generateSimpleChartSVG(statistics);

        // 6. 生成章节统计表格
        const chapterTableRows = chapterStats.map((stats, index) => `
            <tr>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${index + 1}</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${stats.aiFeatureValue}%</td>
                <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${stats.redChars} / ${stats.totalChars}</td>
                <td style="padding: 8px; border: 1px solid #ddd;">${stats.chapterName}</td>
            </tr>
        `).join('');

        // 7. 生成详细章节内容
        const chapterDetails = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `
                    <div style="margin: 10px 0; line-height: 1.8;">
                        <span style="color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'};">
                            ${para.text}
                        </span>
                        <span style="font-size: 12px; color: #666; margin-left: 10px;">[${probability}% | ${charCount}字]</span>
                    </div>
                `;
            }).join('');

            return `
                <div style="margin: 20px 0; page-break-inside: avoid;">
                    <h3 style="font-size: 16px; color: #333; margin-bottom: 10px; border-bottom: 1px solid #ddd; padding-bottom: 5px;">
                        ${chapter.name}
                    </h3>
                    <div style="background: #f8f9fa; padding: 10px; margin: 10px 0; border-radius: 4px; font-size: 12px; font-weight: bold;">
                        AI特征值：${stats.aiFeatureValue}% AI特征字符数/章节字符数 ${stats.redChars} / ${stats.totalChars}
                    </div>
                    <div>
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div style="font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; color: #333; line-height: 1.6; background: white;">
                <!-- 报告头部 -->
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 20px; margin-bottom: 30px;">
                    <h1 style="margin: 0 0 20px 0; font-size: 24px; font-weight: bold; color: #333;">AIGC检测 · 全文报告单</h1>
                    <div style="display: flex; justify-content: space-between; margin: 15px 0; font-size: 12px; font-weight: bold;">
                        <div>NO. ${reportNumber}</div>
                        <div>检测时间：${new Date().toLocaleString('zh-CN')}</div>
                    </div>
                    <div style="text-align: left; margin-top: 15px; font-size: 14px;">
                        <div style="margin: 5px 0;">篇名：${documentTitle}</div>
                        <div style="margin: 5px 0;">作者：${userInfo.username || '未知用户'}</div>
                        <div style="margin: 5px 0;">文件名：${documentTitle}</div>
                        <div style="margin: 5px 0;">全文检测结果 writerpro检测 https://writerpro.cn</div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div style="display: flex; gap: 30px; margin: 30px 0; align-items: flex-start;">
                    <div style="flex: 1; text-align: center;">
                        ${chartSVG}
                    </div>
                    <div style="flex: 1;">
                        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                            <div style="margin: 8px 0; font-size: 14px; font-weight: bold;">AI特征值：${charStats.aiFeatureValue}%</div>
                            <div style="margin: 8px 0; font-size: 14px; font-weight: bold;">AI特征字符数：${charStats.redChars}</div>
                            <div style="margin: 8px 0; font-size: 14px; font-weight: bold;">总字符数：${charStats.totalChars}</div>
                        </div>
                        <div style="margin: 15px 0;">
                            <div style="display: flex; align-items: center; margin: 6px 0; font-size: 12px;">
                                <span style="color: #dc3545; margin-right: 8px; font-size: 14px;">■</span>
                                AI特征显著（计入AI特征字符数）红色
                            </div>
                            <div style="display: flex; align-items: center; margin: 6px 0; font-size: 12px;">
                                <span style="color: #fd7e14; margin-right: 8px; font-size: 14px;">■</span>
                                AI特征疑似（未计入AI特征字符数）黄色
                            </div>
                            <div style="display: flex; align-items: center; margin: 6px 0; font-size: 12px;">
                                <span style="color: #333333; margin-right: 8px; font-size: 14px;">■</span>
                                未标识部分 黑色
                            </div>
                        </div>
                        <div style="background: #fff3cd; padding: 10px; border-radius: 8px; border-left: 4px solid #fd7e14;">
                            <div style="margin: 6px 0; font-size: 12px;">红色AI特征值：${charStats.aiFeatureValue}%</div>
                            <div style="margin: 6px 0; font-size: 12px;">红色AI特征字符数：${charStats.redChars}</div>
                        </div>
                    </div>
                </div>

                <!-- 章节统计表格 -->
                <div style="margin: 30px 0;">
                    <h2 style="font-size: 18px; margin-bottom: 15px; color: #333;">论文章节检测结果</h2>
                    <table style="width: 100%; border-collapse: collapse; margin: 15px 0; font-size: 12px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 10px; font-weight: bold; color: #333;">序号</th>
                                <th style="border: 1px solid #ddd; padding: 10px; font-weight: bold; color: #333;">AI特征值</th>
                                <th style="border: 1px solid #ddd; padding: 10px; font-weight: bold; color: #333;">AI特征字符数/章节字符数</th>
                                <th style="border: 1px solid #ddd; padding: 10px; font-weight: bold; color: #333;">章节名称</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${chapterTableRows}
                        </tbody>
                    </table>
                </div>

                <!-- 详细章节内容 -->
                <div style="margin: 30px 0;">
                    ${chapterDetails}
                </div>

                <!-- 报告说明 -->
                <div style="margin-top: 30px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                    <h3 style="color: #333; margin-bottom: 10px; font-size: 14px;">最后说明:</h3>
                    <ol style="padding-left: 20px; font-size: 12px;">
                        <li style="margin: 6px 0; line-height: 1.6;">支持中、英文内容检测；</li>
                        <li style="margin: 6px 0; line-height: 1.6;">AI特征值=AI特征字符数/总字符数；</li>
                        <li style="margin: 6px 0; line-height: 1.6;">红色代表AI特征显著部分，计入AI特征字符数；</li>
                        <li style="margin: 6px 0; line-height: 1.6;">黄色代表AI特征疑似部分，未计入AI特征字符数；</li>
                        <li style="margin: 6px 0; line-height: 1.6;">检测结果仅供参考，最终判定是否存在学术不端行为时，需结合人工复核、机构审查以及具体学术政策的综合应用进行审慎判断。</li>
                    </ol>
                </div>
            </div>
        `;
    }

    // 核心算法函数 - 按设计文档实现

    // 1. 生成报告编号
    generateReportNumber() {
        const timestamp = Date.now();
        const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
        return `${timestamp}${random}`;
    }

    // 2. 字符数统计算法
    calculateCharacterStats(paragraphs) {
        let totalChars = 0;
        let redChars = 0; // AI≥80%
        let yellowChars = 0; // AI 50-80%
        let blackChars = 0; // AI<50%

        paragraphs.forEach(para => {
            const chars = para.text.length;
            const aiProb = (para.ai_probability || 0) * 100;

            totalChars += chars;

            if (aiProb >= 80) {
                redChars += chars;
            } else if (aiProb >= 50) {
                yellowChars += chars;
            } else {
                blackChars += chars;
            }
        });

        return {
            totalChars,
            redChars,
            yellowChars,
            blackChars,
            aiFeatureValue: totalChars > 0 ? (redChars / totalChars * 100).toFixed(1) : '0.0'
        };
    }

    // 3. 章节分组策略
    groupParagraphsIntoChapters(paragraphs, documentTitle) {
        const chaptersPerGroup = Math.max(1, Math.ceil(paragraphs.length / 5)); // 每章节包含段落数，最少1个
        const chapters = [];

        for (let i = 0; i < paragraphs.length; i += chaptersPerGroup) {
            const chapterParagraphs = paragraphs.slice(i, i + chaptersPerGroup);
            chapters.push({
                id: Math.floor(i / chaptersPerGroup) + 1,
                name: `${documentTitle}_第${Math.floor(i / chaptersPerGroup) + 1}部分`,
                paragraphs: chapterParagraphs
            });
        }

        return chapters;
    }

    // 4. 章节统计计算
    calculateChapterStats(chapter) {
        const stats = this.calculateCharacterStats(chapter.paragraphs);
        return {
            chapterId: chapter.id,
            chapterName: chapter.name,
            aiFeatureValue: stats.aiFeatureValue,
            redChars: stats.redChars,
            totalChars: stats.totalChars
        };
    }

    // 5. 颜色标记系统
    getColorStyle(aiProbability) {
        const prob = (aiProbability || 0) * 100;

        if (prob >= 80) {
            return {
                color: '#dc3545', // 红色
                label: 'AI特征显著',
                includeInStats: true
            };
        } else if (prob >= 50) {
            return {
                color: '#fd7e14', // 黄色
                label: 'AI特征疑似',
                includeInStats: false
            };
        } else {
            return {
                color: '#333333', // 黑色
                label: '未标识',
                includeInStats: false
            };
        }
    }

    // 生成简化的SVG图表（专门为截图优化）
    generateSimpleChartSVG(statistics) {
        const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = statistics;
        const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;

        if (total === 0) {
            return '<p style="text-align: center; color: #666; font-size: 14px;">暂无数据</p>';
        }

        // 计算百分比
        const highPercent = Math.round((highRiskParagraphs / total) * 100);
        const mediumPercent = Math.round((mediumRiskParagraphs / total) * 100);
        const lowPercent = 100 - highPercent - mediumPercent;

        // 计算饼图路径
        const radius = 60;
        const centerX = 80;
        const centerY = 80;

        let currentAngle = 0;
        const paths = [];

        // 高风险扇形
        if (highRiskParagraphs > 0) {
            const angle = (highRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#dc3545" stroke="white" stroke-width="2"/>`);
            currentAngle += angle;
        }

        // 中风险扇形
        if (mediumRiskParagraphs > 0) {
            const angle = (mediumRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#fd7e14" stroke="white" stroke-width="2"/>`);
            currentAngle += angle;
        }

        // 低风险扇形
        if (lowRiskParagraphs > 0) {
            const angle = (lowRiskParagraphs / total) * 360;
            const path = this.createPieSlice(centerX, centerY, radius, currentAngle, currentAngle + angle);
            paths.push(`<path d="${path}" fill="#198754" stroke="white" stroke-width="2"/>`);
        }

        return `
            <div style="display: flex; align-items: center; justify-content: center; gap: 20px;">
                <svg width="160" height="160" viewBox="0 0 160 160" style="background: white;">
                    ${paths.join('')}
                </svg>
                <div style="display: flex; flex-direction: column; gap: 8px;">
                    <div style="display: flex; align-items: center; gap: 8px; font-size: 12px;">
                        <div style="width: 12px; height: 12px; background: #dc3545; border-radius: 2px;"></div>
                        <span>高风险: ${highRiskParagraphs}段 (${highPercent}%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px; font-size: 12px;">
                        <div style="width: 12px; height: 12px; background: #fd7e14; border-radius: 2px;"></div>
                        <span>中风险: ${mediumRiskParagraphs}段 (${mediumPercent}%)</span>
                    </div>
                    <div style="display: flex; align-items: center; gap: 8px; font-size: 12px;">
                        <div style="width: 12px; height: 12px; background: #198754; border-radius: 2px;"></div>
                        <span>低风险: ${lowRiskParagraphs}段 (${lowPercent}%)</span>
                    </div>
                </div>
            </div>
        `;
    }

    // 显示PDF预览（备用方案）
    showPDFPreview(pdfContainer, documentTitle) {
        // 创建新窗口显示PDF内容
        const previewWindow = window.open('', '_blank', 'width=800,height=600');

        if (!previewWindow) {
            this.showNotification('无法打开预览窗口，请允许弹窗后重试', 'error');
            return;
        }

        const previewHTML = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC检测报告预览 - ${documentTitle}</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .preview-actions {
            text-align: center;
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .pdf-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        @media print {
            .preview-header, .preview-actions {
                display: none !important;
            }
            .pdf-content {
                box-shadow: none;
                border-radius: 0;
            }
            body {
                background: white;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h2>📄 AIGC检测报告预览</h2>
        <p>您可以使用浏览器的打印功能保存为PDF</p>
    </div>

    <div class="preview-actions">
        <button class="btn" onclick="window.print()">🖨️ 打印/保存PDF</button>
        <button class="btn" onclick="window.close()">❌ 关闭窗口</button>
    </div>

    <div class="pdf-content">
        ${pdfContainer.innerHTML}
    </div>

    <script>
        // 自动聚焦到打印按钮
        document.querySelector('.btn').focus();
    </script>
</body>
</html>`;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();

        this.showNotification('PDF预览已打开，请使用浏览器打印功能保存为PDF', 'info');
    }

    // 从HTML内容显示PDF预览
    showPDFPreviewFromHTML(htmlContent, documentTitle) {
        // 创建新窗口显示PDF内容
        const previewWindow = window.open('', '_blank', 'width=800,height=600');

        if (!previewWindow) {
            this.showNotification('无法打开预览窗口，请允许弹窗后重试', 'error');
            return;
        }

        const previewHTML = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AIGC检测报告 - ${documentTitle}</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .preview-header {
            background: #007bff;
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            text-align: center;
        }
        .preview-actions {
            text-align: center;
            margin-bottom: 20px;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .pdf-content {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        @media print {
            .preview-header, .preview-actions {
                display: none !important;
            }
            .pdf-content {
                box-shadow: none;
                border-radius: 0;
            }
            body {
                background: white;
                padding: 0;
            }
        }
    </style>
</head>
<body>
    <div class="preview-header">
        <h2>📄 AIGC检测报告</h2>
        <p>您可以使用 Ctrl+P 或浏览器菜单中的"打印"功能保存为PDF</p>
    </div>

    <div class="preview-actions">
        <button class="btn" onclick="window.print()">🖨️ 打印/保存PDF</button>
        <button class="btn" onclick="window.close()">❌ 关闭窗口</button>
    </div>

    <div class="pdf-content">
        ${htmlContent}
    </div>
</body>
</html>`;

        previewWindow.document.write(previewHTML);
        previewWindow.document.close();

        this.showNotification('报告预览已打开！请使用 Ctrl+P 保存为PDF', 'success');
    }

    // html2canvas + jsPDF方案 (A4分页 + 页码)
    async generatePDFWithCanvas(data, userInfo, documentTitle) {
        console.log('🎯 使用html2canvas + jsPDF生成PDF (A4分页版本)');

        try {
            // 1. 检查依赖库
            if (typeof html2canvas === 'undefined') {
                throw new Error('html2canvas库未加载');
            }

            // jsPDF可能在window.jspdf.jsPDF中
            let jsPDF;
            if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
                jsPDF = window.jspdf.jsPDF;
            } else if (typeof window.jsPDF !== 'undefined') {
                jsPDF = window.jsPDF;
            } else {
                throw new Error('jsPDF库未加载');
            }

            // 2. A4尺寸配置 (210mm × 297mm)
            const A4_WIDTH_MM = 210;
            const A4_HEIGHT_MM = 297;
            const MARGIN_LEFT_MM = 7;   // 0.7cm
            const MARGIN_RIGHT_MM = 7;  // 0.7cm
            const MARGIN_TOP_MM = 12;   // 1.2cm
            const MARGIN_BOTTOM_MM = 12; // 1.2cm
            const FOOTER_HEIGHT_MM = 8; // 页脚高度

            // 转换为点 (1mm = 2.834645669 points)
            const MM_TO_POINTS = 2.834645669;
            const A4_WIDTH = A4_WIDTH_MM * MM_TO_POINTS;
            const A4_HEIGHT = A4_HEIGHT_MM * MM_TO_POINTS;
            const MARGIN_LEFT = MARGIN_LEFT_MM * MM_TO_POINTS;
            const MARGIN_RIGHT = MARGIN_RIGHT_MM * MM_TO_POINTS;
            const MARGIN_TOP = MARGIN_TOP_MM * MM_TO_POINTS;
            const MARGIN_BOTTOM = MARGIN_BOTTOM_MM * MM_TO_POINTS;
            const FOOTER_HEIGHT = FOOTER_HEIGHT_MM * MM_TO_POINTS;

            // 内容区域尺寸
            const CONTENT_WIDTH = A4_WIDTH - MARGIN_LEFT - MARGIN_RIGHT;
            const CONTENT_HEIGHT = A4_HEIGHT - MARGIN_TOP - MARGIN_BOTTOM - FOOTER_HEIGHT;

            console.log('📐 A4尺寸配置:', {
                A4_WIDTH, A4_HEIGHT,
                CONTENT_WIDTH, CONTENT_HEIGHT,
                MARGIN_LEFT, MARGIN_RIGHT, MARGIN_TOP, MARGIN_BOTTOM
            });

            // 3. 创建PDF容器 (按A4内容区域尺寸)
            const container = this.createA4PDFContainer(data, userInfo, documentTitle, CONTENT_WIDTH);
            document.body.appendChild(container);

            // 4. 等待渲染和图片加载
            await this.waitForCanvasRender(container);

            // 5. html2canvas截图
            console.log('📸 开始html2canvas截图...');
            const canvas = await html2canvas(container, {
                scale: 2, // 2倍高清
                useCORS: true,
                allowTaint: true,
                backgroundColor: '#ffffff',
                letterRendering: true,
                logging: false
            });

            console.log('✅ 截图完成，canvas尺寸:', canvas.width, 'x', canvas.height);

            // 6. 创建PDF并分页
            const pdf = new jsPDF('p', 'pt', [A4_WIDTH, A4_HEIGHT]);
            await this.addCanvasContentToA4PDF(pdf, canvas, {
                A4_WIDTH, A4_HEIGHT,
                CONTENT_WIDTH, CONTENT_HEIGHT,
                MARGIN_LEFT, MARGIN_TOP, MARGIN_BOTTOM,
                FOOTER_HEIGHT
            });

            // 7. 下载PDF
            const fileName = `AIGC检测报告_${documentTitle}_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

            // 8. 清理
            document.body.removeChild(container);

            console.log('✅ PDF生成成功');
            this.showNotification('PDF报告生成成功！', 'success');

        } catch (error) {
            console.error('❌ html2canvas + jsPDF失败:', error);
            this.showNotification('PDF生成失败: ' + error.message, 'error');
            throw error;
        }
    }

    // 创建A4尺寸的PDF容器
    createA4PDFContainer(data, userInfo, documentTitle, contentWidth) {
        const container = document.createElement('div');

        // 将点转换为像素 (假设96 DPI)
        const contentWidthPx = Math.round(contentWidth * 96 / 72);

        // 设置容器样式 (A4内容区域)
        container.style.cssText = `
            position: fixed;
            left: ${-2 * contentWidthPx}px;
            top: 0;
            padding: 0;
            width: ${contentWidthPx}px;
            box-sizing: border-box;
            background: white;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            z-index: -1000;
            font-size: 12px;
            line-height: 1.6;
        `;

        // 生成HTML内容
        container.innerHTML = this.createA4OptimizedHTML(data, userInfo, documentTitle);

        return container;
    }

    // 创建专门为canvas优化的PDF容器 (保留原方法)
    createCanvasPDFContainer(data, userInfo, documentTitle) {
        const container = document.createElement('div');

        // 设置容器样式 (基于张鑫旭的经验)
        const originWidth = 700;
        container.style.cssText = `
            position: fixed;
            left: ${-2 * originWidth}px;
            top: 0;
            padding: 16px;
            width: ${originWidth}px;
            box-sizing: content-box;
            background: white;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            z-index: -1000;
        `;

        // 生成HTML内容
        container.innerHTML = this.createCanvasOptimizedHTML(data, userInfo, documentTitle);

        return container;
    }

    // 等待canvas渲染完成
    async waitForCanvasRender(container) {
        // 1. 等待图片加载
        const images = container.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    // 转换图片为base64 (解决跨域问题)
                    const src = img.src;
                    if (src && /^http/.test(src)) {
                        fetch(src).then(res => res.blob()).then(blob => {
                            const reader = new FileReader();
                            reader.onload = function() {
                                img.src = this.result;
                                resolve();
                            };
                            reader.readAsDataURL(blob);
                        }).catch(() => resolve());
                    } else {
                        img.onload = resolve;
                        img.onerror = resolve;
                        setTimeout(resolve, 3000); // 超时保护
                    }
                }
            });
        });

        await Promise.all(imagePromises);

        // 2. 等待字体加载
        if (document.fonts && document.fonts.ready) {
            await document.fonts.ready;
        }

        // 3. 额外等待确保渲染完成
        await new Promise(resolve => setTimeout(resolve, 1000));

        console.log('✅ Canvas渲染完成');
    }

    // 生成A4优化的HTML (紧凑布局，适合分页)
    createA4OptimizedHTML(data, userInfo, documentTitle) {
        const { paragraphs, statistics, language } = data;

        // 1. 基础数据计算
        const reportNumber = this.generateReportNumber();
        const charStats = this.calculateCharacterStats(paragraphs);
        const chapters = this.groupParagraphsIntoChapters(paragraphs, documentTitle);
        const chapterStats = chapters.map(chapter => this.calculateChapterStats(chapter));

        // 2. 生成简化的图表 (纯HTML+CSS，避免SVG)
        const chartHTML = this.generateSimpleHTMLChart(statistics);

        // 3. 生成章节统计表格
        const chapterTableRows = chapterStats.map((stats, index) => `
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${index + 1}</td>
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${stats.aiFeatureValue}%</td>
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${stats.redChars} / ${stats.totalChars}</td>
                <td style="padding: 6px; font-size: 11px;">${stats.chapterName}</td>
            </tr>
        `).join('');

        // 4. 生成详细章节内容
        const chapterDetails = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `
                    <p style="margin: 6px 0; line-height: 1.6; color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'}; font-size: 11px;">
                        ${para.text}
                        <span style="font-size: 10px; color: #666; margin-left: 6px; font-weight: normal;">[${probability}% | ${charCount}字]</span>
                    </p>
                `;
            }).join('');

            return `
                <div style="margin: 15px 0; page-break-inside: avoid;">
                    <h3 style="font-size: 14px; color: #333; margin: 10px 0 8px 0; padding-bottom: 3px; border-bottom: 1px solid #ddd;">
                        ${chapter.name}
                    </h3>
                    <div style="background: #f8f9fa; padding: 6px; margin: 8px 0; border-radius: 3px; font-size: 11px; font-weight: bold;">
                        AI特征值：${stats.aiFeatureValue}% AI特征字符数/章节字符数 ${stats.redChars} / ${stats.totalChars}
                    </div>
                    <div style="margin: 8px 0;">
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        // 5. 返回完整HTML (紧凑布局)
        return `
            <div style="font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; color: #333; line-height: 1.5; background: white; padding: 15px;">

                <!-- 报告头部 -->
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 12px; margin-bottom: 20px;">
                    <h1 style="margin: 0 0 12px 0; font-size: 18px; font-weight: bold; color: #333;">AIGC检测 · 全文报告单</h1>

                    <table style="width: 100%; font-size: 11px; margin: 8px 0;">
                        <tr>
                            <td style="text-align: left; font-weight: bold;">NO. ${reportNumber}</td>
                            <td style="text-align: right; font-weight: bold;">检测时间：${new Date().toLocaleString('zh-CN')}</td>
                        </tr>
                    </table>

                    <table style="width: 100%; font-size: 12px; margin-top: 12px;">
                        <tr>
                            <td style="text-align: left; padding: 2px 0;">篇名：${documentTitle}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 2px 0;">作者：${userInfo.username || '未知用户'}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 2px 0;">文件名：${documentTitle}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 2px 0;">全文检测结果 writerpro检测 https://writerpro.cn</td>
                        </tr>
                    </table>
                </div>

                <!-- 统计概览 -->
                <table style="width: 100%; margin: 20px 0;">
                    <tr>
                        <td style="width: 50%; vertical-align: top; padding-right: 15px;">
                            ${chartHTML}
                        </td>
                        <td style="width: 50%; vertical-align: top;">
                            <div style="background: #f8f9fa; padding: 12px; border-radius: 6px; margin-bottom: 12px; border: 1px solid #dee2e6;">
                                <div style="margin: 4px 0; font-size: 12px; font-weight: bold;">AI特征值：${charStats.aiFeatureValue}%</div>
                                <div style="margin: 4px 0; font-size: 12px; font-weight: bold;">AI特征字符数：${charStats.redChars}</div>
                                <div style="margin: 4px 0; font-size: 12px; font-weight: bold;">总字符数：${charStats.totalChars}</div>
                            </div>

                            <div style="margin: 12px 0; font-size: 11px;">
                                <div style="margin: 4px 0;">
                                    <span style="color: #dc3545; margin-right: 5px; font-size: 12px;">■</span>
                                    AI特征显著（计入AI特征字符数）红色
                                </div>
                                <div style="margin: 4px 0;">
                                    <span style="color: #fd7e14; margin-right: 5px; font-size: 12px;">■</span>
                                    AI特征疑似（未计入AI特征字符数）黄色
                                </div>
                                <div style="margin: 4px 0;">
                                    <span style="color: #333333; margin-right: 5px; font-size: 12px;">■</span>
                                    未标识部分 黑色
                                </div>
                            </div>

                            <div style="background: #fff3cd; padding: 8px; border-radius: 6px; border-left: 3px solid #fd7e14; font-size: 11px;">
                                <div style="margin: 3px 0;">红色AI特征值：${charStats.aiFeatureValue}%</div>
                                <div style="margin: 3px 0;">红色AI特征字符数：${charStats.redChars}</div>
                            </div>
                        </td>
                    </tr>
                </table>

                <!-- 章节统计表格 -->
                <div style="margin: 20px 0;">
                    <h2 style="font-size: 14px; margin-bottom: 10px; color: #333;">论文章节检测结果</h2>
                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd; font-size: 11px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 6px; font-weight: bold; color: #333;">序号</th>
                                <th style="border: 1px solid #ddd; padding: 6px; font-weight: bold; color: #333;">AI特征值</th>
                                <th style="border: 1px solid #ddd; padding: 6px; font-weight: bold; color: #333;">AI特征字符数/章节字符数</th>
                                <th style="border: 1px solid #ddd; padding: 6px; font-weight: bold; color: #333;">章节名称</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${chapterTableRows}
                        </tbody>
                    </table>
                </div>

                <!-- 详细章节内容 -->
                <div style="margin: 20px 0;">
                    ${chapterDetails}
                </div>

                <!-- 报告说明 -->
                <div style="margin-top: 20px; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 3px solid #007bff;">
                    <h3 style="color: #333; margin-bottom: 8px; font-size: 12px;">最后说明:</h3>
                    <ol style="padding-left: 15px; font-size: 11px; margin: 0;">
                        <li style="margin: 4px 0; line-height: 1.4;">支持中、英文内容检测；</li>
                        <li style="margin: 4px 0; line-height: 1.4;">AI特征值=AI特征字符数/总字符数；</li>
                        <li style="margin: 4px 0; line-height: 1.4;">红色代表AI特征显著部分，计入AI特征字符数；</li>
                        <li style="margin: 4px 0; line-height: 1.4;">黄色代表AI特征疑似部分，未计入AI特征字符数；</li>
                        <li style="margin: 4px 0; line-height: 1.4;">检测结果仅供参考，最终判定是否存在学术不端行为时，需结合人工复核、机构审查以及具体学术政策的综合应用进行审慎判断。</li>
                    </ol>
                </div>
            </div>
        `;
    }

    // 生成专门为canvas优化的HTML (简化样式，避免复杂CSS)
    createCanvasOptimizedHTML(data, userInfo, documentTitle) {
        const { paragraphs, statistics, language } = data;

        // 1. 基础数据计算
        const reportNumber = this.generateReportNumber();
        const charStats = this.calculateCharacterStats(paragraphs);
        const chapters = this.groupParagraphsIntoChapters(paragraphs, documentTitle);
        const chapterStats = chapters.map(chapter => this.calculateChapterStats(chapter));

        // 2. 生成简化的图表 (纯HTML+CSS，避免SVG)
        const chartHTML = this.generateSimpleHTMLChart(statistics);

        // 3. 生成章节统计表格
        const chapterTableRows = chapterStats.map((stats, index) => `
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 8px; text-align: center; border-right: 1px solid #ddd;">${index + 1}</td>
                <td style="padding: 8px; text-align: center; border-right: 1px solid #ddd;">${stats.aiFeatureValue}%</td>
                <td style="padding: 8px; text-align: center; border-right: 1px solid #ddd;">${stats.redChars} / ${stats.totalChars}</td>
                <td style="padding: 8px;">${stats.chapterName}</td>
            </tr>
        `).join('');

        // 4. 生成详细章节内容
        const chapterDetails = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `
                    <p style="margin: 8px 0; line-height: 1.8; color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'};">
                        ${para.text}
                        <span style="font-size: 11px; color: #666; margin-left: 8px; font-weight: normal;">[${probability}% | ${charCount}字]</span>
                    </p>
                `;
            }).join('');

            return `
                <div style="margin: 20px 0; page-break-inside: avoid;">
                    <h3 style="font-size: 16px; color: #333; margin: 15px 0 10px 0; padding-bottom: 5px; border-bottom: 1px solid #ddd;">
                        ${chapter.name}
                    </h3>
                    <div style="background: #f8f9fa; padding: 8px; margin: 10px 0; border-radius: 4px; font-size: 12px; font-weight: bold;">
                        AI特征值：${stats.aiFeatureValue}% AI特征字符数/章节字符数 ${stats.redChars} / ${stats.totalChars}
                    </div>
                    <div style="margin: 10px 0;">
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        // 5. 返回完整HTML (使用简单的表格布局)
        return `
            <div style="font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; color: #333; line-height: 1.6; background: white; padding: 20px;">

                <!-- 报告头部 -->
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 25px;">
                    <h1 style="margin: 0 0 15px 0; font-size: 22px; font-weight: bold; color: #333;">AIGC检测 · 全文报告单</h1>

                    <table style="width: 100%; font-size: 12px; margin: 10px 0;">
                        <tr>
                            <td style="text-align: left; font-weight: bold;">NO. ${reportNumber}</td>
                            <td style="text-align: right; font-weight: bold;">检测时间：${new Date().toLocaleString('zh-CN')}</td>
                        </tr>
                    </table>

                    <table style="width: 100%; font-size: 14px; margin-top: 15px;">
                        <tr>
                            <td style="text-align: left; padding: 3px 0;">篇名：${documentTitle}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 3px 0;">作者：${userInfo.username || '未知用户'}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 3px 0;">文件名：${documentTitle}</td>
                        </tr>
                        <tr>
                            <td style="text-align: left; padding: 3px 0;">全文检测结果 writerpro检测 https://writerpro.cn</td>
                        </tr>
                    </table>
                </div>

                <!-- 统计概览 -->
                <table style="width: 100%; margin: 25px 0;">
                    <tr>
                        <td style="width: 50%; vertical-align: top; padding-right: 20px;">
                            ${chartHTML}
                        </td>
                        <td style="width: 50%; vertical-align: top;">
                            <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-bottom: 15px; border: 1px solid #dee2e6;">
                                <div style="margin: 6px 0; font-size: 14px; font-weight: bold;">AI特征值：${charStats.aiFeatureValue}%</div>
                                <div style="margin: 6px 0; font-size: 14px; font-weight: bold;">AI特征字符数：${charStats.redChars}</div>
                                <div style="margin: 6px 0; font-size: 14px; font-weight: bold;">总字符数：${charStats.totalChars}</div>
                            </div>

                            <div style="margin: 15px 0; font-size: 12px;">
                                <div style="margin: 5px 0;">
                                    <span style="color: #dc3545; margin-right: 6px; font-size: 14px;">■</span>
                                    AI特征显著（计入AI特征字符数）红色
                                </div>
                                <div style="margin: 5px 0;">
                                    <span style="color: #fd7e14; margin-right: 6px; font-size: 14px;">■</span>
                                    AI特征疑似（未计入AI特征字符数）黄色
                                </div>
                                <div style="margin: 5px 0;">
                                    <span style="color: #333333; margin-right: 6px; font-size: 14px;">■</span>
                                    未标识部分 黑色
                                </div>
                            </div>

                            <div style="background: #fff3cd; padding: 10px; border-radius: 8px; border-left: 4px solid #fd7e14; font-size: 12px;">
                                <div style="margin: 4px 0;">红色AI特征值：${charStats.aiFeatureValue}%</div>
                                <div style="margin: 4px 0;">红色AI特征字符数：${charStats.redChars}</div>
                            </div>
                        </td>
                    </tr>
                </table>

                <!-- 章节统计表格 -->
                <div style="margin: 25px 0;">
                    <h2 style="font-size: 16px; margin-bottom: 12px; color: #333;">论文章节检测结果</h2>
                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd; font-size: 12px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #333;">序号</th>
                                <th style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #333;">AI特征值</th>
                                <th style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #333;">AI特征字符数/章节字符数</th>
                                <th style="border: 1px solid #ddd; padding: 8px; font-weight: bold; color: #333;">章节名称</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${chapterTableRows}
                        </tbody>
                    </table>
                </div>

                <!-- 详细章节内容 -->
                <div style="margin: 25px 0;">
                    ${chapterDetails}
                </div>

                <!-- 报告说明 -->
                <div style="margin-top: 25px; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                    <h3 style="color: #333; margin-bottom: 10px; font-size: 14px;">最后说明:</h3>
                    <ol style="padding-left: 18px; font-size: 12px; margin: 0;">
                        <li style="margin: 5px 0; line-height: 1.5;">支持中、英文内容检测；</li>
                        <li style="margin: 5px 0; line-height: 1.5;">AI特征值=AI特征字符数/总字符数；</li>
                        <li style="margin: 5px 0; line-height: 1.5;">红色代表AI特征显著部分，计入AI特征字符数；</li>
                        <li style="margin: 5px 0; line-height: 1.5;">黄色代表AI特征疑似部分，未计入AI特征字符数；</li>
                        <li style="margin: 5px 0; line-height: 1.5;">检测结果仅供参考，最终判定是否存在学术不端行为时，需结合人工复核、机构审查以及具体学术政策的综合应用进行审慎判断。</li>
                    </ol>
                </div>
            </div>
        `;
    }

    // 将Canvas内容添加到A4 PDF并分页
    async addCanvasContentToA4PDF(pdf, canvas, dimensions) {
        const {
            A4_WIDTH, A4_HEIGHT,
            CONTENT_WIDTH, CONTENT_HEIGHT,
            MARGIN_LEFT, MARGIN_TOP, MARGIN_BOTTOM,
            FOOTER_HEIGHT
        } = dimensions;

        // 计算缩放比例
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const scaleX = CONTENT_WIDTH / canvasWidth;
        const scaleY = scaleX; // 保持比例

        // 计算每页可容纳的canvas高度
        const pageContentHeight = CONTENT_HEIGHT;
        const canvasHeightPerPage = pageContentHeight / scaleY;

        console.log('📄 分页计算:', {
            canvasWidth, canvasHeight,
            scaleX, scaleY,
            pageContentHeight, canvasHeightPerPage
        });

        // 计算总页数
        const totalPages = Math.ceil(canvasHeight / canvasHeightPerPage);
        console.log(`📖 总页数: ${totalPages}`);

        // 分页处理
        for (let pageIndex = 0; pageIndex < totalPages; pageIndex++) {
            if (pageIndex > 0) {
                pdf.addPage();
            }

            // 计算当前页的canvas截取区域
            const startY = pageIndex * canvasHeightPerPage;
            const endY = Math.min(startY + canvasHeightPerPage, canvasHeight);
            const currentPageHeight = endY - startY;

            // 创建当前页的canvas
            const pageCanvas = document.createElement('canvas');
            pageCanvas.width = canvasWidth;
            pageCanvas.height = currentPageHeight;
            const pageCtx = pageCanvas.getContext('2d');

            // 绘制当前页内容
            pageCtx.drawImage(
                canvas,
                0, startY, canvasWidth, currentPageHeight,  // 源区域
                0, 0, canvasWidth, currentPageHeight        // 目标区域
            );

            // 添加到PDF
            const imgData = pageCanvas.toDataURL('image/jpeg', 0.95);
            const imgWidth = CONTENT_WIDTH;
            const imgHeight = currentPageHeight * scaleY;

            pdf.addImage(
                imgData, 'JPEG',
                MARGIN_LEFT, MARGIN_TOP,
                imgWidth, imgHeight
            );

            // 添加页码
            this.addPageNumber(pdf, pageIndex + 1, A4_WIDTH, A4_HEIGHT, MARGIN_BOTTOM);

            console.log(`✅ 第${pageIndex + 1}页处理完成`);
        }
    }

    // 添加页码到PDF
    addPageNumber(pdf, pageNumber, pageWidth, pageHeight, marginBottom) {
        // 设置页码字体
        pdf.setFontSize(12);
        pdf.setFont('helvetica', 'normal');

        // 页码文本
        const pageText = `-${pageNumber}-`;

        // 计算页码位置 (居中，底部)
        const textWidth = pdf.getTextWidth(pageText);
        const x = (pageWidth - textWidth) / 2;
        const y = pageHeight - marginBottom / 2;

        // 添加页码
        pdf.text(pageText, x, y);
    }

    // 创建Canvas饼图
    createCanvasPieChart(highPercent, mediumPercent, lowPercent) {
        const canvas = document.createElement('canvas');
        canvas.width = 240; // 2倍尺寸确保清晰
        canvas.height = 240;
        canvas.style.width = '120px';
        canvas.style.height = '120px';
        canvas.style.margin = '0 auto 15px auto';
        canvas.style.display = 'block';

        const ctx = canvas.getContext('2d');
        const centerX = 120;
        const centerY = 120;
        const radius = 100;

        // 计算角度
        const highAngle = (highPercent / 100) * 2 * Math.PI;
        const mediumAngle = (mediumPercent / 100) * 2 * Math.PI;
        const lowAngle = (lowPercent / 100) * 2 * Math.PI;

        let currentAngle = -Math.PI / 2; // 从顶部开始

        // 绘制高风险扇形（红色）
        if (highPercent > 0) {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + highAngle);
            ctx.closePath();
            ctx.fillStyle = '#dc3545';
            ctx.fill();
            currentAngle += highAngle;
        }

        // 绘制中风险扇形（橙色）
        if (mediumPercent > 0) {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + mediumAngle);
            ctx.closePath();
            ctx.fillStyle = '#fd7e14';
            ctx.fill();
            currentAngle += mediumAngle;
        }

        // 绘制低风险扇形（绿色）
        if (lowPercent > 0) {
            ctx.beginPath();
            ctx.moveTo(centerX, centerY);
            ctx.arc(centerX, centerY, radius, currentAngle, currentAngle + lowAngle);
            ctx.closePath();
            ctx.fillStyle = '#198754';
            ctx.fill();
        }

        return canvas;
    }

    // 生成简化的HTML图表 (使用Canvas饼图)
    generateSimpleHTMLChart(statistics) {
        const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = statistics;
        const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;

        if (total === 0) {
            return '<p style="text-align: center; color: #666; font-size: 14px;">暂无数据</p>';
        }

        // 计算百分比
        const highPercent = Math.round((highRiskParagraphs / total) * 100);
        const mediumPercent = Math.round((mediumRiskParagraphs / total) * 100);
        const lowPercent = 100 - highPercent - mediumPercent;

        // 创建容器并插入Canvas饼图
        const container = document.createElement('div');
        container.style.textAlign = 'center';

        // 创建Canvas饼图
        const canvas = this.createCanvasPieChart(highPercent, mediumPercent, lowPercent);
        container.appendChild(canvas);

        // 创建图例
        const legend = document.createElement('div');
        legend.style.cssText = 'font-size: 11px; text-align: left; max-width: 200px; margin: 0 auto;';
        legend.innerHTML = `
            <div style="margin: 4px 0;">
                <span style="color: #dc3545; margin-right: 5px;">■</span>
                高风险: ${highRiskParagraphs}段 (${highPercent}%)
            </div>
            <div style="margin: 4px 0;">
                <span style="color: #fd7e14; margin-right: 5px;">■</span>
                中风险: ${mediumRiskParagraphs}段 (${mediumPercent}%)
            </div>
            <div style="margin: 4px 0;">
                <span style="color: #198754; margin-right: 5px;">■</span>
                低风险: ${lowRiskParagraphs}段 (${lowPercent}%)
            </div>
        `;
        container.appendChild(legend);

        return container.outerHTML;
    }

    // 智能分页算法 - 计算章节高度并分页
    calculateSmartPageBreaks(chapters) {
        const A4_HEIGHT = 1123; // A4高度(px)
        const PAGE_MARGIN = 80; // 页边距
        const HEADER_HEIGHT = 200; // 第一页头部较高（包含统计信息）
        const FOOTER_HEIGHT = 80; // 页脚高度（为页码留足空间）
        const FIRST_PAGE_USABLE_HEIGHT = A4_HEIGHT - PAGE_MARGIN - HEADER_HEIGHT - FOOTER_HEIGHT;
        const OTHER_PAGE_USABLE_HEIGHT = A4_HEIGHT - PAGE_MARGIN - 40 - FOOTER_HEIGHT; // 其他页面头部较小

        console.log('📏 分页参数:', { A4_HEIGHT, FIRST_PAGE_USABLE_HEIGHT, OTHER_PAGE_USABLE_HEIGHT });

        let pages = [];
        let currentPage = [];
        let currentHeight = 0;
        let isFirstPage = true;

        for (let i = 0; i < chapters.length; i++) {
            const chapter = chapters[i];
            const chapterHeight = this.estimateChapterHeight(chapter);
            const pageLimit = isFirstPage ? FIRST_PAGE_USABLE_HEIGHT : OTHER_PAGE_USABLE_HEIGHT;

            console.log(`📖 章节${i + 1}: ${chapter.chapterName}, 预估高度: ${chapterHeight}px, 页面限制: ${pageLimit}px`);

            // 如果当前章节加入后会超出页面高度
            if (currentHeight + chapterHeight > pageLimit && currentPage.length > 0) {
                // 保存当前页，开始新页
                pages.push([...currentPage]);
                currentPage = [chapter];
                currentHeight = chapterHeight;
                isFirstPage = false; // 第一页之后的页面
                console.log(`📄 分页: 第${pages.length}页完成，开始第${pages.length + 1}页`);
            } else {
                // 加入当前页
                currentPage.push(chapter);
                currentHeight += chapterHeight;
            }
        }

        // 添加最后一页
        if (currentPage.length > 0) {
            pages.push(currentPage);
        }

        console.log(`📚 智能分页完成: 共${pages.length}页`);
        return pages;
    }

    // 估算章节高度
    estimateChapterHeight(chapter) {
        const TITLE_HEIGHT = 40; // 章节标题高度
        const PARAGRAPH_LINE_HEIGHT = 24; // 段落行高
        const PARAGRAPH_MARGIN = 12; // 段落间距
        const CHARS_PER_LINE = 45; // 每行字符数(估算)

        let totalHeight = TITLE_HEIGHT;

        // 计算所有段落高度
        for (const paragraph of chapter.paragraphs) {
            const textLength = paragraph.text.length;
            const lines = Math.ceil(textLength / CHARS_PER_LINE);
            const paragraphHeight = lines * PARAGRAPH_LINE_HEIGHT + PARAGRAPH_MARGIN;
            totalHeight += paragraphHeight;
        }

        return totalHeight;
    }

    // 基于您提供方案的前端图片转PDF实现 - 支持智能分页
    async generateSimplePDFWithCanvas(data, userInfo, documentTitle) {
        console.log('🎯 使用智能分页图片转PDF方案 (A4边距+页脚版本)');

        try {
            console.log('📊 输入数据检查:', {
                paragraphsCount: data.paragraphs?.length,
                userInfo: userInfo?.username,
                documentTitle
            });

            // 1. 准备数据
            const chapters = this.groupParagraphsIntoChapters(data.paragraphs, documentTitle);
            console.log('📖 章节分组完成:', chapters.length);

            // 2. 智能分页
            const pages = this.calculateSmartPageBreaks(chapters);
            console.log(`📚 分页结果: ${pages.length}页`);

            // 3. 为每页生成PDF
            console.log('🔄 开始生成多页PDF...');
            const pdf = await this.generateMultiPagePDF(pages, data, userInfo, documentTitle);
            console.log('✅ 多页PDF生成完成');

            // 4. 下载PDF
            const fileName = `AIGC检测报告_${documentTitle}_${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

            console.log('✅ 智能分页PDF生成成功');
            this.showNotification('PDF报告生成成功！', 'success');

        } catch (error) {
            console.error('❌ 智能分页PDF生成失败:', error);
            console.error('❌ 错误堆栈:', error.stack);
            this.showNotification('PDF生成失败: ' + error.message, 'error');
            throw error;
        }
    }

    // 生成多页PDF
    async generateMultiPagePDF(pages, data, userInfo, documentTitle) {
        // 检查依赖库
        let jsPDF;
        if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
            jsPDF = window.jspdf.jsPDF;
        } else if (typeof window.jsPDF !== 'undefined') {
            jsPDF = window.jsPDF;
        } else {
            throw new Error('jsPDF库未加载');
        }

        // A4尺寸 (点单位)：210mm × 297mm = 595.28 × 841.89 points
        const pdf = new jsPDF('p', 'pt', 'a4');
        const A4_WIDTH = 595.28;
        const A4_HEIGHT = 841.89;

        // 边距设置 (点单位)
        const MARGIN_LEFT = 0.7 * 28.35; // 0.7cm = 19.845pt
        const MARGIN_RIGHT = 0.7 * 28.35;
        const MARGIN_TOP = 1.2 * 28.35; // 1.2cm = 34.02pt
        const MARGIN_BOTTOM = 1.2 * 28.35;

        console.log('📐 PDF边距设置:', {
            MARGIN_LEFT: MARGIN_LEFT.toFixed(2),
            MARGIN_RIGHT: MARGIN_RIGHT.toFixed(2),
            MARGIN_TOP: MARGIN_TOP.toFixed(2),
            MARGIN_BOTTOM: MARGIN_BOTTOM.toFixed(2)
        });

        for (let pageIndex = 0; pageIndex < pages.length; pageIndex++) {
            const pageChapters = pages[pageIndex];
            console.log(`📄 生成第${pageIndex + 1}页，包含${pageChapters.length}个章节`);

            // 为当前页创建HTML容器
            const container = this.createPageContainer(pageChapters, data, userInfo, documentTitle, pageIndex + 1, pages.length);
            document.body.appendChild(container);

            // 如果是第一页，插入Canvas饼图
            if (pageIndex === 0) {
                const pieChartContainer = container.querySelector(`#pieChartContainer-1`);
                if (pieChartContainer) {
                    const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = data.statistics;
                    const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;

                    if (total > 0) {
                        const highPercent = Math.round((highRiskParagraphs / total) * 100);
                        const mediumPercent = Math.round((mediumRiskParagraphs / total) * 100);
                        const lowPercent = 100 - highPercent - mediumPercent;

                        const canvas = this.createCanvasPieChart(highPercent, mediumPercent, lowPercent);
                        canvas.style.width = '100px';
                        canvas.style.height = '100px';
                        pieChartContainer.appendChild(canvas);

                        // 添加图例
                        const legend = document.createElement('div');
                        legend.style.cssText = 'font-size: 10px; margin-top: 8px; text-align: left;';
                        legend.innerHTML = `
                            <div style="margin: 2px 0;"><span style="color: #dc3545;">■</span> 高风险: ${highPercent}%</div>
                            <div style="margin: 2px 0;"><span style="color: #fd7e14;">■</span> 中风险: ${mediumPercent}%</div>
                            <div style="margin: 2px 0;"><span style="color: #198754;">■</span> 低风险: ${lowPercent}%</div>
                        `;
                        pieChartContainer.appendChild(legend);
                    }
                }
            }

            try {
                // 等待渲染
                await this.waitForA4Render(container);

                // 截图
                const canvas = await html2canvas(container, {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true,
                    backgroundColor: '#ffffff',
                    letterRendering: true,
                    logging: false,
                    width: container.offsetWidth,
                    height: container.offsetHeight
                });

                // 添加到PDF
                if (pageIndex > 0) {
                    pdf.addPage();
                }

                const imgData = canvas.toDataURL('image/jpeg', 0.95);

                // 计算内容区域尺寸（考虑边距）
                const contentWidth = A4_WIDTH - MARGIN_LEFT - MARGIN_RIGHT;
                const contentHeight = A4_HEIGHT - MARGIN_TOP - MARGIN_BOTTOM;

                // 添加图片到PDF，考虑边距
                pdf.addImage(imgData, 'JPEG', MARGIN_LEFT, MARGIN_TOP, contentWidth, contentHeight);

                // 添加页脚（页码）
                pdf.setFontSize(12);
                pdf.setTextColor(51, 51, 51); // #333333
                const pageText = `-${pageIndex + 1}-`;
                const textWidth = pdf.getTextWidth(pageText);
                const footerX = (A4_WIDTH - textWidth) / 2; // 居中
                const footerY = A4_HEIGHT - MARGIN_BOTTOM / 2; // 底部边距的一半位置
                pdf.text(pageText, footerX, footerY);

                console.log(`✅ 第${pageIndex + 1}页生成完成`);

            } finally {
                // 清理容器
                document.body.removeChild(container);
            }
        }

        return pdf;
    }

    // 创建单页容器 (A4尺寸 + 页码)
    createPageContainer(chapters, data, userInfo, documentTitle, pageNumber, totalPages) {
        const container = document.createElement('div');

        // A4尺寸：210mm × 297mm，转换为像素 (96 DPI)
        // 210mm = 794px, 297mm = 1123px
        // 边距：左右0.7cm=26px，上下1.2cm=45px
        const A4_WIDTH_PX = 794;
        const A4_HEIGHT_PX = 1123;
        const MARGIN_LR_PX = 26;  // 0.7cm
        const MARGIN_TB_PX = 45;  // 1.2cm
        const FOOTER_HEIGHT_PX = 30; // 页脚高度

        container.style.cssText = `
            position: fixed;
            left: -2000px;
            top: 0;
            width: ${A4_WIDTH_PX}px;
            height: ${A4_HEIGHT_PX}px;
            padding: ${MARGIN_TB_PX}px ${MARGIN_LR_PX}px ${MARGIN_TB_PX + FOOTER_HEIGHT_PX}px ${MARGIN_LR_PX}px;
            box-sizing: border-box;
            background: white;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            z-index: -1000;
        `;

        // 生成页面内容（不包含页脚，页脚由PDF直接添加）
        const pageHTML = this.createPageHTML(chapters, data, userInfo, documentTitle, pageNumber, totalPages);

        container.innerHTML = pageHTML;

        return container;
    }

    // 创建页面HTML内容
    createPageHTML(chapters, data, userInfo, documentTitle, pageNumber, totalPages) {
        const reportNumber = this.generateReportNumber();
        const charStats = this.calculateCharacterStats(data.paragraphs);

        // 第一页包含报告头部和统计信息
        let headerHTML = '';
        if (pageNumber === 1) {
            headerHTML = `
                <!-- 报告头部 -->
                <div style="text-align: center; margin-bottom: 30px; border-bottom: 2px solid #007bff; padding-bottom: 20px;">
                    <h1 style="margin: 0; font-size: 24px; color: #007bff;">AI内容检测报告</h1>
                    <p style="margin: 10px 0 0 0; font-size: 14px; color: #666;">
                        报告编号: ${reportNumber} | 生成时间: ${new Date().toLocaleString('zh-CN')}
                    </p>
                </div>

                <!-- 文档信息 -->
                <div style="background: #f8f9fa; padding: 20px; margin-bottom: 20px; border-radius: 8px;">
                    <h2 style="margin: 0 0 15px 0; font-size: 18px; color: #333;">文档信息</h2>
                    <div style="display: flex; justify-content: space-between; align-items: flex-start;">
                        <div style="flex: 2;">
                            <div style="margin: 5px 0; font-size: 14px;"><strong>篇名：</strong>${documentTitle}</div>
                            <div style="margin: 5px 0; font-size: 14px;"><strong>作者：</strong>${userInfo.username || '未知用户'}</div>
                            <div style="margin: 5px 0; font-size: 14px;"><strong>检测平台：</strong>writerpro检测 https://writerpro.cn</div>
                        </div>
                        <div style="flex: 1;">
                            <div id="pieChartContainer-${pageNumber}" style="text-align: center;"></div>
                        </div>
                    </div>
                </div>

                <!-- 统计概览 -->
                <div style="background: #f8f9fa; padding: 15px; margin-bottom: 20px; border-radius: 8px;">
                    <h2 style="margin: 0 0 10px 0; font-size: 16px;">统计概览</h2>
                    <div style="display: flex; justify-content: space-around; font-size: 14px;">
                        <div><strong>AI特征值：</strong>${charStats.aiFeatureValue}%</div>
                        <div><strong>AI特征字符数：</strong>${charStats.redChars}</div>
                        <div><strong>总字符数：</strong>${charStats.totalChars}</div>
                    </div>
                </div>
            `;
        }

        // 生成章节内容
        const chaptersHTML = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `
                    <p style="margin: 8px 0; line-height: 1.8; color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'};">
                        ${para.text}
                        <span style="font-size: 11px; color: #666; margin-left: 8px; font-weight: normal;">[${probability}% | ${charCount}字]</span>
                    </p>
                `;
            }).join('');

            return `
                <div style="margin: 20px 0; page-break-inside: avoid;">
                    <h3 style="font-size: 16px; margin: 15px 0 10px 0; color: #333; border-left: 4px solid #007bff; padding-left: 10px;">
                        ${stats.chapterName}
                    </h3>
                    <div style="background: #fff3cd; padding: 8px; margin: 8px 0; border-radius: 4px; font-size: 12px;">
                        <strong>AI特征值: ${stats.aiFeatureValue}% | AI特征字符数/章节字符数: ${stats.redChars}/${stats.totalChars}</strong>
                    </div>
                    <div style="margin: 10px 0;">
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        return `
            <div style="position: relative; height: 100%; padding-bottom: 30px;">
                ${headerHTML}
                <div>
                    ${chaptersHTML}
                </div>
            </div>
        `;
    }

    // 创建A4尺寸的容器 (模拟您的A4页面设置)
    createA4Container(data, userInfo, documentTitle) {
        const container = document.createElement('div');

        // A4尺寸：210mm x 297mm，按96DPI计算
        const A4_WIDTH = 794;  // 210mm * 96dpi / 25.4
        const A4_HEIGHT = 1123; // 297mm * 96dpi / 25.4

        container.style.cssText = `
            position: fixed;
            left: -${A4_WIDTH + 100}px;
            top: 0;
            width: ${A4_WIDTH}px;
            min-height: ${A4_HEIGHT}px;
            background: white;
            font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif;
            padding: 40px;
            box-sizing: border-box;
            z-index: -1000;
            overflow: hidden;
        `;

        // 生成A4优化的HTML内容
        container.innerHTML = this.createA4OptimizedHTML(data, userInfo, documentTitle);

        return container;
    }

    // 等待A4容器渲染完成
    async waitForA4Render(container) {
        // 等待图片加载
        const images = container.querySelectorAll('img');
        const imagePromises = Array.from(images).map(img => {
            return new Promise((resolve) => {
                if (img.complete) {
                    resolve();
                } else {
                    img.onload = resolve;
                    img.onerror = resolve;
                    setTimeout(resolve, 3000); // 超时保护
                }
            });
        });

        await Promise.all(imagePromises);

        // 等待字体加载
        if (document.fonts && document.fonts.ready) {
            await document.fonts.ready;
        }

        // 额外等待确保渲染完成
        await new Promise(resolve => setTimeout(resolve, 1500));

        console.log('✅ A4容器渲染完成');
    }

    // 图片转PDF (模拟您的PDFBox convertForA4方法)
    async convertImageToPDF(canvas, documentTitle) {
        // 检查jsPDF是否可用
        let jsPDF;
        if (typeof window.jspdf !== 'undefined' && window.jspdf.jsPDF) {
            jsPDF = window.jspdf.jsPDF;
        } else if (typeof window.jsPDF !== 'undefined') {
            jsPDF = window.jsPDF;
        } else {
            // 如果jsPDF不可用，下载图片
            console.log('📸 jsPDF不可用，下载高清图片');
            const imgData = canvas.toDataURL('image/png', 1.0);
            const link = document.createElement('a');
            link.download = `AIGC检测报告_${documentTitle}_${new Date().toISOString().split('T')[0]}.png`;
            link.href = imgData;
            link.click();
            return;
        }

        // A4尺寸 (mm)
        const A4_WIDTH_MM = 210;
        const A4_HEIGHT_MM = 297;

        // 原始图片尺寸
        const imageWidth = canvas.width;
        const imageHeight = canvas.height;

        // 创建PDF文档
        const pdf = new jsPDF('p', 'mm', 'a4');

        // 计算缩放比例 (等比缩放，模拟您的scale计算)
        const scaleX = A4_WIDTH_MM / (imageWidth * 0.264583); // px to mm
        const scaleY = A4_HEIGHT_MM / (imageHeight * 0.264583);
        const scale = Math.min(scaleX, scaleY);

        // 计算绘制尺寸
        const drawWidth = imageWidth * 0.264583 * scale;
        const drawHeight = imageHeight * 0.264583 * scale;

        // 居中坐标 (模拟您的居中计算)
        const x = (A4_WIDTH_MM - drawWidth) / 2;
        const y = (A4_HEIGHT_MM - drawHeight) / 2;

        // 将图片添加到PDF
        const imgData = canvas.toDataURL('image/jpeg', 0.95);
        pdf.addImage(imgData, 'JPEG', x, y, drawWidth, drawHeight);

        // 下载PDF
        const fileName = `AIGC检测报告_${documentTitle}_${new Date().toISOString().split('T')[0]}.pdf`;
        pdf.save(fileName);

        console.log('✅ PDF生成完成，文件名:', fileName);
    }

    // 创建简化的报告HTML
    createSimpleReportHTML(data, userInfo, documentTitle) {
        const { paragraphs, statistics } = data;
        const reportNumber = this.generateReportNumber();
        const charStats = this.calculateCharacterStats(paragraphs);

        // 生成段落内容
        const paragraphsHTML = paragraphs.map((para, index) => {
            const probability = Math.round((para.ai_probability || 0) * 100);
            const charCount = para.text.length;
            let color = '#333333';
            let label = '低风险';

            if (probability >= 80) {
                color = '#dc3545';
                label = '高风险';
            } else if (probability >= 50) {
                color = '#fd7e14';
                label = '中风险';
            }

            return `
                <div style="margin: 15px 0; padding: 10px; border-left: 4px solid ${color}; background: ${color}15;">
                    <div style="color: ${color}; font-weight: bold; margin-bottom: 5px;">
                        段落${index + 1} - ${label} (AI概率: ${probability}% | ${charCount}字)
                    </div>
                    <div style="color: #333; line-height: 1.6;">${para.text}</div>
                </div>
            `;
        }).join('');

        return `
            <div style="font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; color: #333; line-height: 1.6;">
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 15px; margin-bottom: 20px;">
                    <h1 style="margin: 0 0 10px 0; font-size: 20px;">AIGC检测 · 全文报告单</h1>
                    <div style="font-size: 12px;">
                        <div>NO. ${reportNumber} | 检测时间：${new Date().toLocaleString('zh-CN')}</div>
                        <div style="margin-top: 8px;">
                            <div>篇名：${documentTitle}</div>
                            <div>作者：${userInfo.username || '未知用户'}</div>
                            <div>全文检测结果 writerpro检测 https://writerpro.cn</div>
                        </div>
                    </div>
                </div>

                <div style="background: #f8f9fa; padding: 15px; margin: 15px 0; border-radius: 8px;">
                    <h2 style="margin: 0 0 10px 0; font-size: 16px;">统计概览</h2>
                    <div style="display: flex; justify-content: space-around; font-size: 14px;">
                        <div><strong>AI特征值：</strong>${charStats.aiFeatureValue}%</div>
                        <div><strong>AI特征字符数：</strong>${charStats.redChars}</div>
                        <div><strong>总字符数：</strong>${charStats.totalChars}</div>
                    </div>
                </div>

                <div>
                    <h2 style="font-size: 16px; margin: 15px 0 10px 0;">详细分析</h2>
                    ${paragraphsHTML}
                </div>

                <div style="margin-top: 20px; padding: 10px; background: #f8f9fa; border-radius: 5px; font-size: 12px;">
                    <strong>说明：</strong>红色=AI特征显著(≥80%)，黄色=AI特征疑似(50-80%)，黑色=未标识(<50%)
                </div>
            </div>
        `;
    }

    // 生成A4优化的HTML (模拟您的FreeMarker模板)
    createA4OptimizedHTML(data, userInfo, documentTitle) {
        const { paragraphs, statistics } = data;
        const reportNumber = this.generateReportNumber();
        const charStats = this.calculateCharacterStats(paragraphs);
        const chapters = this.groupParagraphsIntoChapters(paragraphs, documentTitle);
        const chapterStats = chapters.map(chapter => this.calculateChapterStats(chapter));

        // 生成章节统计表格
        const chapterTableRows = chapterStats.map((stats, index) => `
            <tr style="border-bottom: 1px solid #ddd;">
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${index + 1}</td>
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${stats.aiFeatureValue}%</td>
                <td style="padding: 6px; text-align: center; border-right: 1px solid #ddd; font-size: 11px;">${stats.redChars}/${stats.totalChars}</td>
                <td style="padding: 6px; font-size: 11px;">${stats.chapterName}</td>
            </tr>
        `).join('');

        // 生成详细章节内容
        const chapterDetails = chapters.map(chapter => {
            const stats = this.calculateChapterStats(chapter);
            const chapterContent = chapter.paragraphs.map(para => {
                const colorStyle = this.getColorStyle(para.ai_probability);
                const charCount = para.text.length;
                const probability = Math.round((para.ai_probability || 0) * 100);

                return `
                    <p style="margin: 6px 0; line-height: 1.6; color: ${colorStyle.color}; font-weight: ${colorStyle.color !== '#333333' ? 'bold' : 'normal'}; font-size: 12px;">
                        ${para.text}
                        <span style="font-size: 10px; color: #666; margin-left: 6px; font-weight: normal;">[${probability}% | ${charCount}字]</span>
                    </p>
                `;
            }).join('');

            return `
                <div style="margin: 15px 0; page-break-inside: avoid;">
                    <h3 style="font-size: 14px; color: #333; margin: 10px 0 6px 0; padding-bottom: 3px; border-bottom: 1px solid #ddd;">
                        ${chapter.name}
                    </h3>
                    <div style="background: #f8f9fa; padding: 6px; margin: 6px 0; border-radius: 3px; font-size: 10px; font-weight: bold;">
                        AI特征值：${stats.aiFeatureValue}% AI特征字符数/章节字符数 ${stats.redChars}/${stats.totalChars}
                    </div>
                    <div style="margin: 6px 0;">
                        ${chapterContent}
                    </div>
                </div>
            `;
        }).join('');

        // 生成简化的图表
        const { highRiskParagraphs, mediumRiskParagraphs, lowRiskParagraphs } = statistics;
        const total = highRiskParagraphs + mediumRiskParagraphs + lowRiskParagraphs;
        const highPercent = total > 0 ? Math.round((highRiskParagraphs / total) * 100) : 0;
        const mediumPercent = total > 0 ? Math.round((mediumRiskParagraphs / total) * 100) : 0;
        const lowPercent = 100 - highPercent - mediumPercent;

        // A4优化的HTML (紧凑布局)
        return `
            <div style="font-family: 'Microsoft YaHei', 'SimHei', Arial, sans-serif; color: #333; line-height: 1.4; font-size: 12px;">

                <!-- 报告头部 -->
                <div style="text-align: center; border-bottom: 2px solid #333; padding-bottom: 12px; margin-bottom: 15px;">
                    <h1 style="margin: 0 0 10px 0; font-size: 18px; font-weight: bold; color: #333;">AIGC检测 · 全文报告单</h1>

                    <div style="display: flex; justify-content: space-between; margin: 8px 0; font-size: 10px; font-weight: bold;">
                        <div>NO. ${reportNumber}</div>
                        <div>检测时间：${new Date().toLocaleString('zh-CN')}</div>
                    </div>

                    <div style="text-align: left; margin-top: 10px; font-size: 11px; line-height: 1.3;">
                        <div style="margin: 2px 0;">篇名：${documentTitle}</div>
                        <div style="margin: 2px 0;">作者：${userInfo.username || '未知用户'}</div>
                        <div style="margin: 2px 0;">文件名：${documentTitle}</div>
                        <div style="margin: 2px 0;">全文检测结果 writerpro检测 https://writerpro.cn</div>
                    </div>
                </div>

                <!-- 统计概览 (紧凑布局) -->
                <div style="display: flex; gap: 15px; margin: 15px 0; align-items: flex-start;">
                    <div style="flex: 1;">
                        <!-- Canvas饼图 -->
                        <div style="text-align: center; margin-bottom: 10px;">
                            <div id="pieChartContainer" style="margin: 0 auto 8px auto;"></div>
                            <div style="font-size: 9px; text-align: left;">
                                <div style="margin: 2px 0;"><span style="color: #dc3545;">■</span> 高风险: ${highRiskParagraphs}段 (${highPercent}%)</div>
                                <div style="margin: 2px 0;"><span style="color: #fd7e14;">■</span> 中风险: ${mediumRiskParagraphs}段 (${mediumPercent}%)</div>
                                <div style="margin: 2px 0;"><span style="color: #198754;">■</span> 低风险: ${lowRiskParagraphs}段 (${lowPercent}%)</div>
                            </div>
                        </div>
                    </div>
                    <div style="flex: 2;">
                        <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; margin-bottom: 8px; border: 1px solid #dee2e6;">
                            <div style="margin: 3px 0; font-size: 11px; font-weight: bold;">AI特征值：${charStats.aiFeatureValue}%</div>
                            <div style="margin: 3px 0; font-size: 11px; font-weight: bold;">AI特征字符数：${charStats.redChars}</div>
                            <div style="margin: 3px 0; font-size: 11px; font-weight: bold;">总字符数：${charStats.totalChars}</div>
                        </div>

                        <div style="margin: 8px 0; font-size: 9px;">
                            <div style="margin: 2px 0;"><span style="color: #dc3545;">■</span> AI特征显著（计入AI特征字符数）红色</div>
                            <div style="margin: 2px 0;"><span style="color: #fd7e14;">■</span> AI特征疑似（未计入AI特征字符数）黄色</div>
                            <div style="margin: 2px 0;"><span style="color: #333333;">■</span> 未标识部分 黑色</div>
                        </div>

                        <div style="background: #fff3cd; padding: 6px; border-radius: 3px; border-left: 3px solid #fd7e14; font-size: 9px;">
                            <div style="margin: 2px 0;">红色AI特征值：${charStats.aiFeatureValue}%</div>
                            <div style="margin: 2px 0;">红色AI特征字符数：${charStats.redChars}</div>
                        </div>
                    </div>
                </div>

                <!-- 章节统计表格 -->
                <div style="margin: 15px 0;">
                    <h2 style="font-size: 13px; margin-bottom: 8px; color: #333;">论文章节检测结果</h2>
                    <table style="width: 100%; border-collapse: collapse; border: 1px solid #ddd; font-size: 10px;">
                        <thead>
                            <tr style="background: #f8f9fa;">
                                <th style="border: 1px solid #ddd; padding: 4px; font-weight: bold; color: #333; width: 8%;">序号</th>
                                <th style="border: 1px solid #ddd; padding: 4px; font-weight: bold; color: #333; width: 15%;">AI特征值</th>
                                <th style="border: 1px solid #ddd; padding: 4px; font-weight: bold; color: #333; width: 25%;">AI特征字符数/章节字符数</th>
                                <th style="border: 1px solid #ddd; padding: 4px; font-weight: bold; color: #333;">章节名称</th>
                            </tr>
                        </thead>
                        <tbody>
                            ${chapterTableRows}
                        </tbody>
                    </table>
                </div>

                <!-- 详细章节内容 -->
                <div style="margin: 15px 0;">
                    ${chapterDetails}
                </div>

                <!-- 报告说明 -->
                <div style="margin-top: 15px; padding: 8px; background: #f8f9fa; border-radius: 5px; border-left: 3px solid #007bff;">
                    <h3 style="color: #333; margin-bottom: 6px; font-size: 11px;">最后说明:</h3>
                    <ol style="padding-left: 15px; font-size: 9px; margin: 0; line-height: 1.3;">
                        <li style="margin: 2px 0;">支持中、英文内容检测；</li>
                        <li style="margin: 2px 0;">AI特征值=AI特征字符数/总字符数；</li>
                        <li style="margin: 2px 0;">红色代表AI特征显著部分，计入AI特征字符数；</li>
                        <li style="margin: 2px 0;">黄色代表AI特征疑似部分，未计入AI特征字符数；</li>
                        <li style="margin: 2px 0;">检测结果仅供参考，最终判定是否存在学术不端行为时，需结合人工复核、机构审查以及具体学术政策的综合应用进行审慎判断。</li>
                    </ol>
                </div>
            </div>
        `;
    }
}

// 页面加载完成后初始化，确保所有脚本都已加载
document.addEventListener('DOMContentLoaded', () => {
    // 延迟初始化，确保AuthManager完全准备就绪
    setTimeout(() => {
        console.log('开始初始化AIGC检测页面...');
        new AigcDetection();
    }, 100);
});
